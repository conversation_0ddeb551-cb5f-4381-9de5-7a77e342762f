#ifndef AUTO_RECOVERY_HPP
#define AUTO_RECOVERY_HPP

#include <rclcpp/rclcpp.hpp>
#include <chrono>
#include <vector>
#include <memory>

namespace auto_recovery {

/**
 * @brief 定位系统状态枚举
 */
enum class LocalizationState {
    ICP_TRACKING,           // ICP位姿跟踪模式
    FAILURE_DETECTED,       // 检测到跟踪失败
    WIFI_RECOVERY,          // WiFi全局定位恢复中
    RECOVERY_VALIDATION,    // 恢复结果验证
    SMOOTH_TRANSITION       // 平滑过渡到ICP跟踪
};

/**
 * @brief 跟踪质量结构体
 */
struct TrackingQuality {
    int numIcpPoints = 0;
    double weightSumTurkey = 0.0;
    double translationNorm = 0.0;
    double icpScore = 0.0;
    rclcpp::Time timestamp;
    
    TrackingQuality() : timestamp(rclcpp::Time(0)) {}
};

/**
 * @brief 失败检测器类
 */
class FailureDetector {
public:
    struct Config {
        // 即时失败检测阈值
        int min_icp_points = 5;
        double min_weight_sum = 1e-6;
        double max_translation_norm = 5.0;
        
        // 质量退化检测阈值
        double min_point_ratio = 0.05;
        double min_weight_ratio = 0.1;
        double min_icp_score = 0.3;
        
        // 连续失败计数阈值
        int max_critical_failures = 2;
        int max_quality_failures = 5;
        int max_consecutive_poor = 10;
    };

private:
    Config config_;
    int critical_failure_count_ = 0;
    int quality_failure_count_ = 0;
    int consecutive_poor_frames_ = 0;
    std::vector<TrackingQuality> quality_history_;
    size_t max_history_size_ = 20;
    
public:
    explicit FailureDetector(const Config& config) : config_(config) {}
    
    /**
     * @brief 更新跟踪质量并检测失败
     */
    void updateQuality(const TrackingQuality& quality);
    
    /**
     * @brief 检查是否应该触发恢复
     */
    bool shouldTriggerRecovery() const;
    
    /**
     * @brief 重置失败计数器
     */
    void reset();
    
    /**
     * @brief 获取当前失败统计
     */
    void getFailureStats(int& critical, int& quality, int& consecutive) const;

private:
    bool isCriticalFailure(const TrackingQuality& quality) const;
    bool isQualityDegraded(const TrackingQuality& quality) const;
};

/**
 * @brief 自动恢复状态机类
 */
class AutoRecoveryStateMachine {
public:
    struct Config {
        int max_recovery_attempts = 3;
        double recovery_timeout = 30.0;
        double min_recovery_score = 0.5;
        int transition_frames = 5;
        double transition_weight_decay = 0.8;
        double recovery_error_up_thred = 2.0;
        double recovery_error_low_thred = 1.2;
        double threshold_tightening_rate = 0.95;
        bool enable_debug_output = true;
    };

private:
    Config config_;
    LocalizationState current_state_;
    std::unique_ptr<FailureDetector> failure_detector_;
    
    // 恢复流程状态
    int recovery_attempts_ = 0;
    rclcpp::Time recovery_start_time_;
    rclcpp::Time last_state_change_;
    
    // 过渡状态
    int transition_frame_count_ = 0;
    bool odom_fusion_backup_state_ = false;
    
    // 日志记录
    rclcpp::Logger logger_;
    
public:
    explicit AutoRecoveryStateMachine(const Config& config, 
                                    const FailureDetector::Config& detector_config,
                                    rclcpp::Logger logger);
    
    /**
     * @brief 更新状态机
     */
    void update(const TrackingQuality& quality, rclcpp::Time current_time);
    
    /**
     * @brief 获取当前状态
     */
    LocalizationState getCurrentState() const { return current_state_; }
    
    /**
     * @brief 检查是否需要触发WiFi恢复
     */
    bool shouldTriggerWiFiRecovery() const;
    
    /**
     * @brief 检查是否需要验证恢复结果
     */
    bool shouldValidateRecovery() const;
    
    /**
     * @brief 检查是否需要平滑过渡
     */
    bool shouldPerformTransition() const;
    
    /**
     * @brief 通知WiFi恢复完成
     */
    void notifyWiFiRecoveryComplete(double recovery_score);
    
    /**
     * @brief 通知过渡完成
     */
    void notifyTransitionComplete();
    
    /**
     * @brief 重置状态机
     */
    void reset();
    
    /**
     * @brief 获取恢复统计信息
     */
    void getRecoveryStats(int& attempts, double& success_rate) const;

private:
    void changeState(LocalizationState new_state, rclcpp::Time current_time);
    bool isRecoveryTimeout(rclcpp::Time current_time) const;
    void logStateChange(LocalizationState old_state, LocalizationState new_state) const;
};

/**
 * @brief 自动恢复管理器类 - 主要接口
 */
class AutoRecoveryManager {
public:
    struct Config {
        bool enable_auto_recovery = false;
        FailureDetector::Config detector_config;
        AutoRecoveryStateMachine::Config state_machine_config;
    };

private:
    Config config_;
    std::unique_ptr<AutoRecoveryStateMachine> state_machine_;
    rclcpp::Logger logger_;
    bool enabled_ = false;
    
public:
    explicit AutoRecoveryManager(const Config& config, rclcpp::Logger logger);
    
    /**
     * @brief 启用/禁用自动恢复
     */
    void setEnabled(bool enabled) { enabled_ = enabled; }
    bool isEnabled() const { return enabled_; }
    
    /**
     * @brief 更新跟踪质量并处理恢复逻辑
     */
    void updateTracking(int numIcpPoints, double weightSumTurkey, 
                       double translationNorm, double icpScore, 
                       rclcpp::Time timestamp);
    
    /**
     * @brief 获取当前状态
     */
    LocalizationState getCurrentState() const;
    
    /**
     * @brief 检查各种恢复动作
     */
    bool shouldTriggerWiFiRecovery() const;
    bool shouldValidateRecovery() const;
    bool shouldPerformTransition() const;
    
    /**
     * @brief 通知恢复事件
     */
    void notifyWiFiRecoveryComplete(double recovery_score);
    void notifyTransitionComplete();
    
    /**
     * @brief 重置管理器
     */
    void reset();
};

} // namespace auto_recovery

#endif // AUTO_RECOVERY_HPP

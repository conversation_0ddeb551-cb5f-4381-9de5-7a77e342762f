#!/usr/bin/env python3
"""
AGLoc自动位姿恢复功能测试脚本
测试自动恢复功能的基本工作流程和参数配置
"""

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import PointCloud2
from geometry_msgs.msg import PoseStamped
import time
import yaml
import os

class AutoRecoveryTester(Node):
    def __init__(self):
        super().__init__('auto_recovery_tester')
        
        # 订阅机器人位姿
        self.pose_sub = self.create_subscription(
            PoseStamped,
            '/robot_pose',
            self.pose_callback,
            10
        )
        
        # 订阅点云数据
        self.cloud_sub = self.create_subscription(
            PointCloud2,
            '/hesai/pandar',
            self.cloud_callback,
            10
        )
        
        # 测试状态
        self.pose_count = 0
        self.cloud_count = 0
        self.last_pose_time = None
        self.pose_positions = []
        
        self.get_logger().info("🧪 自动恢复功能测试器已启动")
        
        # 检查参数配置
        self.check_parameters()
        
    def check_parameters(self):
        """检查自动恢复相关参数是否正确配置"""
        try:
            # 读取params.yaml文件
            config_path = os.path.join(
                os.path.dirname(__file__), 
                '..', 'config', 'params.yaml'
            )
            
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    params = yaml.safe_load(f)
                
                # 检查自动恢复参数
                if 'auto_recovery' in params.get('/**', {}).get('ros__parameters', {}):
                    auto_recovery_params = params['/**']['ros__parameters']['auto_recovery']
                    self.get_logger().info("✅ 发现自动恢复参数配置:")
                    for key, value in auto_recovery_params.items():
                        self.get_logger().info(f"   {key}: {value}")
                else:
                    self.get_logger().warn("⚠️  未找到自动恢复参数配置")
                    
                # 检查总开关
                enable_auto_recovery = params.get('/**', {}).get('ros__parameters', {}).get('enable_auto_recovery', False)
                if enable_auto_recovery:
                    self.get_logger().info("✅ 自动恢复功能已启用")
                else:
                    self.get_logger().warn("⚠️  自动恢复功能未启用 (enable_auto_recovery: false)")
                    
            else:
                self.get_logger().error(f"❌ 未找到参数文件: {config_path}")
                
        except Exception as e:
            self.get_logger().error(f"❌ 读取参数文件失败: {e}")
    
    def pose_callback(self, msg):
        """位姿回调函数"""
        self.pose_count += 1
        current_time = time.time()
        
        # 记录位置
        position = [msg.pose.position.x, msg.pose.position.y]
        self.pose_positions.append(position)
        
        # 检查位姿更新频率
        if self.last_pose_time is not None:
            dt = current_time - self.last_pose_time
            if dt > 0.5:  # 超过500ms没有更新
                self.get_logger().warn(f"⚠️  位姿更新频率较低: {dt:.2f}s")
        
        self.last_pose_time = current_time
        
        # 每10个位姿打印一次状态
        if self.pose_count % 10 == 0:
            self.get_logger().info(f"📍 收到位姿 #{self.pose_count}: [{position[0]:.2f}, {position[1]:.2f}]")
            
        # 检查位姿跳跃
        if len(self.pose_positions) >= 2:
            last_pos = self.pose_positions[-2]
            curr_pos = self.pose_positions[-1]
            distance = ((curr_pos[0] - last_pos[0])**2 + (curr_pos[1] - last_pos[1])**2)**0.5
            
            if distance > 2.0:  # 位姿跳跃超过2米
                self.get_logger().warn(f"🚨 检测到位姿跳跃: {distance:.2f}m")
                self.get_logger().info("   这可能表明自动恢复功能正在工作")
    
    def cloud_callback(self, msg):
        """点云回调函数"""
        self.cloud_count += 1
        
        # 每50帧打印一次状态
        if self.cloud_count % 50 == 0:
            self.get_logger().info(f"☁️  收到点云 #{self.cloud_count}")
    
    def print_summary(self):
        """打印测试总结"""
        self.get_logger().info("=" * 60)
        self.get_logger().info("🧪 自动恢复功能测试总结")
        self.get_logger().info("=" * 60)
        self.get_logger().info(f"📊 统计信息:")
        self.get_logger().info(f"   - 收到位姿消息: {self.pose_count}")
        self.get_logger().info(f"   - 收到点云消息: {self.cloud_count}")
        
        if len(self.pose_positions) >= 2:
            # 计算总移动距离
            total_distance = 0
            for i in range(1, len(self.pose_positions)):
                last_pos = self.pose_positions[i-1]
                curr_pos = self.pose_positions[i]
                distance = ((curr_pos[0] - last_pos[0])**2 + (curr_pos[1] - last_pos[1])**2)**0.5
                total_distance += distance
            
            self.get_logger().info(f"   - 总移动距离: {total_distance:.2f}m")
            self.get_logger().info(f"   - 平均移动速度: {total_distance/max(1, self.pose_count-1):.3f}m/frame")
        
        self.get_logger().info("=" * 60)

def main():
    rclpy.init()
    
    tester = AutoRecoveryTester()
    
    try:
        # 运行测试
        tester.get_logger().info("🚀 开始自动恢复功能测试...")
        tester.get_logger().info("   请启动AGLoc节点并播放数据包")
        tester.get_logger().info("   按Ctrl+C停止测试")
        
        rclpy.spin(tester)
        
    except KeyboardInterrupt:
        tester.get_logger().info("🛑 测试被用户中断")
    finally:
        tester.print_summary()
        tester.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()

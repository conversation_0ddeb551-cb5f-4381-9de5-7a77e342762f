#!/usr/bin/env python3
import matplotlib
matplotlib.use('Agg') # Use non-interactive backend
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import os
from datetime import datetime
import math
from pathlib import Path

# Placeholder for calculate_precise_distance.
# Ensure this can be imported from wifi_loc.utils.util in the final setup.
def calculate_precise_distance(lon1, lat1, lon2, lat2):
    R = 6371e3  # Earth radius in meters
    phi1 = math.radians(lat1)
    phi2 = math.radians(lat2)
    delta_phi = math.radians(lat2 - lat1)
    delta_lambda = math.radians(lon2 - lon1)
    a = math.sin(delta_phi / 2) * math.sin(delta_phi / 2) + \
        math.cos(phi1) * math.cos(phi2) * \
        math.sin(delta_lambda / 2) * math.sin(delta_lambda / 2)
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    distance = R * c
    return distance

class LocalizationPlotter:
    def __init__(self, logger, polygon_edges, ground_truth_data=None, default_bag_name='unknown_bag'):
        self.logger = logger
        self.polygon_edges = polygon_edges # Expected format: {'floor_str': [shapely.geometry.LineString, ...]}
        self.ground_truth_data = ground_truth_data if ground_truth_data is not None else {}
        self.default_bag_name = default_bag_name
        self.plot_save_dir = "/home/<USER>/AGLoc_ws/src/wifi_loc/figs_wifi_loc"  # Updated save directory
        # Directory creation is now handled in _finalize_and_save_plot
        # Initialize plot counter or any other persistent state if needed for multiple plots.

    def plot_localization(self,
                          wifi_main_location,  # Main WiFi point (e.g., 'result') for red star, AP/GT lines
                          detected_ap_positions,
                          detected_ap_rssis,
                          smallest_room_bounds=None,
                          agloc_pose_geographic=None, # Geographic coords of AGLoc pose
                          wifi_for_agloc_line=None, # Specific WiFi point (e.g., 'mid_point') for AGLoc line
                          current_bag_name=None,
                          plot_timestamp_str=None):
        """
        Generates and saves the localization visualization plot.
        wifi_main_location: [lon, lat] for the main WiFi position.
        agloc_pose_geographic: [lon, lat] for AGLoc pose.
        wifi_for_agloc_line: [lon, lat] for connecting to AGLoc, defaults to wifi_main_location if None.
        """
        fig, ax = plt.subplots(figsize=(12, 10))
        position_info = [] # For text below plot

        current_bag_name = current_bag_name or self.default_bag_name
        timestamp_for_file = plot_timestamp_str if plot_timestamp_str else datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]

        try:
            # 1. Plot map boundaries (assuming floor '2' for now, as in original)
            self._plot_map_boundaries(ax, floor_str='2')

            # 2. Plot smallest room boundary
            if smallest_room_bounds:
                self._plot_room_boundary(ax, smallest_room_bounds)

            # 3. Plot main WiFi location (red star)
            self._plot_wifi_main_location(ax, wifi_main_location, position_info)

            # 4. Plot detected APs and lines to main WiFi location
            self._plot_ap_info(ax, detected_ap_positions, detected_ap_rssis, wifi_main_location)

            # 5. Plot Ground Truth location and line to main WiFi location
            if current_bag_name in self.ground_truth_data:
                self._plot_ground_truth_location(ax, wifi_main_location, current_bag_name, position_info)

            # 6. Plot AGLoc pose and lines
            if agloc_pose_geographic:
                point_for_agloc_conn = wifi_for_agloc_line if wifi_for_agloc_line is not None else wifi_main_location
                self._plot_agloc_pose(ax, agloc_pose_geographic, point_for_agloc_conn, current_bag_name, position_info)

            # 7. Add legend, title, and save
            self._finalize_and_save_plot(fig, ax, position_info, current_bag_name, timestamp_for_file)

        except Exception as e:
            self.logger.error(f"Error during plotting: {e}")
        finally:
            plt.close(fig) # Ensure figure is closed to free memory

    def _plot_map_boundaries(self, ax, floor_str='2'):
        if floor_str in self.polygon_edges:
            colors = plt.rcParams['axes.prop_cycle'].by_key()['color']
            color_index = 0
            for edge in self.polygon_edges[floor_str]:
                edge_x, edge_y = edge.xy
                ax.plot(edge_x, edge_y, linewidth=1, linestyle='solid', color=colors[color_index % len(colors)])
                color_index += 1
        else:
            self.logger.warn(f"No boundary data for floor {floor_str}.")

    def _plot_room_boundary(self, ax, smallest_room_bounds):
        minx, miny, maxx, maxy = smallest_room_bounds
        width = maxx - minx
        height = maxy - miny
        room_rect = patches.Rectangle(
            (minx, miny), width, height,
            linewidth=1.5, edgecolor='orange', facecolor='none',
            label='Room Boundary', linestyle='--'
        )
        ax.add_patch(room_rect)

    def _plot_wifi_main_location(self, ax, wifi_location, position_info):
        ax.scatter(wifi_location[0], wifi_location[1], color='red', s=100, marker='*', label='WiFi Position', zorder=5)
        position_info.append(f'WiFi Position: ({wifi_location[0]:.6f}, {wifi_location[1]:.6f})')

    def _plot_ap_info(self, ax, ap_positions, ap_rssis, wifi_main_location):
        # Ensure labels are added only once for APs if multiple exist
        ap_label_added = False
        for i, (pos, rssi) in enumerate(zip(ap_positions, ap_rssis)):
            label = 'Detected AP' if not ap_label_added else None
            ax.scatter(pos[0], pos[1], color='blue', s=60, alpha=0.7, label=label, zorder=4)
            ap_label_added = True
            ax.annotate(f'{rssi:.1f}dBm', (pos[0], pos[1]), textcoords="offset points",
                        xytext=(0, -15), ha='center', fontsize=7)
            ax.plot([pos[0], wifi_main_location[0]], [pos[1], wifi_main_location[1]], 'g--', alpha=0.4, linewidth=0.8)

    def _plot_ground_truth_location(self, ax, wifi_main_location, current_bag_name, position_info):
        gt_pos_data = self.ground_truth_data[current_bag_name] # Original: [lat, lon]
        gt_lon, gt_lat = gt_pos_data[1], gt_pos_data[0] # Plotting as [lon, lat]

        ax.scatter(gt_lon, gt_lat, color='purple', s=100, marker='*', label='Ground Truth', zorder=5)
        ax.plot([wifi_main_location[0], gt_lon], [wifi_main_location[1], gt_lat], 'm--', alpha=0.6, linewidth=1)
        distance_wifi_gt = calculate_precise_distance(wifi_main_location[0], wifi_main_location[1], gt_lon, gt_lat)
        position_info.append(f'GT Position: ({gt_lon:.6f}, {gt_lat:.6f})')
        position_info.append(f'Dist (WiFi-GT): {distance_wifi_gt:.2f} m')

    def _plot_agloc_pose(self, ax, agloc_pose_geographic, wifi_connection_point, current_bag_name, position_info):
        agloc_lon, agloc_lat = agloc_pose_geographic
        ax.scatter(agloc_lon, agloc_lat, color='green', s=100, marker='*', label='AGLoc Pose', zorder=5)
        ax.plot([wifi_connection_point[0], agloc_lon], [wifi_connection_point[1], agloc_lat], 'c--', alpha=0.7, linewidth=1)
        distance_wifi_agloc = calculate_precise_distance(wifi_connection_point[0], wifi_connection_point[1], agloc_lon, agloc_lat)
        position_info.append(f'AGLoc Pose: ({agloc_lon:.6f}, {agloc_lat:.6f})')
        position_info.append(f'Dist (WiFi-AGLoc): {distance_wifi_agloc:.2f} m')

        # If ground truth is available, also calculate and plot AGLoc to GT distance
        if current_bag_name in self.ground_truth_data:
            gt_pos_data = self.ground_truth_data[current_bag_name]
            gt_lon, gt_lat = gt_pos_data[1], gt_pos_data[0]
            distance_agloc_gt = calculate_precise_distance(agloc_lon, agloc_lat, gt_lon, gt_lat)
            ax.plot([agloc_lon, gt_lon], [agloc_lat, gt_lat], color='black', linestyle=':', alpha=0.5, linewidth=1)
            position_info.append(f'Dist (AGLoc-GT): {distance_agloc_gt:.2f} m')

    def _finalize_and_save_plot(self, fig, ax, position_info, current_bag_name, timestamp_for_file):
        ax.set_xlabel('Longitude')
        ax.set_ylabel('Latitude')
        ax.set_title(f'Localization Result - {current_bag_name}')
        ax.legend(loc='upper left', bbox_to_anchor=(1.02, 1), fontsize='small') # Adjusted bbox_to_anchor
        ax.grid(True, linestyle=':', alpha=0.5)
        ax.set_aspect('equal', adjustable='box')

        # Prepare info text content
        info_text_content = "\n".join(position_info)

        # Add new position info text box to the right-bottom of the axes
        ax.text(1.02, 0.0, info_text_content, # x=1.02 (right of axis), y=0.0 (bottom of axis)
                transform=ax.transAxes, # Coordinates are relative to axes
                fontsize=8,
                verticalalignment='bottom', # Text box's bottom aligned with y=0.0
                horizontalalignment='left', # Text box's left aligned with x=1.02
                bbox=dict(boxstyle='round,pad=0.5', fc='lavender', alpha=0.5))

        # Adjust layout to make space for legend and new text box on the right
        # This should be called after all artists are added.
        plt.tight_layout(rect=[0.05, 0.05, 0.80, 0.95]) # Leave 20% on right

        # Save plot
        plot_dir = Path(self.plot_save_dir) # Use class member for save directory
        plot_filename = plot_dir / f'{current_bag_name}_{timestamp_for_file}.png'
        try:
            plot_dir.mkdir(parents=True, exist_ok=True)
            plt.savefig(plot_filename, dpi=200)
            self.logger.info(f'Plot saved to {plot_filename}')
        except Exception as e:
            self.logger.error(f"Error saving plot {plot_filename}: {e}")

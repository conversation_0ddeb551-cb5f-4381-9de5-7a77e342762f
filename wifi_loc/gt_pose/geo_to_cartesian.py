#!/usr/bin/env python3

import json
import math
import numpy as np

# 常量定义
EARTH_RADIUS = 6378137.0  # 地球半径（米）

# 坐标转换函数
def to_cartesian(ref_latlon, cur_latlon):
    """
    将经纬度转换为相对于参考点的局部坐标
    
    参数:
        ref_latlon: [纬度, 经度] 参考点
        cur_latlon: [纬度, 经度] 当前点
    
    返回:
        [x, y] 局部坐标
    """
    ref_lat, ref_lon = ref_latlon
    cur_lat, cur_lon = cur_latlon
    
    lat_rad = ref_lat * math.pi / 180.0
    d_lat = (cur_lat - ref_lat) * math.pi / 180.0
    d_lon = (cur_lon - ref_lon) * math.pi / 180.0
    
    y = EARTH_RADIUS * d_lat
    x = EARTH_RADIUS * math.cos(lat_rad) * d_lon
    
    return [x, y]

def geographic_to_map(local_xy, map_extrinsic_trans, map_yaw_angle_rad):
    """
    将局部坐标转换为map坐标系
    
    参数:
        local_xy: [x, y] 局部坐标
        map_extrinsic_trans: [x, y, z] 平移向量
        map_yaw_angle_rad: 旋转角度（弧度）
    
    返回:
        [x, y] map坐标系下的坐标
    """
    x_local, y_local = local_xy
    
    # 进行旋转
    cos_yaw = math.cos(map_yaw_angle_rad)
    sin_yaw = math.sin(map_yaw_angle_rad)
    x_ag = x_local * cos_yaw - y_local * sin_yaw
    y_ag = x_local * sin_yaw + y_local * cos_yaw
    
    # 进行平移
    x_map = x_ag + map_extrinsic_trans[0]
    y_map = y_ag + map_extrinsic_trans[1]
    
    return [x_map, y_map]

def main():
    # 读取输入JSON文件
    input_file = "/home/<USER>/AGLoc_ws/src/wifi_loc/gt_pose/rosbag_gt_pose.json"
    output_file = "/home/<USER>/AGLoc_ws/src/wifi_loc/gt_pose/rosbag_gt_pose_xy.json"
    
    # 参数设置（与wifi_dummy_node中相同）
    base_latitude = 31.17947960435    # 基准纬度
    base_longitude = 121.59139728509  # 基准经度
    map_extrinsic_trans = [9.8, -34.3, -16.0]  # AGmap到map的变换参数
    map_yaw_angle_deg = -81.0  # 度
    map_yaw_angle_rad = map_yaw_angle_deg * math.pi / 180.0
    
    # 读取JSON文件
    with open(input_file, 'r') as f:
        geo_data = json.load(f)
    
    # 创建输出数据结构
    xy_data = {}
    
    # 转换每个点
    for key, value in geo_data.items():
        # 输入格式是 [纬度, 经度]
        latitude, longitude = value
        
        # 转换为局部坐标
        local_xy = to_cartesian([base_latitude, base_longitude], [latitude, longitude])
        
        # 转换为map坐标系
        map_xy = geographic_to_map(local_xy, map_extrinsic_trans, map_yaw_angle_rad)
        
        # 保存结果，保留两位小数
        xy_data[key] = [round(map_xy[0], 2), round(map_xy[1], 2)]
    
    # 写入输出文件
    with open(output_file, 'w') as f:
        json.dump(xy_data, f, indent=2)
    
    print(f"转换完成，结果已保存到 {output_file}")

if __name__ == "__main__":
    main()

from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import IncludeLaunchDescription, ExecuteProcess, TimerAction
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.substitutions import FindPackageShare

def generate_launch_description():
    use_sim_time = False
    map_file = FindPackageShare('hesai_amcl').find('hesai_amcl') + '/maps/elevator.yaml'
    rviz_config_file = FindPackageShare('hesai_amcl').find('hesai_amcl') + '/config/amcl.rviz'
    amcl_yaml = FindPackageShare('hesai_amcl').find('hesai_amcl') + '/params/amcl_params.yaml'

    return LaunchDescription([
        # RViz Node
        Node(
            package='rviz2',
            executable='rviz2',
            name='rviz2',
            output='screen',
            arguments=['-d', rviz_config_file],
            parameters=[{
                'use_sim_time': use_sim_time
            }]
        ),

        # Static Transform Publisher
        Node(
            package='tf2_ros',
            executable='static_transform_publisher',
            name='static_transform_publisher_map2odom',
            arguments=[
                '0', '0', '0',
                '0', '0', '0', '1',
                'map', 'odom'
            ]
        ),

        # Wait for 2 seconds before launching other nodes
        TimerAction(
            period=2.0,
            actions=[
                # Map Server Node
                Node(
                    package='nav2_map_server',
                    executable='map_server',
                    name='map_server',
                    output='screen',
                    parameters=[{
                        'use_sim_time': use_sim_time,
                        'yaml_filename': map_file
                    }]
                ),

                # AMCL Node
                Node(
                    package='nav2_amcl',
                    executable='amcl',
                    name='amcl',
                    output='screen',
                    parameters=[amcl_yaml]
                ),

                # Lifecycle Manager Node
                Node(
                    package='nav2_lifecycle_manager',
                    executable='lifecycle_manager',
                    name='lifecycle_manager_localization',
                    output='screen',
                    parameters=[{'use_sim_time': use_sim_time},
                        {'autostart': True},
                        {'node_names': ['map_server', 'amcl']}]
                ),

                # Play Bag File
                ExecuteProcess(
                    cmd=['ros2', 'bag', 'play', '-s', 'mcap', '/home/<USER>/AGLoc_ws/rosbag/1226/1226.mcap', '--clock'],
                    output='screen'
                )
            ]
        ),

        
    ])

if __name__ == '__main__':
    generate_launch_description()

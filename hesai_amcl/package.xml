<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>hesai_amcl</name>
  <version>0.0.0</version>
  <description>Hesai AMCL</description>
  <maintainer email="<EMAIL>">jay</maintainer>
  <license>License declaration</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>nav2_amcl</depend>
  <depend>nav2_map_server</depend>
  <depend>tf2_ros</depend>
  <depend>pointcloud_to_laserscan</depend>
  <depend>nav_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>geometry_msgs</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>

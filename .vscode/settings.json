{"python.autoComplete.extraPaths": ["/home/<USER>/AGLoc_ws/install/wifi_msgs/lib/python3.10/site-packages", "/home/<USER>/AGLoc_ws/install/rss/lib/python3.10/site-packages", "/home/<USER>/AGLoc_ws/install/area_graph_data_parser/lib/python3.10/site-packages", "/opt/ros/iron/lib/python3.10/site-packages"], "files.associations": {"functional": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "any": "cpp", "array": "cpp", "atomic": "cpp", "strstream": "cpp", "bit": "cpp", "*.tcc": "cpp", "bitset": "cpp", "chrono": "cpp", "codecvt": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "cstdint": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "string": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "regex": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "semaphore": "cpp", "shared_mutex": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "thread": "cpp", "cfenv": "cpp", "cinttypes": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "valarray": "cpp", "variant": "cpp", "*.ipp": "cpp", "core": "cpp", "dense": "cpp", "__bit_reference": "cpp", "__hash_table": "cpp", "__split_buffer": "cpp", "__tree": "cpp", "filesystem": "cpp", "queue": "cpp", "stack": "cpp"}, "cmake.sourceDirectory": "/home/<USER>/AGLoc_ws/src/area_graph_data_parser", "python.analysis.extraPaths": ["/home/<USER>/AGLoc_ws/install/wifi_loc/lib/python3.10/site-packages", "/home/<USER>/AGLoc_ws/install/rss/lib/python3.10/site-packages", "/home/<USER>/AGLoc_ws/install/area_graph_data_parser/lib/python3.10/site-packages", "/opt/ros/iron/lib/python3.10/site-packages"]}
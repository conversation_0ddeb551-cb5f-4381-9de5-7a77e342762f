from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration

def generate_launch_description():
    return LaunchDescription([
        # 声明启动参数
        DeclareLaunchArgument(
            'publish_rate',
            default_value='1.0',
            description='发布频率(Hz)'
        ),
        DeclareLaunchArgument(
            'base_longitude',
            default_value='121.59139728509',
            description='基准经度'
        ),
        DeclareLaunchArgument(
            'base_latitude',
            default_value='31.17947960435',
            description='基准纬度'
        ),
        DeclareLaunchArgument(
            'position_noise',
            default_value='0.00001',
            description='位置噪声标准差'
        ),
        DeclareLaunchArgument(
            'floor',
            default_value='1',
            description='楼层'
        ),
        # 新增：地图变换参数
        DeclareLaunchArgument(
            'map_extrinsic_trans',
            default_value='[9.8,-34.3,-16.0]',
            description='AGmap到map的平移变换'
        ),
        DeclareLaunchArgument(
            'map_yaw_angle',
            default_value='-81.0',
            description='AGmap到map的旋转角度（度）'
        ),
        # 新增：相对坐标参数
        DeclareLaunchArgument(
            'relative_x',
            default_value='0.0',
            description='map坐标系下的x坐标'
        ),
        DeclareLaunchArgument(
            'relative_y',
            default_value='0.0',
            description='map坐标系下的y坐标'
        ),

        # 启动WiFi模拟节点
        Node(
            package='wifi_dummy_node',
            executable='wifi_dummy_node',
            name='wifi_dummy_node',
            parameters=[{
                'publish_rate': LaunchConfiguration('publish_rate'),
                'base_longitude': LaunchConfiguration('base_longitude'),
                'base_latitude': LaunchConfiguration('base_latitude'),
                'position_noise': LaunchConfiguration('position_noise'),
                'floor': LaunchConfiguration('floor'),
                'map_extrinsic_trans': LaunchConfiguration('map_extrinsic_trans'),
                'map_yaw_angle': LaunchConfiguration('map_yaw_angle'),
                'relative_x': LaunchConfiguration('relative_x'),
                'relative_y': LaunchConfiguration('relative_y'),
            }],
            output='screen'
        )
    ])

#pragma once

#include <array>
#include <cmath>

namespace wgs84 {
    constexpr double EARTH_RADIUS = 6378137.0;  // 地球半径（米）

    // 将经纬度转换为相对于参考点的局部坐标
    inline std::array<double, 2> toCartesian(const std::array<double, 2>& ref, const std::array<double, 2>& cur) {
        double lat_rad = ref[0] * M_PI / 180.0;
        double d_lat = (cur[0] - ref[0]) * M_PI / 180.0;
        double d_lon = (cur[1] - ref[1]) * M_PI / 180.0;

        double y = EARTH_RADIUS * d_lat;
        double x = EARTH_RADIUS * std::cos(lat_rad) * d_lon;

        return {x, y};
    }

    // 将局部坐标转换回经纬度（相对于参考点）
    inline std::array<double, 2> toGeographic(const std::array<double, 2>& ref_latlon, const std::array<double, 2>& local_xy) {
        double ref_lat_rad = ref_latlon[0] * M_PI / 180.0;
        
        double d_lat = local_xy[1] / EARTH_RADIUS;
        double d_lon = local_xy[0] / (EARTH_RADIUS * std::cos(ref_lat_rad));

        double lat = ref_latlon[0] + d_lat * 180.0 / M_PI;
        double lon = ref_latlon[1] + d_lon * 180.0 / M_PI;

        return {lat, lon};
    }
}

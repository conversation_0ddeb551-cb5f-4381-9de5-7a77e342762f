

#include <rclcpp/rclcpp.hpp>
#include "rss/msg/wifi_location.hpp"
#include <random>
#include <eigen3/Eigen/Dense>
#include <cstring>
#include "../include/wifi_dummy_node/WGS84toCartesian.h"

struct GeoCoordinate {
    double longitude;
    double latitude;
    double altitude;
};

class WifiDummyNode : public rclcpp::Node {
public:
    WifiDummyNode() : Node("wifi_dummy_node") {
        // 声明参数
        this->declare_parameter("publish_rate", 1.0);  // Hz
        this->declare_parameter("base_longitude", 121.59139728509);  // 基准经度
        this->declare_parameter("base_latitude", 31.17947960435);    // 基准纬度
        this->declare_parameter("position_noise", 0.00001);          // 位置噪声标准差
        this->declare_parameter("floor", 1);                         // 楼层
        
        // AGmap到map的变换参数(与params中相同，并且如果osmAG的root_node是确定的，这两个值不会改变)
        this->declare_parameter("map_extrinsic_trans", std::vector<double>{9.8, -34.3, -16.0});
        this->declare_parameter("map_yaw_angle", -81.0);  // 度
        
        // 相对坐标参数（在map坐标系下）
        this->declare_parameter("relative_x", 0.0);
        this->declare_parameter("relative_y", 0.0);

        // 获取参数
        double rate = this->get_parameter("publish_rate").as_double();
        base_longitude_ = this->get_parameter("base_longitude").as_double();
        base_latitude_ = this->get_parameter("base_latitude").as_double();
        position_noise_ = this->get_parameter("position_noise").as_double();
        floor_ = this->get_parameter("floor").as_int();
        
        // 获取变换参数
        auto trans_vec = this->get_parameter("map_extrinsic_trans").as_double_array();
        map_extrinsic_trans_ = {trans_vec[0], trans_vec[1], trans_vec[2]};
        map_yaw_angle_ = this->get_parameter("map_yaw_angle").as_double() * M_PI / 180.0;
        
        // 获取相对坐标
        relative_x_ = this->get_parameter("relative_x").as_double();
        relative_y_ = this->get_parameter("relative_y").as_double();

        // 创建发布者
        auto qos = rclcpp::QoS(rclcpp::KeepLast(10));  
        publisher_ = this->create_publisher<rss::msg::WifiLocation>(
            "/WifiLocation", 
            qos
        );

        // 创建定时器
        timer_ = this->create_wall_timer(
            std::chrono::duration<double>(1.0/rate),
            std::bind(&WifiDummyNode::timer_callback, this));

        // 初始化随机数生成器
        std::random_device rd;
        gen_ = std::mt19937(rd());
        dist_ = std::normal_distribution<double>(0.0, position_noise_);

        RCLCPP_INFO(this->get_logger(), "WiFi dummy node initialized");
        RCLCPP_INFO(this->get_logger(), "Base position: %.8f, %.8f", base_longitude_, base_latitude_);
        RCLCPP_INFO(this->get_logger(), "Relative position (map frame): %.2f, %.2f", relative_x_, relative_y_);
        
        // 计算并显示对应的经纬度
        auto geo = mapToGeographic(relative_x_, relative_y_);
        RCLCPP_INFO(this->get_logger(), "Corresponding geographic position: %.8f, %.8f", 
                    geo.longitude, geo.latitude);
    }

private:
    // 将map坐标系下的坐标转换为经纬度
    GeoCoordinate mapToGeographic(double x, double y) {
        // 1. 从map坐标系转回AGmap坐标系
        // 首先减去平移
        double x_ag = x - map_extrinsic_trans_[0];
        double y_ag = y - map_extrinsic_trans_[1];
        
        // 然后进行反向旋转
        double cos_yaw = std::cos(-map_yaw_angle_);  // 注意这里是负角
        double sin_yaw = std::sin(-map_yaw_angle_);
        double x_local = x_ag * cos_yaw - y_ag * sin_yaw;
        double y_local = x_ag * sin_yaw + y_ag * cos_yaw;

        // 2. 从局部坐标转换为经纬度
        std::array<double, 2> ref_latlon = {base_latitude_, base_longitude_};
        std::array<double, 2> local_xy = {x_local, y_local};
        auto geo = wgs84::toGeographic(ref_latlon, local_xy);

        GeoCoordinate result;
        result.latitude = geo[0];
        result.longitude = geo[1];
        result.altitude = 0.0;
        return result;
    }

    void timer_callback() {
        auto message = rss::msg::WifiLocation();
        
        // 从相对坐标计算经纬度
        auto geo = mapToGeographic(relative_x_, relative_y_);
        
        // 添加随机噪声
        message.longitude = geo.longitude + dist_(gen_);
        message.latitude = geo.latitude + dist_(gen_);
        message.floor = floor_;

        publisher_->publish(message);
    }

    rclcpp::Publisher<rss::msg::WifiLocation>::SharedPtr publisher_;
    rclcpp::TimerBase::SharedPtr timer_;
    
    // 基准点参数
    double base_longitude_;
    double base_latitude_;
    double position_noise_;
    int floor_;
    
    // 变换参数
    std::array<double, 3> map_extrinsic_trans_;
    double map_yaw_angle_;
    
    // 相对坐标
    double relative_x_;
    double relative_y_;
    
    // 随机数生成器
    std::mt19937 gen_;
    std::normal_distribution<double> dist_;
};

int main(int argc, char * argv[]) {
    rclcpp::init(argc, argv);
    rclcpp::spin(std::make_shared<WifiDummyNode>());
    rclcpp::shutdown();
    return 0;
}

from launch import LaunchDescription
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from launch.substitutions import PathJoinSubstitution

def generate_launch_description():
    # 获取地图文件的路径
    map_file = PathJoinSubstitution([
        FindPackageShare('areaGraphDataParser'),
        'data',
        'map1.yaml'
    ])

    return LaunchDescription([
        # 启动map_server节点，并传递地图文件参数
        Node(
            package='nav2_map_server',
            executable='map_server',
            name='map_server',
            output='screen',
            parameters=[{'yaml_filename': map_file}]
        )
    ])

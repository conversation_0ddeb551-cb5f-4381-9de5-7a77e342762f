from launch import LaunchDescription
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from launch.substitutions import PathJoinSubstitution
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration

def generate_launch_description():
    # 声明OSM文件路径参数
    osm_file_arg = DeclareLaunchArgument(
        'osm_file',
        default_value='SIST1_SEM_fixid_biggestever_with_vertical_passages.osm',
        description='OSM file name to parse'
    )
    
    # 获取包路径
    osm_file_path = PathJoinSubstitution([
        FindPackageShare('area_graph_data_parser'),
        'data',
        'fix_id',
        LaunchConfiguration('osm_file')
    ])

    area_graph_config = PathJoinSubstitution([
        FindPackageShare('area_graph_data_parser'),
        'config',
        'areaGraphRviz2.rviz'
    ])

    map_file = PathJoinSubstitution([
        FindPackageShare('area_graph_data_parser'),
        'data',
        'map1.yaml'
    ])

    return LaunchDescription([
        # 添加文件路径参数
        osm_file_arg,

        # 启动 RViz 节点
        Node(
            package='rviz2',
            executable='rviz2',
            name='rviz',
            output='screen',
            arguments=['-d', area_graph_config],
            parameters=[{'use_sim_time': False}]  
        ),

        # 启动静态变换发布器 (map to AGmap)
        Node(
            package='tf2_ros',
            executable='static_transform_publisher',
            name='AGmap_map_bro',
            arguments=['0', '0', '0', '0', '0', '0', '1', 'map', 'AGmap']
        ),

        # 启动静态变换发布器 (world to map)
        Node(
            package='tf2_ros',
            executable='static_transform_publisher',
            name='world_map',
            arguments=['0', '0', '0', '0', '0', '0', '1', 'world', 'map']
        ),

        # 启动数据处理节点
        Node(
            package='area_graph_data_parser',
            executable='main',
            name='main',
            output='screen',
            parameters=[{
                'osm_file_path': osm_file_path  # 将参数传递给main节点
            }]
        ),

        # 启动地图服务器节点
        Node(
            package='nav2_map_server',
            executable='map_server',
            name='map_server1',
            output='screen',
            parameters=[{'yaml_filename': map_file}]
        )
    ])
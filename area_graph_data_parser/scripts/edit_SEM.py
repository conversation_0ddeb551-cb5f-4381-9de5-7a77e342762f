import xml.etree.ElementTree as ET

def starts_with_target_floor(text):
    """检查文本是否以F1或F2开头"""
    return text.startswith('F1') or text.startswith('F2')

def modify_osm_xml(input_file, output_file):
    """
    修改OSM XML文件:
    - 为符合条件的passage way添加C1-前缀
    - 为符合条件的room way添加C1-前缀和parent标签
    处理所有F1或F2开头的情况
    """
    # 解析XML文件
    tree = ET.parse(input_file)
    root = tree.getroot()
    
    # 遍历所有way元素
    for way in root.findall(".//way"):
        tags = way.findall(".//tag")
        
        # 用字典存储way的所有标签，便于后续判断
        tag_dict = {tag.get('k'): tag.get('v') for tag in tags}
        
        # 处理passage类型的way
        if tag_dict.get('osmAG:type') == 'passage':
            from_tag = way.find(".//tag[@k='osmAG:from']")
            to_tag = way.find(".//tag[@k='osmAG:to']")
            
            if (from_tag is not None and to_tag is not None and 
                starts_with_target_floor(from_tag.get('v')) and 
                starts_with_target_floor(to_tag.get('v'))):
                
                # 添加C1-前缀（如果还没有的话）
                from_value = from_tag.get('v')
                to_value = to_tag.get('v')
                
                if not from_value.startswith('C1-'):
                    from_tag.set('v', f"C1-{from_value}")
                if not to_value.startswith('C1-'):
                    to_tag.set('v', f"C1-{to_value}")
                    
                print(f"Modified passage way {way.get('id')}: updated from/to tags")
        
        # 处理room类型的way
        elif tag_dict.get('osmAG:areaType') == 'room':
            name_tag = way.find(".//tag[@k='name']")
            
            if name_tag is not None and starts_with_target_floor(name_tag.get('v')):
                # 添加C1-前缀（如果还没有的话）
                name_value = name_tag.get('v')
                if not name_value.startswith('C1-'):
                    name_tag.set('v', f"C1-{name_value}")
                
                # 检查房间所在的楼层
                floor_prefix = 'F1' if name_value.startswith('F1') else 'F2'
                
                # 添加或更新osmAG:parent标签
                parent_tag = way.find(".//tag[@k='osmAG:parent']")
                if parent_tag is None:
                    # 如果不存在parent标签，创建新的
                    parent_tag = ET.SubElement(way, 'tag')
                    parent_tag.set('k', 'osmAG:parent')
                
                parent_tag.set('v', f'C1-{floor_prefix}')
                print(f"Modified room way {way.get('id')}: updated name and parent tags")
    
    # 保存修改后的文件
    tree.write(output_file, encoding='UTF-8', xml_declaration=True)

def verify_modifications(file_path):
    """验证修改后的文件是否符合要求"""
    tree = ET.parse(file_path)
    root = tree.getroot()
    verification_results = {
        'passages_modified': 0,
        'rooms_modified': 0,
        'errors': []
    }
    
    for way in root.findall(".//way"):
        tags = {tag.get('k'): tag.get('v') for tag in way.findall(".//tag")}
        
        # 验证passage
        if tags.get('osmAG:type') == 'passage':
            from_value = tags.get('osmAG:from', '')
            to_value = tags.get('osmAG:to', '')
            
            if (starts_with_target_floor(from_value.replace('C1-', '')) and 
                starts_with_target_floor(to_value.replace('C1-', ''))):
                if not (from_value.startswith('C1-') and to_value.startswith('C1-')):
                    verification_results['errors'].append(
                        f"Passage way {way.get('id')} missing C1- prefix")
                else:
                    verification_results['passages_modified'] += 1
        
        # 验证room
        elif tags.get('osmAG:areaType') == 'room':
            name = tags.get('name', '')
            if starts_with_target_floor(name.replace('C1-', '')):
                floor_prefix = 'F1' if 'F1' in name else 'F2'
                expected_parent = f'C1-{floor_prefix}'
                
                if not name.startswith('C1-'):
                    verification_results['errors'].append(
                        f"Room way {way.get('id')} missing C1- prefix in name")
                elif tags.get('osmAG:parent') != expected_parent:
                    verification_results['errors'].append(
                        f"Room way {way.get('id')} has incorrect parent tag")
                else:
                    verification_results['rooms_modified'] += 1
    
    return verification_results

if __name__ == "__main__":
    input_file = "/home/<USER>/osmAG_ws/src/area_graph_data_parser/data/semantic_tags/SIST1_SEM_Sem_right.osm"  # 替换为实际的输入文件路径
    output_file = "/home/<USER>/osmAG_ws/src/area_graph_data_parser/data/semantic_tags/SIST1_SEM_Sem_right.osm"  # 替换为实际的输出文件路径
    
    try:
        # 执行修改
        modify_osm_xml(input_file, output_file)
        print("\nModification completed. Verifying results...")
        
        # 验证修改
        results = verify_modifications(output_file)
        print(f"\nModification summary:")
        print(f"- Modified passages: {results['passages_modified']}")
        print(f"- Modified rooms: {results['rooms_modified']}")
        
        if results['errors']:
            print("\nWarning: Found following issues:")
            for error in results['errors']:
                print(f"- {error}")
        else:
            print("\nVerification passed: All modifications are valid.")
            
    except Exception as e:
        print(f"Error during modification: {str(e)}")



#!/usr/bin/env python3
import numpy as np
import math

def deg2rad(deg):
    return deg * np.pi / 180.0

def get_local_transform(lat1, lon1, lat2, lon2):
    """
    计算两个经纬度坐标点之间的局部坐标变换
    参数:
        lat1, lon1: 参考点的纬度和经度（度）
        lat2, lon2: 目标点的纬度和经度（度）
    返回:
        dx, dy: 局部坐标系中的东向和北向距离（米）
    """
    # WGS84椭球体参数
    a = 6378137.0  # 赤道半径
    f = 1/298.257223563  # 扁率
    b = a * (1 - f)  # 极半径
    e2 = 1 - (b*b)/(a*a)  # 第一偏心率平方

    # 将经纬度转换为弧度
    lat1_rad = deg2rad(lat1)
    lon1_rad = deg2rad(lon1)
    lat2_rad = deg2rad(lat2)
    lon2_rad = deg2rad(lon2)

    # 计算卯酉圈曲率半径
    N = a / np.sqrt(1 - e2 * np.sin(lat1_rad)**2)

    # 计算局部坐标系中的差异
    dx = N * np.cos(lat1_rad) * (lon2_rad - lon1_rad)  # 东向距离
    dy = N * (lat2_rad - lat1_rad)  # 北向距离

    return dx, dy

if __name__ == "__main__":
    # 使用给定的坐标点
    # root1
    lat1 = 31.17947960453  # 参考点纬度
    lon1 = 121.59139728492  # 参考点经度

    # root2
    lat2 = 31.17946047265  # 目标点纬度
    lon2 = 121.59022200987  # 目标点经度

    # 计算局部坐标变换
    dx, dy = get_local_transform(lat1, lon1, lat2, lon2)
    
    print(f"\n局部坐标变换结果:")
    print(f"东向距离 (dx): {dx:.2f} 米")
    print(f"北向距离 (dy): {dy:.2f} 米")
    print(f"相对位置: 从参考点到目标点")
    
    # 计算总距离
    total_distance = np.sqrt(dx**2 + dy**2)
    print(f"总距离: {total_distance:.2f} 米")
    
    # 计算方位角（相对于正北方向）
    heading = np.arctan2(dx, dy) * 180 / np.pi
    if heading < 0:
        heading += 360
    print(f"方位角: {heading:.2f} 度")

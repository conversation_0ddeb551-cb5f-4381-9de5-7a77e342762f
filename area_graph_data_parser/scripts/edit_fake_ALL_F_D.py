# Last edit by <PERSON><PERSON><PERSON>
# 该脚本用于后处理 generate_fake_ALL_FD.py 所生成的 fake的SIST1-D区1～4楼地图，用以把3，4层的room的parent改为对应的structure，这是为了适应于我现在的可视化代码
# 我现在的可视化代码中，structure和room已经通过osmAG:parent构建了层次关系 
import xml.etree.ElementTree as ET
from xml.dom import minidom

def prettify(elem):
    """将ElementTree对象转换为格式化的XML字符串"""
    rough_string = ET.tostring(elem, 'utf-8')
    reparsed = minidom.parseString(rough_string)
    return reparsed.toprettyxml(indent="  ")

def update_room_parents(input_file, output_file):
    # 解析XML文件
    tree = ET.parse(input_file)
    root = tree.getroot()
    
    # 存储结构体ID
    structure_ids = {
        '3': None,  # E1d-F3的ID
        '4': None   # E1d-F4的ID
    }
    
    # 首先找到结构体的ID
    for way in root.findall('way'):
        for tag in way.findall('tag'):
            if tag.attrib.get('k') == 'name':
                if tag.attrib.get('v') == 'E1d-F3':
                    structure_ids['3'] = way.attrib['id']
                elif tag.attrib.get('v') == 'E1d-F4':
                    structure_ids['4'] = way.attrib['id']
    
    # 更新房间的parent属性
    for way in root.findall('way'):
        level = None
        is_room = False
        parent_tag_exists = False
        
        # 检查是否是房间且在3或4层
        for tag in way.findall('tag'):
            if tag.attrib.get('k') == 'level':
                level = tag.attrib.get('v')
            elif (tag.attrib.get('k') == 'osmAG:areaType' and 
                  tag.attrib.get('v') == 'room'):
                is_room = True
            elif tag.attrib.get('k') == 'osmAG:parent':
                parent_tag_exists = True
                parent_tag = tag
        
        # 如果是3或4层的房间，更新或添加parent标签
        if is_room and level in ['3', '4']:
            parent_id = structure_ids[level]
            if parent_id:
                if parent_tag_exists:
                    # 更新现有的parent标签
                    parent_tag.set('v', parent_id)
                else:
                    # 创建新的parent标签
                    new_tag = ET.Element('tag')
                    new_tag.set('k', 'osmAG:parent')
                    new_tag.set('v', parent_id)
                    way.append(new_tag)
    
    # 保存修改后的文件
    pretty_xml = prettify(root)
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('<?xml version="1.0" encoding="utf-8"?>\n')
        clean_xml = '\n'.join(line for line in pretty_xml.split('\n') if line.strip())
        f.write(clean_xml)

if __name__ == '__main__':
    input_file = '/home/<USER>/osmAG_ws/src/area_graph_data_parser/data/fix_id/SIST1_ALL_F_D.osm'
    output_file = '/home/<USER>/osmAG_ws/src/area_graph_data_parser/data/fix_id/SIST1_ALL_F_D_updated.osm'
    update_room_parents(input_file, output_file)
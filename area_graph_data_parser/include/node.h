#ifndef NODE_H_
#define NODE_H_

#include <map>
#include <vector>
#include <memory>
#include <string>
#include <unordered_set>
#include <eigen3/Eigen/Dense>
#include <iostream>
#include <fstream>

namespace osm_ag {

// 定义节点ID类型
using NodeId = int64_t;

/**
 * @brief 地理坐标结构体,用于存储GPS坐标信息
 */
struct GeoCoordinate {
    double latitude;    // 纬度
    double longitude;   // 经度 
    double altitude{0.0}; // 海拔高度,默认为0
};

/**
 * @brief 节点属性结构体
 * 存储节点的各种属性信息,包括位置、可见性、动作类型等
 */
struct NodeAttributes {
public:
    using Ptr = std::unique_ptr<NodeAttributes>;
    
    // 构造和析构函数
    NodeAttributes();
    ~NodeAttributes() = default;  

    /**
     * @brief 克隆当前属性对象
     * @return 返回属性对象的智能指针副本
     */
    NodeAttributes::Ptr clone() const {
        return std::make_unique<NodeAttributes>(*this);
    }

    // 节点属性
    Eigen::Vector3d position{0.0, 0.0, 0.0}; // 三维笛卡尔坐标位置
    GeoCoordinate geo_position;               // GPS地理坐标
    bool visible;                             // 节点是否可见
    std::string action;                       // 节点动作类型:"modify"或"real"

    // OSM额外信息
    std::map<std::string, uint64_t> osm_otherinfos_number;   // 存储数值类型的OSM信息(如uid)
    std::map<std::string, std::string> osm_otherinfos_string;// 存储字符串类型的OSM信息(如user)
    std::map<std::string, std::string> tags;                 // 节点标签信息

protected:
    std::ostream& fill_ostream(std::ostream& out) const; // 只声明，不实现
};

/**
 * @brief 节点状态枚举类
 * 用于跟踪节点在图中的历史状态
 */
enum class NodeStatus { 
    NEW,         // 新建节点
    VISIBLE,     // 可见节点
    MERGED,      // 已合并节点
    DELETED,     // 已删除节点
    NONEXISTENT  // 不存在的节点
};

// 前向声明
class Area;
class Passage;

/**
 * @brief 节点基类
 * 表示地图中的一个基本节点单元
 */
class Node {
public:
    using Attributes = NodeAttributes;
    using AttributesPtr = std::unique_ptr<Attributes>;
    using Ptr = std::unique_ptr<Node>;

    // 友元类声明
    friend class Area;
    friend class Passage; 
    friend class AreaGraph;

    /**
     * @brief 构造函数
     * @param id 节点ID
     * @param attrs 节点属性智能指针
     */
    Node(NodeId id, AttributesPtr&& attrs);
    virtual ~Node() = default;

    NodeId id;  // 节点唯一标识符
    AttributesPtr attributes_;  // 节点属性智能指针

    std::unordered_set<int64_t> area_belongto_;     // 节点所属区域集合
    std::unordered_set<int64_t> passage_belongto_;  // 节点所属通道集合
    
    /**
     * @brief 获取节点属性指针
     * @return 返回节点属性的原始指针
     */
    Attributes* getAttributesPtr() const { return attributes_.get(); }

protected:
    virtual std::ostream& fill_ostream(std::ostream& out) const; // 只声明，不实现
};

/**
 * @brief 原点节点类
 * 继承自Node类,用于表示坐标系统的原点
 */
class OriginNode : public Node {
public:
    Eigen::Vector3d rotation;  // 原点旋转向量

    /**
     * @brief 构造函数
     * @param id 节点ID
     * @param rot 旋转向量
     * @param attrs 节点属性智能指针
     */
    OriginNode(NodeId id, Eigen::Vector3d rot, AttributesPtr&& attrs);
    ~OriginNode() override = default;
};

} // namespace osm_ag

#endif // NODE_H_
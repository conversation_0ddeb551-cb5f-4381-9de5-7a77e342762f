#ifndef _GRAPH_DATA_HANDLER_H_
#define _GRAPH_DATA_HANDLER_H_

#include <string>
#include "area_grid_map.h"  // 包含 AreaGraph 等自定义类型
#include <tinyxml2.h>


/**
 * @brief 解析 OSM 文件为 AreaGraph 结构
 * 
 * @param graph 要解析的 AreaGraph 对象
 * @param file_path OSM 文件的路径，默认为 "../data/f1-d(v1).osm"
 * @return bool 返回解析是否成功
 */
bool Parsing_Osm2AreaGraph(osm_ag::AreaGraph& graph, const char* file_path = "../data/f1-d(v1).osm");

/**
 * @brief 将 AreaGraph 结构保存为 OSM 文件
 * 
 * @param graph 要保存的 AreaGraph 对象
 * @param file_path 保存的目标 OSM 文件路径，默认为 "../data/fix_id/new.osm"
 */
void Save_AreaGraph2Osm(osm_ag::AreaGraph& graph, const char* file_path = "../data/fix_id/new.osm");

void TraverseArea(osm_ag::AreaGraph& graph);

void TraversePassage(osm_ag::AreaGraph& graph);
#endif // _GRAPH_DATA_HANDLER_H_

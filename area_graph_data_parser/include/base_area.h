#ifndef _BASE_AREA_H_
#define _BASE_AREA_H_

#include "node.h"
#include <map>
#include <vector>
#include <memory>
#include <set>
#include <unordered_set>

namespace osm_ag {

// 定义层级ID类型
using LayerId = uint64_t;

/**
 * @brief 路径属性基类,用于存储区域和通道的共同属性
 */
struct WayAttributes {
public:
    using Ptr = std::unique_ptr<WayAttributes>;
    
    /**
     * @brief 默认构造函数
     */
    WayAttributes() = default;
    
    /**
     * @brief 虚析构函数
     */
    virtual ~WayAttributes() = default;
    
    /**
     * @brief 克隆当前属性对象
     * @return 返回属性对象的unique_ptr
     */
    virtual WayAttributes::Ptr clone() const {
        return std::make_unique<WayAttributes>(*this);
    }

    bool visible;                                  // 是否可见
    std::string action;                           // 动作类型: "modify" 或 "real"
    std::map<std::string, std::string> tags;      // 存储其他标签信息

protected:
    /**
     * @brief 输出流操作符重载
     * @param out 输出流对象
     * @return 返回输出流引用
     */
    virtual std::ostream& fill_ostream(std::ostream& out) const;
};

/**
 * @brief 区域基类,作为Area和Passage的父类
 * 
 * 该类定义了区域的基本接口和属性,是整个区域图系统的基础
 */
class BaseArea {
public:
    using NodeRef = std::reference_wrapper<const Node>;
    using Ptr = std::unique_ptr<BaseArea>;

    // 允许 AreaGraph 访问私有成员
    friend class AreaGraph;
    
    /**
     * @brief 虚析构函数
     */
    virtual ~BaseArea() = default;

    // TODO: 以下是可能需要实现的功能接口
    /*
    // 获取区域中心位置
    // virtual Eigen::Vector3d getCenterPosition() const = 0;

    // 检查节点状态
    // virtual NodeStatus checkNode(NodeId node_id) const = 0;

    // 获取新添加的节点ID列表
    // virtual void getNewNodes(std::vector<NodeId>& new_nodes, bool clear_new) = 0;

    // 获取已删除的节点ID列表
    // virtual void getRemovedNodes(std::vector<NodeId>& removed_nodes, bool clear_removed) = 0;
    
    // 删除指定节点
    // virtual bool removeNode(NodeId node_id) = 0;
    
    // 克隆当前区域
    // virtual BaseArea::Ptr clone() const = 0;
    */

protected:
    // 保护成员变量和方法可以在这里添加
};

} // namespace osm_ag

#endif // _BASE_AREA_H_
#ifndef _AREAGRIDMAP_H_
#define _AREAGRIDMAP_H_

#include <cmath>
#include <unordered_map>
#include <iostream>
#include <queue>
#include <opencv2/opencv.hpp>
#include <opencv2/core/core.hpp>
#include <opencv2/imgproc/imgproc_c.h>

#include "area_graph.h"
#include "astar_cal.h"

namespace osm_ag {
    void ComputeCenter_Area(AreaGraph& graph, AreaId area_id);
    void InitOccupancyMap_Area(AreaGraph& graph, AreaId area_id, double resolution = 0.1, int offset = 4);
}

#endif // _AREAGRIDMAP_H_

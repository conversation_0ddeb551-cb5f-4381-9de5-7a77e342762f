#ifndef _AREAGRAPH_H_
#define _AREAGRAPH_H_

#include "passage.h"
#include <map>
#include <vector>
#include <memory>
#include <set>
#include <unordered_set>

namespace osm_ag {

/**
 * @brief AreaGraph类，管理和组织整个场景的拓扑结构
 * 
 * AreaGraph是一个分层的数据结构，包含:
 * 1. 节点(Nodes): 基本的空间位置点
 * 2. 区域(Areas): 由节点围成的封闭区域
 * 3. 通道(Passages): 连接不同区域的路径
 */
class AreaGraph {
public:
    using Ptr = std::shared_ptr<AreaGraph>;

    /**
     * @brief 默认构造函数
     */
    AreaGraph() {
        printf("Area Graph initialized!!!\n");
    }

    bool hasValidOrigin() const {
        return initial && origin_.second != nullptr;
    }
    
    /**
     * @brief 虚析构函数
     */
    virtual ~AreaGraph() = default;

    /**
     * @brief 添加新节点到图中
     * @param node_ptr 节点智能指针
     */
    void AddNodeVertex(Node::Ptr&& node_ptr) {
        NodeId idx_ = node_ptr->id;
        nodes_[idx_] = std::move(node_ptr);
    }

    /**
     * @brief 添加新区域到图中
     * @param area_ptr 区域智能指针
     */
    void AddArea(Area::Ptr&& area_ptr) {
        AreaId idx_ = area_ptr->id;
        areas_[idx_] = std::move(area_ptr);
    }

    /**
     * @brief 添加新通道到图中
     * @param passage_ptr 通道智能指针
     */
    void AddPassage(Passage::Ptr&& passage_ptr) {
        PassageId idx_ = passage_ptr->id;
        passages_[idx_] = std::move(passage_ptr);
    }

    /**
     * @brief 获取图中节点总数
     * @return 节点数量
     */
    int NodeNums() const {
        return nodes_.size();
    }

    friend class PathBase;  // 允许PathBase访问私有成员

public:  // 公共数据成员
    // 基础元素容器
    std::map<NodeId, Node::Ptr> nodes_;        // 所有节点
    std::map<AreaId, Area::Ptr> areas_;        // 所有区域
    std::map<PassageId, Passage::Ptr> passages_; // 所有通道

    // 节点ID范围
    NodeId max_node_id{INT64_MIN};             // 最大节点ID
    NodeId min_node_id{INT64_MAX};             // 最小节点ID

    // 层级结构
    std::map<AreaId, std::unordered_set<AreaId>> area_trees;     // 区域层级关系(父区域->子区域集合)
    std::map<AreaId, std::unordered_set<PassageId>> passage_trees; // 区域包含的通道(区域->通道集合)

    // 原点相关
    bool initial{false};                        // 是否已初始化原点
    std::pair<NodeId, OriginNode::Ptr> origin_; // 原点信息(ID和指针)

};

} // namespace osm_ag

#endif // _AREAGRAPH_H_
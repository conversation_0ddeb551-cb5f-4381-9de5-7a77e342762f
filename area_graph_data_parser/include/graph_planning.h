#ifndef _GRAPH_PLANNING_H_
#define _GRAPH_PLANNING_H_

#include <vector>
#include <opencv2/opencv.hpp>  
#include <Eigen/Core>
#include "area_grid_map.h"  
#include "path_graph.h"



// 基于 AreaGraph 结构的规划
void PlanInStructure(osm_ag::AreaGraph& graph, osm_ag::AreaId parent_areaid, osm_ag::PassageId start_id, osm_ag::PassageId end_id);
void PlanInOSM(osm_ag::AreaGraph& graph, osm_ag::PassageId start_id, osm_ag::PassageId end_id, std::vector<Eigen::Vector3d>& path_result);

// 基于 GridMap 的规划
void PlanInGridMap(osm_ag::AreaGraph& graph, osm_ag::PassageId start_id, osm_ag::PassageId end_id, cv::Mat& grid_map_show, 
                   double resolution = 0.1, bool if_show = true);

#endif // _GRAPH_PLANNING_H_

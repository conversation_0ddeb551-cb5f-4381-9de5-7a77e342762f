#ifndef _PATHGRAPH_H_
#define _PATHGRAPH_H_

#include "area_graph.h"
#include <limits>
#include <cmath>
#include <unordered_map>
#include <queue>
#include <geometry_msgs/msg/point.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <nav_msgs/msg/path.hpp>

namespace osm_ag {

// 定义通道轨迹图：存储两个通道之间的实际路径点
using PathedgesTrjMap = std::map<std::pair<PassageId, PassageId>, std::vector<Eigen::Vector3d>>;

/**
 * @brief 路径节点类
 * 我们只需要用到graph当中的passage的一些信息，通过id进行查询就行了
 */
struct PathNode {
    PathNode() : previous(nullptr) {}

    PassageId passage_id{0};        // 通道ID
    Eigen::Vector3d position;       // 位置信息
    PathNode* previous{nullptr};    // 用于规划时记录路径

    // A*规划相关属性
    double h_value{0.0};           // 启发值(h=0时等同于Dijkstra算法)
    double g_value{std::numeric_limits<double>::max()};  // 从起点到当前点的代价
    double f_value{h_value + g_value};                   // 总代价f = g + h
    bool is_open{false};           // 是否在开放列表中
    bool is_closed{false};         // 是否在关闭列表中

    /** 
     * @brief 初始化规划参数
     * 用于A*算法的节点初始化
     */
    void InitForPlanning(
        double hv = 0.0, 
        double gv = std::numeric_limits<double>::max(),
        bool open = false, 
        bool close = false) {
        h_value = hv;
        g_value = gv;
        f_value = h_value + g_value;
        is_open = open;
        is_closed = close;
    }

    void update_f() { 
        f_value = h_value + g_value; 
    }

    /**
     * @brief 优先队列排序操作符
     * 为了优先队列排序，默认大的在前面，所以写小于的时候用大于
     */
    bool operator<(const PathNode& cmp) const {
        return f_value > cmp.f_value;
    }
};

/**
 * @brief 路径边类，建立边
 * 用于表示通道之间的连接关系
 */
struct PathEdge {
    PathEdge() = default;
    PathEdge(PassageId from, PassageId to) : from(from), to(to) {}
    
    PassageId from;               // 起始通道
    PassageId to;                 // 目标通道
    double dist;                  // 用最简单的两条边的欧式距离或者用A*的距离（因为有node的值）
    AreaId area_id_belongto;      // 所属区域ID
    std::vector<Eigen::Vector3d> trj_astar;  // from->to的3D路径点序列
};

/**
 * @brief 路径结构，存储规划结果
 */
struct Path {
    Path(PassageId from, PassageId to) : from(from), to(to) {}
    PassageId from;              // 起点通道
    PassageId to;                // 终点通道
    std::vector<PassageId> path; // 通道序列
};

/**
 * @brief 通道图类
 * 通过遍历一个内区域所包含的所有边来构建基础通道图
 * @param pathnodes passages as pathnode of the lowest level area
 * @param pathedges 属于同一个area的不同的passage间组成的edge
 */
class PassageGraph {
public:
    std::map<PassageId, PathNode*> pathnodes;    // passage_id及其对应的节点
    std::vector<PathEdge*> pathedges;            // 属于同一个area的不同passage间的边
    PathedgesTrjMap pathedges_trj;               // 存储实际轨迹
};

/**
 * @brief 基础路径图类
 * 包含整张图的路径信息
 */
class PathBase {
public:
    PathBase() = default;
    virtual ~PathBase() = default;

    void AddEdge_AStar(PassageId start_id, PassageId end_id);
    void FindNeighbors(std::vector<std::pair<PassageId,double>>& subs, PassageId current_id);
    void PrintEdgesDistance(AreaGraph& graph, std::ofstream& outfile);
    const std::map<PassageId, PathNode*>& getPathnodes() const { return pathnodes; }
    const std::vector<Path*>& getPaths() const { return paths; }
protected:
    std::map<PassageId, PathNode*> pathnodes;    // passage_id及其值
    std::vector<PathEdge*> pathedges;            // 属于同一个area的不同passage间的边
    std::vector<Path*> paths;                    // 属于不同area的不同passage间的边，必须用A*的dist，因为是一条路径
    PathedgesTrjMap pathedges_trj;               // 只是同一个area的不同passage
};

/**
 * @brief 层级路径图类
 * PathLayer和PathBase的不同在于只需要计算并保存出口通道之间的路径，
 * 而PathBase并不需要计算path，只需要为预计算建立passage graph
 */
class PathLayer : public PathBase {
public:
    // 初始化，较高一层的areaid
    PathLayer(AreaGraph& graph, AreaId parent_areaid);
    virtual ~PathLayer() = default;

    AreaId parent_area_id;
};

/**
 * @brief 全局路径图类
 * 初始化，traverse all the lowest level passages
 * 当前版本的话可以直接用该函数构建整张地图的passage_graph
 */
class PathGraph : public PathBase {
public:
    PathGraph(AreaGraph& graph, PassageGraph& passage_graph);
    virtual ~PathGraph() = default;
};

} // namespace osm_ag

#endif // _PATHGRAPH_H_
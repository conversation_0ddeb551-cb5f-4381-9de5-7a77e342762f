#ifndef _VISUALIZATION_H_
#define _VISUALIZATION_H_

#include "path_graph.h"
#include <opencv2/opencv.hpp>
#include <opencv2/core/core.hpp>
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/imgproc/imgproc.hpp>
#include <visualization_msgs/msg/marker.hpp>
#include <visualization_msgs/msg/marker_array.hpp>
#include "area_graph_data_parser/msg/a_gindex.hpp"
#include "area_graph_data_parser/msg/area_index.hpp"
#include <geometry_msgs/msg/point.hpp>
#include <geometry_msgs/msg/point32.hpp>
#include <sensor_msgs/msg/point_cloud.hpp>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <std_msgs/msg/header.hpp>
#include <sensor_msgs/point_cloud_conversion.hpp>

#define IMAGE_W 2000
#define IMAGE_H 1200

void PlotNodePoints(osm_ag::AreaGraph& graph, cv::Mat& image, double resolution = 0.1);
void PlotAreas(osm_ag::AreaGraph& graph, cv::Mat& image, double resolution = 0.1);
void PlotPassages(osm_ag::AreaGraph& graph, cv::Mat& image, double resolution = 0.1);
void PlotPath(std::vector<Eigen::Vector3d> path, cv::Mat& image, double resolution = 0.1, bool areapath = true);
void PlotAreaInGrid(osm_ag::AreaGraph& graph, cv::Mat& image, osm_ag::AreaId id, double resolution = 0.1);
void PlotHighAreaInGrid(osm_ag::AreaGraph& graph, cv::Mat& image, osm_ag::AreaId id, double resolution = 0.1);
void PlotAreasInGrid(osm_ag::AreaGraph& graph, cv::Mat& image1, cv::Mat& image2, double resolution = 0.1);
cv::Point2i XYZ2Grid(Eigen::Vector3d point, double resolution = 0.1);
Eigen::Vector3d Grid2XYZ_vis(cv::Point pt_grid, double resolution = 0.1);
void plotAndSaveImages(osm_ag::AreaGraph& graph);

// 新增：层级配置结构体
struct LevelConfig {
    double height_per_level{8.0};  // 默认每层高度
    std::map<int, std_msgs::msg::ColorRGBA> level_colors;  // 每层对应的颜色
};

// 生成颜色的辅助函数声明
std_msgs::msg::ColorRGBA createColor(float r, float g, float b, float a);
std_msgs::msg::ColorRGBA generateLevelColor(int level, int total_levels);

// 单个结构的所有标记
struct StructureMarkers {
    std::string name;                                       // 结构名称
    visualization_msgs::msg::Marker structure_outline;      // 结构轮廓
    std::vector<visualization_msgs::msg::Marker> rooms;     // 房间标记
    std::vector<visualization_msgs::msg::Marker> passages;  // 通道标记
};



// 单层的所有标记
struct LevelVisualization {
    int level;                                             // 层号
    std::string name;                                      // 层名称
    double height;                                         // 层高度
    std::map<osm_ag::AreaId, StructureMarkers> structures;// 该层的所有结构
    visualization_msgs::msg::MarkerArray getMarkerArray() const; // 获取该层所有标记
};

// 创建可视化数据
void createVisualizationData(
    const osm_ag::AreaGraph& graph,
    std::map<int, LevelVisualization>& level_visuals,
    const LevelConfig* config = nullptr
);


#endif  // _VISUALIZATION_H_

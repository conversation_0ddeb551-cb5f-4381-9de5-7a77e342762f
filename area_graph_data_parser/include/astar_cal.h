#ifndef _ASTARCAL_H_
#define _ASTARCAL_H_
#include<opencv2/opencv.hpp>
#include <unordered_map>
#include<iostream>
#include <string>
using namespace std;
 
 
const double kCost1 = 1; //直移一个点消耗G 
const double kCost2 = 1.414; //斜移一个点消耗G 
 
 
struct CalcPt
{
  cv::Point pt; //OpenCV中点坐标 
  double F, G, H; //F=G+H 
  CalcPt* parent; //parent的坐标，这里没有用指针，从而简化代码 
  CalcPt(cv::Point _pt) :pt(_pt), F(0), G(0), H(0), parent(nullptr) {}//变量初始化 
};
 
 
class AStarCalc
{
private:
 
  string getPointid(CalcPt* cpoint){
      int str1 = cpoint->pt.x;
      int str2 = cpoint->pt.y;
      return to_string(str1) + "s" + to_string(str2);
  }
  //计算临近八个点
  vector<CalcPt*> getSurroundPoints(const CalcPt* point);
  //判断某点是否可以用于下一步判断 
  bool isCanreach(const CalcPt* point, CalcPt* target);
  //检测障碍点
  bool isInSites(const cv::Point* point) const;
  //从开启列表中返回F值最小的节点 
  CalcPt* getLeastFpoint();
  //计算FGH值 
  double calcG(CalcPt* temp_start, CalcPt* point);
  double calcH(CalcPt* point, const CalcPt* end);
  double calcF(CalcPt* point);
 
 
  vector<vector<int>> sites;  //图片点 0-可通行，1-障碍点
  list<CalcPt*> openList;  //开启列表 
  unordered_map<string, int> statemap; // point_id, its state
  unordered_map<string, CalcPt*> costGmap;
 
 
public:
  void InitSites(vector<vector<int>> _sites); //初始化地图
  //获取到路径
  vector<CalcPt*> findPath(const CalcPt& startPoint, const CalcPt& endPoint, double& resDistance);
};
 
#endif
 
#ifndef PASSAGE_H_
#define PASSAGE_H_

#include "area.h"
#include <map>
#include <vector>
#include <memory>
#include <set>
#include <unordered_set>

namespace osm_ag {

/**
 * @brief 通道ID类型定义
 */
using PassageId = int64_t;

/**
 * @brief 通道节点结构体
 * 定义通道的起点和终点节点
 */
struct PassageNodes {
    NodeId source;  // 起始节点
    NodeId target;  // 终止节点
};

/**
 * @brief 通道状态枚举类
 * 用于跟踪通道在地图中的状态
 */
enum class PassageStatus { 
    NEW,         // 新创建的通道
    VISIBLE,     // 当前可用的通道
    DELETED,     // 已删除的通道
    MERGED,      // 已与其他通道合并
    NONEXISTENT  // 不存在的通道
};

/**
 * @brief 通道类
 * 表示两个区域之间的连接通道，继承自BaseArea
 * 通道是两个节点构成的线段，作为无向边连接两个区域
 */
class Passage : public BaseArea {
public:
    using Ptr = std::unique_ptr<Passage>;
    using NodesCheckup = std::map<NodeId, NodeStatus>;  // 用于节点状态检查
    using Attributes = WayAttributes;                    // 通道属性类型
    using AttributesPtr = std::unique_ptr<Attributes>;   // 通道属性智能指针

    friend class AreaGraph;

    const PassageId id;              // 通道唯一标识符
    PassageNodes passage_nodes;      // 通道的起点和终点
    AttributesPtr info;              // 通道属性信息
    AreaId area_from;                // 起始区域ID
    AreaId area_to;                  // 终止区域ID
    Eigen::Vector3d center_position; // 通道中心点的3D位置

    /**
     * @brief 构造函数
     * @param id 通道ID
     * @param passage_nodes 通道的起终点节点
     * @param attrs 通道属性
     */
    explicit Passage(PassageId id, PassageNodes passage_nodes, AttributesPtr&& attrs);

    /**
     * @brief 获取通道属性指针
     * @return 返回通道属性的原始指针
     */
    Attributes* getAttributesPtr() const { return info.get(); }

    virtual ~Passage() = default;
};

/**
 * @brief 通道键结构体
 * 用于通道的唯一标识和查找
 * 确保 k1 始终小于 k2，以保证通道的唯一性
 */
struct PassageKey {
    NodeId k1;  // 较小的节点ID
    NodeId k2;  // 较大的节点ID

    /**
     * @brief 构造函数
     * @param k1 第一个节点ID
     * @param k2 第二个节点ID
     */
    PassageKey(NodeId k1, NodeId k2) : k1(std::min(k1, k2)), k2(std::max(k1, k2)) {}

    /**
     * @brief 相等运算符重载
     */
    inline bool operator==(const PassageKey& other) const {
        return k1 == other.k1 && k2 == other.k2;
    }

    /**
     * @brief 小于运算符重载
     * 用于map等容器的排序
     */
    inline bool operator<(const PassageKey& other) const {
        if (k1 == other.k1) {
            return k2 < other.k2;
        }
        return k1 < other.k1;
    }
};

/**
 * @brief 通道容器类
 * 用于管理和存储通道集合
 * 支持通道的增删改查等操作
 */
struct PassageContainer {
    using Passages = std::map<PassageKey, Passage>;
    using PassageStatusMap = std::map<PassageKey, PassageStatus>;

    Passages passages;               // 存储所有通道
    PassageStatusMap passage_status; // 存储通道状态

    /**
     * @brief 获取容器中通道的数量
     * @return 返回通道数量
     */
    inline size_t size() const { return passages.size(); }

    // TODO: 以下是计划实现的功能
    // void insert(NodeId source, NodeId target, WayAttributes::Ptr&& passage_info);
    // void remove(NodeId source, NodeId target);
    // void rewire(NodeId source, NodeId target, NodeId new_source, NodeId new_target);
    // bool contains(NodeId source, NodeId target) const;
    // void reset();
    // const Passage& get(NodeId source, NodeId target) const;
    // Passage& get(NodeId source, NodeId target);
    // PassageStatus getStatus(NodeId source, NodeId target) const;
    // void getRemoved(std::vector<PassageKey>& removed_Passages, bool clear_removed);
    // void getNew(std::vector<PassageKey>& new_Passages, bool clear_new);
};

} // namespace osm_ag

#endif // PASSAGE_H_
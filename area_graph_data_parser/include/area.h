#ifndef AREA_H_
#define AREA_H_

#include "base_area.h"
#include <map>
#include <vector>
#include <memory>
#include <set>
#include <unordered_set>
#include <grid_map_core/grid_map_core.hpp>
#include <rclcpp/rclcpp.hpp>
#include <nav_msgs/msg/occupancy_grid.hpp>
#include <nav_msgs/msg/grid_cells.hpp>
#include <opencv2/opencv.hpp>
#include <grid_map_core/GridMap.hpp>

namespace osm_ag {

/**
 * @brief 区域ID类型定义
 */
using AreaId = int64_t;

/**
 * @brief 栅格地图数据结构
 * 用于存储和管理区域的栅格地图信息
 */
struct GridMat {
    cv::Mat grid_mat;     // OpenCV格式的栅格地图数据
    int origin_grid_x;    // 栅格地图原点X坐标
    int origin_grid_y;    // 栅格地图原点Y坐标
    int offset;          // 地图偏移量
    double resolution;   // 地图分辨率(米/像素)
};

/**
 * @brief 区域类
 * 继承自BaseArea，用于表示环境中的一个区域单元
 * 可以是房间(room)或结构(structure)
 */
class Area : public BaseArea {
public:
    // 类型定义
    using Ptr = std::unique_ptr<Area>;
    using Nodes = std::map<NodeId, Node::Ptr>;           // 区域包含的节点映射
    using NodesCheckup = std::map<NodeId, NodeStatus>;   // 节点状态检查映射
    using Attributes = WayAttributes;                     // 区域属性类型
    using AttributesPtr = std::unique_ptr<Attributes>;    // 区域属性智能指针

    friend class AreaGraph;  // 声明AreaGraph为友元类

    // 基本属性
    const AreaId id;              // 区域唯一标识符
    LayerId layer_id;            // 区域所在层级ID（预留字段）
    const std::string type;      // 区域类型（"room" 或 "structure"）

    // 拓扑关系
    std::set<int64_t> passageids;                    // 与该区域相关的通道ID集合
    std::set<std::pair<NodeId,NodeId>> passage_nodes;// 构成通道的节点对集合
    std::vector<NodeId> nodes_inorder_;              // 按顺序排列的区域边界节点
    NodesCheckup nodes_status_;                      // 节点状态信息
    AreaId parent_;                                  // 父区域ID

    // 空间信息
    GridMat grid_mat_;                              // OpenCV格式的栅格地图
    nav_msgs::msg::OccupancyGrid area_occupancy;    // ROS2格式的占据栅格地图
    Eigen::Vector3d center_position;                // 区域中心点3D坐标

    /**
     * @brief 构造函数
     * @param id 区域ID
     * @param attrs 区域属性
     * @param type 区域类型
     */
    explicit Area(AreaId id, AttributesPtr&& attrs, std::string type);

    /**
     * @brief 状态查询函数
     */
    inline bool hasParent() const { return has_parent_; }
    inline bool hasSiblings() const { return not siblings_.empty(); }
    inline bool hasChildren() const { return not children_.empty(); }

    /**
     * @brief 获取区域属性指针
     */
    Attributes* getAttributesPtr() const { return attributes_.get(); }

    /**
     * @brief 状态标志
     */
    bool has_parent_ = false;      // 是否有父区域
    bool use_grid_map = false;     // 是否使用栅格地图
    bool use_occupancy_map = false;// 是否使用占据栅格地图
    bool init_center = false;      // 是否已初始化中心点
    bool is_leaf = true;           // 是否是叶子节点

    virtual ~Area() = default;

public:  // 原protected，但根据项目实际使用情况改为public
    AttributesPtr attributes_;                     // 区域属性
    std::set<AreaId> siblings_;                   // 同级区域集合
    std::set<AreaId> children_;                   // 子区域集合

    /**
     * @brief 输出流操作符重载
     * @param out 输出流
     * @return 修改后的输出流
     */
    virtual std::ostream& fill_ostream(std::ostream& out) const;

    /* 预留接口，可能在未来实现
    void update_grid_map(AreaGraph& graph, 
                        const double length = 10.0, 
                        const double width = 10.0, 
                        const double resolution = 0.2);
    */
};

/**
 * @brief Area类的辅助功能接口
 * 这些函数可以在需要时实现
 */
namespace area_utils {
    /**
     * @brief 计算区域中心点
     */
    void computeAreaCenter(Area& area);

    /**
     * @brief 更新区域栅格地图
     */
    void updateGridMap(Area& area);

    /**
     * @brief 检查两个区域是否相邻
     */
    bool areAreasAdjacent(const Area& area1, const Area& area2);
}

} // namespace osm_ag

#endif // AREA_H_
cmake_minimum_required(VERSION 3.5)
project(area_graph_data_parser)

# 添加编译选项
add_compile_options(-Wall -Wextra)

# 设置编译类型和编译选项
set(CMAKE_BUILD_TYPE Debug)
set(CMAKE_CXX_FLAGS_DEBUG "$ENV{CXXFLAGS} -Wall -g -ggdb")
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 查找需要的包
find_package(ament_cmake REQUIRED)
find_package(Eigen3 REQUIRED)
find_package(OpenCV REQUIRED)
find_package(tf2 REQUIRED)
find_package(rclcpp REQUIRED)
find_package(tf2_eigen REQUIRED)
find_package(grid_map_core REQUIRED)
find_package(grid_map_ros REQUIRED)
find_package(grid_map_msgs REQUIRED)
find_package(grid_map_cv REQUIRED)
find_package(grid_map_sdf REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(std_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(cv_bridge REQUIRED)
find_package(rosbag2_cpp REQUIRED)
find_package(visualization_msgs REQUIRED)
find_package(PCL REQUIRED)
find_package(pcl_conversions REQUIRED)
find_package(rosidl_default_runtime REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(type_description_interfaces REQUIRED)
find_package(rosidl_dynamic_typesupport REQUIRED)
find_package(rcl_interfaces REQUIRED)

# 添加消息文件
rosidl_generate_interfaces(${PROJECT_NAME}
  "msg/AreaIndex.msg"
  "msg/AGindex.msg"
  DEPENDENCIES std_msgs geometry_msgs
)

# 将生成的消息添加到项目中
ament_export_dependencies(rosidl_default_runtime)

# 包含目录
include_directories(
  include
  ${EIGEN3_INCLUDE_DIRS}
  ${OpenCV_INCLUDE_DIRS}
  ${rclcpp_INCLUDE_DIRS}
  ${tf2_INCLUDE_DIRS}
  ${tf2_eigen_INCLUDE_DIRS}
  ${grid_map_core_INCLUDE_DIRS}
  ${grid_map_ros_INCLUDE_DIRS}
  ${grid_map_msgs_INCLUDE_DIRS}
  ${grid_map_cv_INCLUDE_DIRS}
  ${grid_map_sdf_INCLUDE_DIRS}
  ${sensor_msgs_INCLUDE_DIRS}
  ${nav_msgs_INCLUDE_DIRS}
  ${std_msgs_INCLUDE_DIRS}
  ${geometry_msgs_INCLUDE_DIRS}
  ${cv_bridge_INCLUDE_DIRS}
  ${rosbag2_cpp_INCLUDE_DIRS}
  ${visualization_msgs_INCLUDE_DIRS}
  ${pcl_conversions_INCLUDE_DIRS}
  ${type_description_interfaces_INCLUDE_DIRS}
  ${rosidl_dynamic_typesupport_INCLUDE_DIRS}
  /opt/ros/iron/include/rosidl_dynamic_typesupport
  ${rcl_interfaces_INCLUDE_DIRS}
  ${CMAKE_CURRENT_BINARY_DIR}/rosidl_generator_cpp
)

# 创建核心库
add_library(${PROJECT_NAME}_core SHARED
  src/node.cpp
  src/area.cpp
  src/astarcal.cpp
  src/math_utils.cpp
  src/path_graph.cpp
  tinyxml2/tinyxml2.cpp
  src/graph_planning.cpp
  src/graph_data_handler.cpp
  src/visualization_utils.cpp
  src/area_grid_map.cpp
)

# 设置核心库的属性和依赖
target_include_directories(${PROJECT_NAME}_core PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>
)

ament_target_dependencies(${PROJECT_NAME}_core
  grid_map_core
  grid_map_ros
  rclcpp
  tf2
  tf2_eigen
  grid_map_msgs
  grid_map_cv
  grid_map_sdf
  sensor_msgs
  nav_msgs
  std_msgs
  geometry_msgs
  cv_bridge
  rosbag2_cpp
  visualization_msgs
  pcl_conversions
  Eigen3
  OpenCV
)

target_link_libraries(${PROJECT_NAME}_core
  ${OpenCV_LIBS}
  ${EIGEN3_LIBRARIES}
)

# 添加main可执行文件
add_executable(main src/main.cpp)
target_link_libraries(main 
  ${PROJECT_NAME}_core
  ${OpenCV_LIBS}
)

# 获取消息支持目标
rosidl_get_typesupport_target(cpp_typesupport_target
  ${PROJECT_NAME} "rosidl_typesupport_cpp"
)

target_link_libraries(main "${cpp_typesupport_target}")

# 添加topology_publisher节点
add_executable(topology_publisher src/topology_publisher_node.cpp)
target_link_libraries(topology_publisher
  ${PCL_LIBRARIES}
  ${pcl_conversions_LIBRARIES}
  ${PROJECT_NAME}_core
  "${cpp_typesupport_target}"
)

# 为main添加依赖
ament_target_dependencies(main
  rclcpp
  tf2
  tf2_eigen
  grid_map_core
  grid_map_ros
  grid_map_msgs
  grid_map_cv
  grid_map_sdf
  sensor_msgs
  nav_msgs
  std_msgs
  geometry_msgs
  cv_bridge
  rosbag2_cpp
  visualization_msgs
  pcl_conversions
  Eigen3
  OpenCV
  type_description_interfaces
  rosidl_dynamic_typesupport 
  rcl_interfaces 
)

# 为topology_publisher添加依赖
ament_target_dependencies(topology_publisher
  rclcpp
  sensor_msgs
  visualization_msgs
  geometry_msgs
  nav_msgs
)

# 安装目标
install(TARGETS
  ${PROJECT_NAME}_core
  main
  topology_publisher
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION lib/${PROJECT_NAME}
)

# 安装头文件
install(DIRECTORY include/
  DESTINATION include
)

# 安装其他文件
install(DIRECTORY
  launch 
  config 
  data
  DESTINATION share/${PROJECT_NAME}
)

ament_package()
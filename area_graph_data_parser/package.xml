<package format="3">
  <name>area_graph_data_parser</name>
  <version>0.0.0</version>
  <description>areaGraphDataParser</description>
  <maintainer email="<EMAIL>">xiefujing</maintainer>
  <license>TODO</license>

  <buildtool_depend>ament_cmake</buildtool_depend>


  <buildtool_depend>rosidl_default_generators</buildtool_depend>


  <build_depend>rclcpp</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>tf2_eigen</build_depend>


  <build_depend>grid_map</build_depend>
  <build_depend>grid_map_ros</build_depend>
  <build_depend>grid_map_core</build_depend>
  <build_depend>grid_map_msgs</build_depend>
  <build_depend>grid_map_cv</build_depend>
  <build_depend>grid_map_sdf</build_depend>

  <build_depend>sensor_msgs</build_depend>
  <build_depend>nav_msgs</build_depend>
  <build_depend>cv_bridge</build_depend>

  <build_depend>rosbag2_cpp</build_depend>
  <build_depend>visualization_msgs</build_depend>
  <build_depend>pcl_conversions</build_depend>
  <build_depend>type_description_interfaces</build_depend>
  <build_depend>rosidl_dynamic_typesupport</build_depend> 添加这一行
  <build_depend>rosidl_default_generators</build_depend>

  <exec_depend>rclcpp</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>tf2_eigen</exec_depend>


  <exec_depend>grid_map</exec_depend>
  <exec_depend>grid_map_ros</exec_depend>
  <exec_depend>grid_map_core</exec_depend>
  <exec_depend>grid_map_msgs</exec_depend>
  <exec_depend>grid_map_cv</exec_depend>
  <exec_depend>grid_map_sdf</exec_depend>

  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>nav_msgs</exec_depend>
  <exec_depend>cv_bridge</exec_depend>

  <exec_depend>rosbag2_cpp</exec_depend>
  <exec_depend>visualization_msgs</exec_depend>
  <exec_depend>pcl_conversions</exec_depend>
  <exec_depend>type_description_interfaces</exec_depend>
  <exec_depend>rosidl_dynamic_typesupport</exec_depend> 添加这一行
  <exec_depend>rosidl_default_runtime</exec_depend>
  
  <member_of_group>rosidl_interface_packages</member_of_group>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>

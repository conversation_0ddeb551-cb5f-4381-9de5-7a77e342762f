#include "graph_data_handler.h"
#include "math_utils.h"

using namespace tinyxml2;
using namespace osm_ag;
// Last Modifed by <PERSON><PERSON><PERSON> 24.10.29
// 图数据解析与存储

// 在函数开始处定义输出文件
std::ofstream debug_log("parsing_debug.log");

// 封装debug输出函数
void debug_print(const std::string& message, std::ofstream& log_file) {
    log_file << message << std::endl;
}

// 格式化输出的辅助函数
template<typename T>
std::string format_debug(const std::string& prefix, const T& value) {
    std::stringstream ss;
    ss << prefix << value;
    return ss.str();
}

namespace {
// 使用匿名命名空间，使函数仅在该文件内可见
bool isRootNode(XMLElement* xml_node) {
    // 首先检查是否有root属性（兼容旧格式）
    if(xml_node->Attribute("root") && std::string(xml_node->Attribute("root")) == "true") {
        return true;
    }
    
    // 检查是否有name="root"的标签（新格式）
    XMLElement* tag = xml_node->FirstChildElement("tag");
    while(tag) {
        if(std::string(tag->Attribute("k")) == "name" && 
           std::string(tag->Attribute("v")) == "root") {
            return true;
        }
        tag = tag->NextSiblingElement("tag");
    }
    return false;
}
}  // namespace


/**
 * @brief This function parses .osm(xml format) file to our area graph.
 * @param graph which the file parse to
 **/
bool Parsing_Osm2AreaGraph(AreaGraph& graph, const char* file_path){
    bool success_parse = false;

    // 打开调试日志文件
    std::ofstream debug_log("parsing_debug.log");
    debug_log << "Starting parsing process at: " << std::time(nullptr) << std::endl;
    debug_log << "Input file: " << file_path << std::endl;
    debug_log << "----------------------------------------" << std::endl;
    
    // 统计计数器
    int total_ways = 0;
    int total_areas = 0;
    std::map<std::string, int> areas_by_level;

    // 验证输入参数
    if (!file_path) {
        printf("Error: file_path is null\n");
        return false;
    }

    // 验证文件是否存在
    std::ifstream f(file_path);
    if (!f.good()) {
        printf("Error: file does not exist or cannot be opened: %s\n", file_path);
        return false;
    }

    XMLDocument doc;
    std::cout << "The loading file name is " << file_path << std::endl;

    XMLError eResult = doc.LoadFile(file_path);
    if(eResult != XML_SUCCESS){
        printf("XML Error Code: %d\n", eResult);
        printf("XML Error Description: %s\n", doc.ErrorStr());
        return success_parse;
    }

    // 获取根节点
    XMLNode* osm = doc.FirstChildElement("osm");
    if(!osm){
        printf("Error: Cannot find root element 'osm'\n");
        return success_parse;
    }

    // 存储所有需要后续处理的passage元素
    std::vector<XMLElement*> passage_elements;
    std::vector<XMLElement*> xml_left_elements;

    // 第一阶段：解析所有nodes和areas
    for(XMLElement* xml_node = osm->FirstChildElement(); xml_node != NULL; xml_node = xml_node->NextSiblingElement()){
        std::string node_type = xml_node->Value();
        
        if(node_type == "node"){
            NodeId node_id = atol(xml_node->Attribute("id")); 
            NodeAttributes attrs;
            attrs.geo_position.latitude = atof(xml_node->Attribute("lat")); 
            attrs.geo_position.longitude = atof(xml_node->Attribute("lon")); 
            attrs.action = std::string(xml_node->Attribute("action")); 
            attrs.visible = std::string(xml_node->Attribute("visible")) == "true" ? true : false; 

            // 检查是否为root节点，并且graph尚未初始化
            if(!graph.initial && isRootNode(xml_node)) {
                attrs.geo_position.altitude = xml_node->Attribute("alt") ? 
                    atof(xml_node->Attribute("alt")) : 0.0;
                
                Eigen::Vector3d rot;
                if(xml_node->Attribute("qx") && xml_node->Attribute("qy") && xml_node->Attribute("qz")) {
                    rot = {atof(xml_node->Attribute("qx")),
                          atof(xml_node->Attribute("qy")),
                          atof(xml_node->Attribute("qz"))};
                } else {
                    rot = Eigen::Vector3d(0, 0, 0);
                }
                
                OriginNode::Ptr origin_ptr = std::make_unique<OriginNode>(node_id, rot, attrs.clone());
                graph.initial = true;
                graph.origin_ = std::make_pair(node_id, std::move(origin_ptr));
                printf("Found root node with ID: %ld\n", node_id);
            }

            if(graph.initial){
                GeoCoordinate init = graph.origin_.second->getAttributesPtr()->geo_position;
                attrs.position = CoordinateTransform(init, attrs.geo_position);
                Node::Ptr node_ptr = std::make_unique<Node>(node_id, attrs.clone());
                graph.AddNodeVertex(std::move(node_ptr));
                graph.min_node_id = std::min(graph.min_node_id, node_id);
                graph.max_node_id = std::max(graph.max_node_id, node_id);
            }
        }
        else if(node_type == "way"){
            total_ways++;
            
            // 解析基本属性
            std::map<std::string, std::string> tags_;
            std::vector<NodeId> nodes_inorder_;
            WayAttributes way_attrs;
            
            const char* way_id_str = xml_node->Attribute("id");
            if(!way_id_str) continue;
            uint64_t way_id = atol(way_id_str);

            // 解析action和visible属性
            const char* action = xml_node->Attribute("action");
            const char* visible = xml_node->Attribute("visible");
            if(!action || !visible) continue;

            way_attrs.action = std::string(action);
            way_attrs.visible = std::string(visible) == "true";

            // 解析所有tag和node引用
            for(XMLElement* sub_elem = xml_node->FirstChildElement(); sub_elem != NULL; sub_elem = sub_elem->NextSiblingElement()){
                if(std::string(sub_elem->Value()) == "tag"){
                    const char* key = sub_elem->Attribute("k");
                    const char* value = sub_elem->Attribute("v");
                    if(key && value){
                        tags_[key] = value;
                    }
                }
                else if(std::string(sub_elem->Value()) == "nd"){
                    nodes_inorder_.emplace_back(atol(sub_elem->Attribute("ref")));
                }
            }

            // 根据type分别处理
            if(tags_["osmAG:type"] == "area"){
                std::string areatype = tags_["osmAG:areaType"];
                
                // 处理parent关系
                AreaId parent_id = 0;
                bool has_parent = false;
                if(tags_.find("osmAG:parent") != tags_.end()){
                    has_parent = true;
                    std::string parent_str = tags_["osmAG:parent"];
                    if(!parent_str.empty() && parent_str != "0"){
                        parent_id = stol(parent_str);
                    }
                }

                // 清理特殊标签
                tags_.erase("osmAG:parent");
                tags_.erase("osmAG:areaType");
                tags_.erase("osmAG:type");
                
                way_attrs.tags = tags_;
                
                // 验证area的闭合性
                if(nodes_inorder_.back() != nodes_inorder_.front()){ 
                    debug_log << "WARNING: Area " << way_id << " is not closed\n";
                    debug_log << "Details for Area " << way_id << ":" << std::endl;
                    debug_log << "  - First node ID: " << nodes_inorder_.front() << std::endl;
                    debug_log << "  - Last node ID: " << nodes_inorder_.back() << std::endl;
                    debug_log << "  - Total nodes: " << nodes_inorder_.size() << std::endl;
                    debug_log << "  - Area type: " << areatype << std::endl;
                    if(!tags_["name"].empty()) {
                        debug_log << "  - Area name: " << tags_["name"] << std::endl;
                    }
                    debug_log << "  - Level: " << tags_["level"] << std::endl;
                    continue;
                }

                // 创建并添加area
                Area::Ptr area_ptr = std::make_unique<Area>(way_id, way_attrs.clone(), areatype);
                area_ptr->nodes_inorder_ = nodes_inorder_;
                
                if(has_parent){
                    area_ptr->has_parent_ = true;
                    area_ptr->parent_ = parent_id;
                    graph.area_trees[parent_id].insert(way_id);
                }

                total_areas++;
                areas_by_level[tags_["level"]]++;
                graph.AddArea(std::move(area_ptr));
            }
            else if(tags_["osmAG:type"] == "passage"){
                // 将passage元素保存起来，留到第二阶段处理
                passage_elements.push_back(xml_node);
            }
        }
        else{
            xml_left_elements.push_back(xml_node);
        }
    }

    // 第二阶段：处理所有passages
    debug_log << "\nProcessing passages..." << std::endl;
    for(XMLElement* passage_node : passage_elements){
        WayAttributes way_attrs;
        std::map<std::string, std::string> tags_;
        std::vector<NodeId> nodes_inorder_;
        
        uint64_t way_id = atol(passage_node->Attribute("id"));
        way_attrs.action = passage_node->Attribute("action");
        way_attrs.visible = std::string(passage_node->Attribute("visible")) == "true";

        // 解析passage的tags和nodes
        for(XMLElement* sub_elem = passage_node->FirstChildElement(); sub_elem != NULL; sub_elem = sub_elem->NextSiblingElement()){
            if(std::string(sub_elem->Value()) == "tag"){
                const char* key = sub_elem->Attribute("k");
                const char* value = sub_elem->Attribute("v");
                if(key && value){
                    tags_[key] = value;
                }
            }
            else if(std::string(sub_elem->Value()) == "nd"){
                nodes_inorder_.emplace_back(atol(sub_elem->Attribute("ref")));
            }
        }

        // 验证passage节点数
        if(nodes_inorder_.size() != 2){
            debug_log << "WARNING: Passage " << way_id << " has invalid number of nodes: " << std::endl;
            debug_log << "Details for Passage " << way_id << ":" << std::endl;
            debug_log << "  - Expected: 2 nodes" << std::endl;
            debug_log << "  - Found: " << nodes_inorder_.size() << " nodes" << std::endl;
            debug_log << "  - Node IDs:";
            for(const auto& node_id : nodes_inorder_) {
                debug_log << " " << node_id;
            }
            debug_log << std::endl;
            if(!tags_["name"].empty()) {
                debug_log << "  - Passage name: " << tags_["name"] << std::endl;
            }
            if(!tags_["level"].empty()) {
                debug_log << "  - Level: " << tags_["level"] << std::endl;
            }
            debug_log << "  - From area: " << tags_["osmAG:from"] << std::endl;
            debug_log << "  - To area: " << tags_["osmAG:to"] << std::endl;
            continue;
        }

        // 创建passage
        PassageNodes nodes_id;
        nodes_id.source = nodes_inorder_[0];
        nodes_id.target = nodes_inorder_[1];

        AreaId from = stol(tags_["osmAG:from"]);
        AreaId to = stol(tags_["osmAG:to"]);

        // 此时所有area都已解析完成，可以安全地检查和添加parent关系
        if(graph.areas_.count(from) > 0 && graph.areas_.count(to) > 0){
            if(graph.areas_[from]->hasParent()){
                graph.passage_trees[graph.areas_[from]->parent_].insert(way_id);
            }
            if(graph.areas_[to]->hasParent()){
                graph.passage_trees[graph.areas_[to]->parent_].insert(way_id);
            }

            tags_.erase("osmAG:from");
            tags_.erase("osmAG:to");
            tags_.erase("osmAG:type");

            way_attrs.tags = tags_;
            Passage::Ptr passage_ptr = std::make_unique<Passage>(way_id, nodes_id, way_attrs.clone());
            passage_ptr->area_from = from;
            passage_ptr->area_to = to;

            debug_log << "Successfully added passage " << way_id 
                     << " connecting areas " << from << " and " << to << std::endl;

            graph.AddPassage(std::move(passage_ptr));
        }
        else {
            debug_log << "WARNING: Could not add passage " << way_id 
                     << ": Referenced areas not found (from=" << from << ", to=" << to << ")" << std::endl;
        }
    }

    // 输出统计信息
    debug_log << "\n========== Parsing Statistics ==========" << std::endl;
    debug_log << "Total ways processed: " << total_ways << std::endl;
    debug_log << "Total areas created: " << total_areas << std::endl;
    debug_log << "Total passages processed: " << passage_elements.size() << std::endl;

    debug_log << "\nAreas by level:" << std::endl;
    for(const auto& [level, count] : areas_by_level) {
        debug_log << "  Level " << level << ": " << count << std::endl;
    }

    debug_log << "\nParsing process completed at: " << std::time(nullptr) << std::endl;
    debug_log.close();

    success_parse = true;
    return success_parse;
}

void Save_AreaGraph2Osm(const AreaGraph& graph, const char* file_path){
    XMLDocument doc;
    /*add a declartion for xml* version="1.0" encoding=UTF-8*/
    XMLDeclaration* declar = doc.NewDeclaration();
    doc.InsertFirstChild(declar);
    /*creat root node*/
    XMLElement* root = doc.NewElement("osm");
    root->SetAttribute("version","0.6");
    root->SetAttribute("generator","AreaGraph");
    doc.InsertEndChild(root);
    
    for(auto it = graph.nodes_.begin(); it!= graph.nodes_.end(); it++){
        XMLElement* xml_node = doc.NewElement("node");
        xml_node->SetAttribute("id", it->first);
        auto attr = it->second->getAttributesPtr();
        xml_node->SetAttribute("action", attr->action.c_str());
        const char * vis = attr->visible ? "true": "false";
        xml_node->SetAttribute("visible", vis);
        xml_node->SetAttribute("lat", attr->geo_position.latitude);
        xml_node->SetAttribute("lon", attr->geo_position.longitude);

        /*test the xyz transform*/
        xml_node->SetAttribute("x", attr->position[0]);
        xml_node->SetAttribute("y", attr->position[1]);
        
        // printf("node ID = %ld\n", it->first);
        // if(!it->second->passage_belongto_.empty()){
        //     printf("belong passage%ld\n",*(it->second->passage_belongto_.begin()));
        // }

        // if(!it->second->area_belongto_.empty()){
        //     printf("belong area%ld\n",*(it->second->area_belongto_.begin()));
        // }


        root->InsertEndChild(xml_node);
    }
    
    for(auto it = graph.areas_.begin(); it!= graph.areas_.end(); it++){
        XMLElement* xml_node = doc.NewElement("way");
        
        xml_node->SetAttribute("id", it->first);
        auto attr = it->second->getAttributesPtr();
        xml_node->SetAttribute("action", attr->action.c_str());
        const char * vis = attr->visible ? "true": "false";
        xml_node->SetAttribute("visible", vis);

        for(auto ref_it = it->second->nodes_inorder_.begin(); ref_it != it->second->nodes_inorder_.end(); ref_it++){
            XMLElement* xml_ref = doc.NewElement("nd");
            xml_ref->SetAttribute("ref", *ref_it);/*attention int inversion!!!*/
            xml_node->InsertEndChild(xml_ref);
        }
        XMLElement* xml_tag = doc.NewElement("tag");
        xml_tag->SetAttribute("k", "osmAG:type");
        xml_tag->SetAttribute("v", "area");
        xml_node->InsertEndChild(xml_tag);
        xml_tag = doc.NewElement("tag");
        xml_tag->SetAttribute("k", "osmAG:areaType");
        xml_tag->SetAttribute("v", it->second->type.c_str());
        xml_node->InsertEndChild(xml_tag);
        if(it->second->has_parent_){
            xml_tag = doc.NewElement("tag");
            xml_tag->SetAttribute("k", "osmAG:parent");
            xml_tag->SetAttribute("v", it->second->parent_);
            xml_node->InsertEndChild(xml_tag);
        }
        for(auto tag_left_it = attr->tags.begin(); tag_left_it != attr->tags.end(); tag_left_it++){
            xml_tag = doc.NewElement("tag");
            xml_tag->SetAttribute("k", (tag_left_it->first).c_str());
            xml_tag->SetAttribute("v", (tag_left_it->second).c_str());
            xml_node->InsertEndChild(xml_tag);
        }
        root->InsertEndChild(xml_node);
    }

    for(auto it = graph.passages_.begin(); it!= graph.passages_.end(); it++){
        XMLElement* xml_node = doc.NewElement("way");
        
        xml_node->SetAttribute("id", it->first);
        auto attr = it->second->getAttributesPtr();
        xml_node->SetAttribute("action", attr->action.c_str());
        const char * vis = attr->visible ? "true": "false";
        xml_node->SetAttribute("visible", vis);

        
        XMLElement* xml_ref = doc.NewElement("nd");
        xml_ref->SetAttribute("ref", it->second->passage_nodes.source);
        xml_node->InsertEndChild(xml_ref);

        xml_ref = doc.NewElement("nd");
        xml_ref->SetAttribute("ref", it->second->passage_nodes.target);
        xml_node->InsertEndChild(xml_ref);

        XMLElement* xml_tag = doc.NewElement("tag");
        xml_tag->SetAttribute("k", "osmAG:type");
        xml_tag->SetAttribute("v", "passage");
        xml_node->InsertEndChild(xml_tag);
        xml_tag = doc.NewElement("tag");
        xml_tag->SetAttribute("k", "osmAG:from");
        xml_tag->SetAttribute("v", it->second->area_from);
        xml_node->InsertEndChild(xml_tag);
        xml_tag = doc.NewElement("tag");
        xml_tag->SetAttribute("k", "osmAG:to");
        xml_tag->SetAttribute("v", it->second->area_to);
        xml_node->InsertEndChild(xml_tag);

        for(auto tag_left_it = attr->tags.begin(); tag_left_it != attr->tags.end(); tag_left_it++){
            xml_tag = doc.NewElement("tag");
            xml_tag->SetAttribute("k", (tag_left_it->first).c_str());
            xml_tag->SetAttribute("v", (tag_left_it->second).c_str());
            xml_node->InsertEndChild(xml_tag);
        }
        root->InsertEndChild(xml_node);
    }

    doc.SaveFile(file_path);

}

/**
 * @brief Check the nodes in each area are all in the graph.
 * use area_belingto_ to record which areas each node belongs to within a given Areagraph 
 * @param graph Areagraph
*/
void TraverseArea(AreaGraph& graph){
    printf("Traversing Area...\n");
    printf("areas number: %ld\n", graph.areas_.size());
    // 遍历每一个 Area
    for(auto area = graph.areas_.begin(); area != graph.areas_.end(); area++){
        osm_ag::AreaId areaid = area->first;
        // 遍历这个 Area 中的 node，由于Area的 first_node 与 last_node 重合，因此为避免重复，遍历的终点为 end -1
        for(auto node = area->second->nodes_inorder_.begin(); node != (area->second->nodes_inorder_.end()-1); node++){
            // 检查：如果当前 node 存在于 graph.nodes_中
            if(graph.nodes_.count(*node) != 0){ // area_belongto_ looks useless
                // 如果存在，并且这个node的仍没有被标注为归属于哪一个area的话
                if(graph.nodes_[*node]->area_belongto_.count(areaid) == 0){
                    // 把当前的area_id (也即正在遍历的当前area的id) 赋值给这个node的属性，用以标识：这个node是属于这个area的
                    graph.nodes_[*node]->area_belongto_.insert(areaid);
                }
            }
            else{
                // 当前area的某个node在graph.nodes_集合中找不到 ==> graph不完整
                std::cout << "Can not find this node, graph is not complete!!!!!!" << std::endl;
            }
        }
    }
}


/**
 * @brief Check the nodes in each passage are all in the graph.
 * 
 * @param graph 
 */
void TraversePassage(AreaGraph& graph){
    printf("Traversing Passage...\n");
    // 遍历每一条 passage
    for(auto passage = graph.passages_.begin(); passage != graph.passages_.end(); passage++){
        PassageId passageId = passage->first;
        auto node1 = passage->second->passage_nodes.source; // node 1
        auto node2 = passage->second->passage_nodes.target; // node 2

        auto from = passage->second->area_from; // from this area
        auto to = passage->second->area_to; // to this area
        // std::map<AreaId, Area::Ptr> areas_;
        // 遍历每一条 passage 所指向的 两个area
        for(auto area_id : {from, to}){
            // 如果 graph 中确实存在这个area
            if(graph.areas_.count(area_id) != 0){// find this area
                // add (this passage and its two nodes) to the from area.
                graph.areas_[area_id]->passageids.insert(passageId); 
                graph.areas_[area_id]->passage_nodes.insert(std::make_pair(node1, node2));
                // 如果当前 area 还有父母
                if(graph.areas_[area_id]->hasParent()){
                    AreaId parent_id_from = graph.areas_[area_id]->parent_;
                    graph.passage_trees[parent_id_from].insert(passageId);
                }      
            }
            else{
                // 如果 graph 中找不到当前遍历的 area
                printf("Graph Parsing is not complete!!!!!!");
                printf("areaid is %ld\n", area_id);
            }
        }


        // if(graph.areas_.count(to) != 0){
        //     graph.areas_[to]->passageids.insert(passage->first);
        //     graph.areas_[to]->passage_nodes.insert(std::make_pair(passage->second->passage_nodes.source, passage->second->passage_nodes.target));
        //     if(graph.areas_[to]->hasParent()){
        //         AreaId parent_id_to = graph.areas_[to]->parent_;
        //         graph.passage_trees[parent_id_to].insert(passage->first);
        //     }
                
        // }else{
        //     printf("Graph Parsing is not complete!!!!!!");
        //     printf("areaid is %ld\n", to);

        // }
        Eigen::Vector3d center;

        if(graph.nodes_.count(node1) != 0){ // find this node
            center = graph.nodes_[node1]->attributes_->position;
            if(graph.nodes_[node1]->passage_belongto_.count(passageId) == 0){
                graph.nodes_[node1]->passage_belongto_.insert(passageId);
            }
        }else{
            printf("Graph is not complete!!!!!!");
            printf("nodeid is %ld\n", node1);

        }

        if(graph.nodes_.count(node2) != 0){
            center += graph.nodes_[node2]->attributes_->position;

            if(graph.nodes_[node2]->passage_belongto_.count(passageId) == 0){
                graph.nodes_[node2]->passage_belongto_.insert(passageId);
            }
        }else{
            printf("Graph is not complete!!!!!!");
            printf("nodeid is %ld\n", node2);
        }
        // printf("center of this passage %f\n",center[0]);

        center = center/2.0;
        passage->second->center_position = center; // the center of this passage
    }
    printf("Travering pass success!!!!\n");
}

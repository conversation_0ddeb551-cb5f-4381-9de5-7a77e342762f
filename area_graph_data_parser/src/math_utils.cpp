// Last Modifed by <PERSON><PERSON><PERSON> 24.10.29
// 数学工具和坐标转换函数

// 系统自带的头文件
#include <cmath>
#include <Eigen/Core>
#include <Eigen/Geometry> 
#include <vector>
// 自定义头文件
#include "math_utils.h"
using namespace osm_ag;

/**
 * @brief transform degree to radius
 * 
 * @param d 
 * @return double 
 */
double Deg2Rad(double d){
    return d * M_PI / 180.0;
}

/**
 * @brief transfrom WGS84(GPS) coordinate to XYZ coordinate
 * 
 * @param init reference point in long/lat form
 * @param cur point that needed to transform
 * @return Eigen::Vector3d, aka. in XYZ form
 */
Eigen::Vector3d CoordinateTransform(GeoCoordinate init, GeoCoordinate cur) {
    std::array<double, 2> reference{init.latitude, init.longitude};
    std::array<double, 2> currrent{cur.latitude, cur.longitude};
    std::array<double, 2> cur_xy;

    // 使用WGS84转换，不考虑旋转
    cur_xy = wgs84::toCartesian(reference, currrent);
    
    // 简单处理高度差，如果没有高度信息就默认为0
    double z = (cur.altitude - init.altitude);
    
    return {cur_xy[0], cur_xy[1], z};
}


/**
 * @brief fcompute path's distance, for comparison to show SOTA
 * 
 * @param path_pts 
 * @return double 
 */
double ComputPathDistance(const std::vector<Eigen::Vector3d>& path_pts){
    Eigen::Vector3d point_last;
    int i = 0;
    double result_dist = 0;
    for(Eigen::Vector3d pt:path_pts){
        if(i > 0){
            result_dist += (pt - point_last).norm();
        }
        point_last = pt;
        i++;
    }
    return result_dist;
}
// Last Modifed by <PERSON><PERSON><PERSON> 24.10.29
// 路径规划和图结构处理
// 使用 path_graph 进行实际的路径规划

#include "graph_planning.h"
#include "visualization_utils.h"
#include "math_utils.h"

using namespace osm_ag;
/**
 * @brief 在 main 流程中没有用到
 * 
 * @param graph 
 * @param parent_areaid 
 * @param start_id 
 * @param end_id 
 */
void PlanInStructure(AreaGraph& graph, AreaId parent_areaid, PassageId start_id, PassageId end_id){
    printf("Start to build the PathGraph for each layer!!!\n");
    if(graph.area_trees.find(parent_areaid)==graph.area_trees.end()){
        printf("This is not an correct Structure!!!!!!!\n");
        return;
    }else{
        printf("parent_area is %ld\n",graph.areas_[parent_areaid]->id);
    }

    PathLayer layer1(graph,parent_areaid);
    printf("Success Build the PathGraph for each layer!!!\n");

    if(layer1.getPathnodes().find(start_id)==layer1.getPathnodes().end()){
        printf("This layer doesn't include this start passage!!!\n");
        return;
    }
    if(layer1.getPathnodes().find(end_id)==layer1.getPathnodes().end()){
        printf("This layer doesn't include this end passage!!!\n");
        return;
    }
    layer1.AddEdge_AStar(start_id,end_id);

    printf("The shortest path from %ld to %ld is : ", start_id, end_id);
    auto p = layer1.getPaths()[0]->path;   
    // for(auto pp=p.rbegin(); pp !=p.rend(); ++pp){
    //     printf("%ld -> ",*pp);
    // }
    for(auto pp=p.rbegin(); pp !=p.rend(); ++pp){
        printf("%s -> ",graph.passages_[*pp]->info->tags["name"].c_str());
    }
    printf("\n");
}

// TODO 重点必看，因为这里涉及是否用了 pre_computed_path
/**
 @brief Get the path in the osmag
 @param graph : areagraph
 @param start_id : start passage id
 @param end_id : end passage id
 @param path_result : path result in osmag
*/
void PlanInOSM(AreaGraph& graph, PassageId start_id, PassageId end_id, std::vector<Eigen::Vector3d>& path_result){
    //Check the start passage id and end passage id is in this area.
    if(graph.passages_.find(start_id) == graph.passages_.end()){
        printf("ERROR: The start point id is not in this graph!!!");
        return;
    }
    if(graph.passages_.find(end_id) == graph.passages_.end()){
        printf("ERROR: The end point id is not in this graph!!!");
        return;
    }
    PassageGraph passage_graph;
    PathGraph path_graphs(graph,passage_graph);
    path_graphs.AddEdge_AStar(start_id, end_id);
    auto p = path_graphs.getPaths()[0]->path;
    for(auto pp=p.rbegin(); pp !=p.rend(); ++pp){ // rbegin表示反向遍历
        printf("%s -> ",graph.passages_[*pp]->info->tags["name"].c_str());
    }
    printf("\n");

    // std::vector<std::vector<Eigen::Vector3d>> path_result;
    std::vector<Eigen::Vector3d> path_temp;

    // geometry_msgs::PoseStamped path_res; 
    // path_res.header.frame_id

    //Transform  topo to metric path
    if(p.size()>1){
        std::reverse(p.begin(), p.end());
        PassageId from = p[0];
        for(auto it = p.begin()+1; it != p.end(); it++){
            std::pair<PassageId, PassageId> edge_(std::make_pair(from, *it));
            double level_h = stod(graph.passages_[from]->info->tags["level"]);
            if(passage_graph.pathedges_trj.count(edge_) != 0){
                path_temp = passage_graph.pathedges_trj[edge_];
                for(auto& point:path_temp){
                    point[2] = level_h;
                }
                path_result.insert(path_result.end(),path_temp.begin(),path_temp.end());
            }
            else{
                printf("ERROR!!!!!!Cannot output the metric path!!!\n");
                break;
            }
            from = *it;
        }

        // double dist_path = ComputPathDistance(path_result);
        // printf("The path distance using OSM-AG: %f m.\n", dist_path);
    }
// }
    /*如果是用L2或者L1进行的计算，这里需要拼接A*的； 当前只是为了计算耗时，因为计算path_graph的时候就已经算过了这一部分
      之后需要进行修改
    // */
    // if(p.size()>1){
    //     //前面的p已经反转过了
    //     timeval start_time_test, stop_time_test;//us级别
    //     long elapsed_us_test = 0;
    //     int count = 1;

    //     PassageId last = p[0];
    //     for(auto it = p.begin()+1; it != p.end(); it++){
    //         //首先去找这两个passage在哪一个area当中
    //         AreaId common_area = 0;
    //         AreaId last_area_1 = graph.passages_[last]->area_from;
    //         AreaId last_area_2 = graph.passages_[last]->area_to;
    //         AreaId current_area_1 = graph.passages_[*it]->area_from;
    //         AreaId current_area_2 = graph.passages_[*it]->area_to;

    //         if((last_area_1 == current_area_1) | (last_area_1 == current_area_2)){
    //             common_area = last_area_1;
    //         }
    //         if((last_area_2 == current_area_1) | (last_area_2 == current_area_2)){
    //             common_area = last_area_2;
    //         }
    //         if(common_area==0){
    //             printf("CANNOT find the common area!!!\n");//一般不会出现这种情况。
    //             return;
    //         }
    //         Eigen::Vector3d center_position = graph.areas_[common_area]->center_position;

    //         GridMat grid_mat_ = graph.areas_[common_area]->grid_mat_;

    //         Eigen::Vector3d pi = graph.passages_[last]->center_position;
    //         Eigen::Vector3d pj = graph.passages_[*it]->center_position;
    //         int x_i = (pi[0] - center_position[0])/grid_mat_.resolution + grid_mat_.origin_grid_x + grid_mat_.offset;
    //         int y_i = (pi[1] - center_position[1])/grid_mat_.resolution + grid_mat_.origin_grid_y + grid_mat_.offset;
    //         int x_j = (pj[0] - center_position[0])/grid_mat_.resolution + grid_mat_.origin_grid_x + grid_mat_.offset;
    //         int y_j = (pj[1] - center_position[1])/grid_mat_.resolution + grid_mat_.origin_grid_y + grid_mat_.offset;
    //         cv::Point2i point_i(x_i,y_i);
    //         cv::Point2i point_j(x_j,y_j);
    //         // double res_dis;
    //         vector<int> dis_result;
	//         std::vector<std::vector<int>> sites;
    //         for (int col = 0; col < grid_mat_.grid_mat.cols; col++) {
	// 	        std::vector<int> rows;
	// 	        for (int row = 0; row < grid_mat_.grid_mat.rows; row++) {
	// 		        int color = grid_mat_.grid_mat.at<uchar>(row, col);
	// 		        rows.push_back(color == 255 ? 0 : 1);//白色的点是障碍点？？？
	// 	        }
	// 	        sites.push_back(rows);
	//         }
    //         AStarCalc calc = AStarCalc();
    //         calc.InitSites(sites);
    //         CalcPt startpt = CalcPt(point_i);
	//         CalcPt endpt = CalcPt(point_j);

    //         gettimeofday(&start_time_test, NULL);

	//         vector<CalcPt* > reslist = calc.GetPath(startpt, endpt, dis_result);

    //         gettimeofday(&stop_time_test, NULL);

    //         elapsed_us_test += (stop_time_test.tv_sec - start_time_test.tv_sec)*1000000 + (stop_time_test.tv_usec - start_time_test.tv_usec);

    //         last = *it;
    //         printf("%d...", count);
    //         count++;
    //     }
    //     printf("\nThe A* duration in OSM-AG: %ld us.\n", elapsed_us_test);
    // }
}

/**
 * @brief just compare to 2d Grid map using A*
 * @param graph which the file parse to
 **/
void PlanInGridMap(AreaGraph& graph, PassageId start_id, PassageId end_id, cv::Mat& grid_map, double resolution, bool if_show){
    //built gird map for one layer. 这里暂时不考虑level，不对多层做处理 //注意只能对底层叶节点进行！！！
    cv::Mat gridmap_;
    cv::cvtColor(grid_map,gridmap_,cv::COLOR_BGR2GRAY);

    //First we need to judge if the start and end points exist.
    if(graph.passages_.find(start_id) == graph.passages_.end()){
        printf("ERROR: The start point id is not in this graph!!!");
        return;
    }
    if(graph.passages_.find(end_id) == graph.passages_.end()){
        printf("ERROR: The end point id is not in this graph!!!");
        return;
    }
    Eigen::Vector3d pos_s, pos_e;
    pos_s = graph.passages_[start_id]->center_position;
    pos_e = graph.passages_[end_id]->center_position;
    cv::Point2i pt_s, pt_e;
    pt_s = XYZ2Grid(pos_s);
    pt_e = XYZ2Grid(pos_e);

    cv::Mat srccpy = gridmap_.clone();

    double res_distance = 0.0;
    std::vector<Eigen::Vector3d> res_xyz;
    std::vector<cv::Point2i> resPoints;//注意这里是倒序的点，终点到起点。
    double dis_result;
	std::vector<std::vector<int>> sites;

    for (int col = 0; col < gridmap_.cols; col++) {
		std::vector<int> rows;
		for (int row = 0; row < gridmap_.rows; row++) {
			int color = gridmap_.at<uchar>(row, col);
			rows.push_back(color == 0 ? 1 : 0); // if white, = 0
		}
		sites.push_back(rows);
	}
    AStarCalc calc = AStarCalc();
    calc.InitSites(sites);
    CalcPt startpt = CalcPt(pt_s);
	CalcPt endpt = CalcPt(pt_e);
	vector<CalcPt* > reslist = calc.findPath(startpt, endpt, dis_result);

	for (auto p : reslist) {
		resPoints.push_back(p->pt);//注意这里是倒序的点，终点到起点。
        // cv::circle(srccpy, p->pt, 1, cv::Scalar(0,0,0));
	}
    //test the performance of a* in grid map
    if(resPoints.size()>0){
        reverse(resPoints.begin(), resPoints.end());
        if(if_show){
            res_xyz.push_back(Grid2XYZ_vis(resPoints[0]));

            for(std::size_t j=0; j<resPoints.size()-1; j++){
                int k = j+1;
                res_xyz.push_back(Grid2XYZ_vis(resPoints[k]));
            }
            PlotPath(res_xyz, grid_map, 0.1, false);
        }
        res_distance = dis_result;
        double dist_path = ComputPathDistance(res_xyz);
        printf("The path distance using grid_map: %f m; the appro dist: %f m.\n", dist_path, res_distance);
             
    }else{
        printf("ERR: CANNOT ARRIVE!!!\n");
        res_distance = std::numeric_limits<double>::max();
    }
    cv::imwrite("/home/<USER>/agilex_ws/src/area_graph_data_parser/data/show_path_compare.png", grid_map);

}

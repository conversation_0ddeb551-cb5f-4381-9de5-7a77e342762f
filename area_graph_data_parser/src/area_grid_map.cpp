#include "area_grid_map.h"

namespace osm_ag {

/**
 * @brief 计算区域的几何中心
 * @param graph 区域图
 * @param area_id 待计算的区域ID
 * @details 通过计算区域边界点的平均位置得到几何中心,不包括最后一个重复点
 */
void ComputeCenter_Area(AreaGraph& graph, AreaId area_id) {
    Eigen::Vector3d center_position(0.0, 0.0, 0.0);
    int num = 0;
    const std::vector<NodeId>& nodeids = graph.areas_[area_id]->nodes_inorder_;

    // 计算所有节点的平均位置
    for(auto it = nodeids.begin(); it != nodeids.end()-1; it++) {
        center_position += graph.nodes_[*it]->attributes_->position;
        num++;
    }

    // 更新区域中心信息
    if(num == 0) {
        printf("ERR:This area has no node!");
        graph.areas_[area_id]->init_center = false;
    } else {
        center_position /= num;
        graph.areas_[area_id]->init_center = true;
        graph.areas_[area_id]->center_position = center_position;
    }
}

/**
 * @brief 初始化区域的占据栅格地图
 * @param graph 区域图
 * @param area_id 待处理的区域ID
 * @param resolution 栅格分辨率(米/像素)
 * @param offset 地图边界扩展像素数
 */
void InitOccupancyMap_Area(AreaGraph& graph, AreaId area_id, const double resolution, const int offset) {
    auto& area = graph.areas_[area_id];
    
    // 初始化栅格地图参数
    area->area_occupancy.info.resolution = resolution;
    if(!area->init_center) {
        ComputeCenter_Area(graph, area_id);
    }
    const Eigen::Vector3d& center_position = area->center_position;

    // 设置栅格地图原点和方向
    auto& origin = area->area_occupancy.info.origin;
    origin.position.x = center_position[0];
    origin.position.y = center_position[1];
    origin.position.z = center_position[2];
    origin.orientation.y = 0.0;
    origin.orientation.z = 0.0;
    origin.orientation.w = 1.0;

    // 计算区域边界
    double min_x = center_position[0], max_x = center_position[0];
    double min_y = center_position[1], max_y = center_position[1];
    const std::vector<NodeId>& nodeids = area->nodes_inorder_;
    for(auto it = nodeids.begin(); it != nodeids.end()-1; it++) {
        const auto& pos = graph.nodes_[*it]->attributes_->position;
        min_x = std::min(min_x, pos[0]);
        max_x = std::max(max_x, pos[0]);
        min_y = std::min(min_y, pos[1]);
        max_y = std::max(max_y, pos[1]);
    }

    // 计算栅格尺寸
    int rows = std::ceil((center_position[1]-min_y)/resolution) + 
               std::ceil((max_y - center_position[1])/resolution) + offset*2;
    int columns = std::ceil((center_position[0]-min_x)/resolution) + 
                 std::ceil((max_x - center_position[0])/resolution) + offset*2;
    int origin_row = std::ceil((center_position[1]-min_y)/resolution);
    int origin_column = std::ceil((center_position[0]-min_x)/resolution);

    // 创建栅格地图
    cv::Mat area_mat(rows, columns, CV_8UC1, cv::Scalar(125));  // 初始化为未知区域(125)

    // 绘制区域轮廓并填充内部
    int i = 0;
    cv::Point2i points_2d_last;
    std::vector<std::vector<cv::Point2i>> contour_pts(1);
    NodeId node_id_last;
    for(auto node_id : nodeids) {
        int x = (graph.nodes_[node_id]->attributes_->position[0] - center_position[0])/resolution 
              + origin_column + offset;
        int y = (graph.nodes_[node_id]->attributes_->position[1] - center_position[1])/resolution 
              + origin_row + offset;
        cv::Point2i points_2d(x,y);
        if(i > 0) {
            contour_pts[0].push_back(points_2d);
        }
        points_2d_last = points_2d;
        i++;
    }

    // 填充区域内部为可通行区域(白色)
    cv::drawContours(area_mat, contour_pts, -1, cv::Scalar(255), CV_FILLED);

    // 绘制非通道的边界为障碍物
    i = 0;
    for(auto node_id : nodeids) {
        int x = (graph.nodes_[node_id]->attributes_->position[0] - center_position[0])/resolution 
              + origin_column + offset;
        int y = (graph.nodes_[node_id]->attributes_->position[1] - center_position[1])/resolution 
              + origin_row + offset;
        cv::Point2i points_2d(x,y);
        if(i > 0) {
            auto p = std::make_pair(node_id, node_id_last);
            auto p_i = std::make_pair(node_id_last, node_id);
            if(!area->passage_nodes.count(p) && !area->passage_nodes.count(p_i)) {
                cv::line(area_mat, points_2d_last, points_2d, cv::Scalar(0), 2);
            }
        }
        points_2d_last = points_2d;
        node_id_last = node_id;
        i++;
    }

    // 绘制通道
    for(auto passage : area->passageids) {
        const auto& nodes = graph.passages_[passage]->passage_nodes;
        const auto& pi = graph.nodes_[nodes.source]->attributes_->position;
        const auto& pj = graph.nodes_[nodes.target]->attributes_->position;
        Eigen::Vector3d pm = (pi + pj) / 2.0;

        // 转换为栅格坐标
        int x_i = (pi[0] - center_position[0])/resolution + origin_column + offset;
        int y_i = (pi[1] - center_position[1])/resolution + origin_row + offset;
        int x_j = (pj[0] - center_position[0])/resolution + origin_column + offset;
        int y_j = (pj[1] - center_position[1])/resolution + origin_row + offset;
        int x_m = (pm[0] - center_position[0])/resolution + origin_column + offset;
        int y_m = (pm[1] - center_position[1])/resolution + origin_row + offset;
        
        cv::Point2i point_i(x_i, y_i);
        cv::Point2i point_j(x_j, y_j);
        cv::Point2i point_m(x_m, y_m);
        
        // 绘制通道
        cv::line(area_mat, point_i, point_j, cv::Scalar(255), 2);
        cv::circle(area_mat, point_m, 4, cv::Scalar(255), CV_FILLED);
    }

    // 保存栅格信息
    area->grid_mat_.grid_mat = area_mat.clone();
    area->grid_mat_.origin_grid_x = origin_column;
    area->grid_mat_.origin_grid_y = origin_row;
    area->grid_mat_.offset = offset;
    area->grid_mat_.resolution = resolution;

    // 转换为ROS占据栅格地图消息
    area->area_occupancy.info.width = columns;
    area->area_occupancy.info.height = rows;
    area->area_occupancy.data.resize(columns * rows);

    // 填充占据栅格数据
    for(int i = 0; i < columns; i++) {
        for(int j = 0; j < rows; j++) {
            uint8_t pixel = area_mat.at<uint8_t>(j,i);
            int8_t& cell = area->area_occupancy.data[j*columns+i];
            
            if(pixel == 125)      cell = -1;  // 未知区域
            else if(pixel == 255) cell = 0;   // 可通行区域
            else if(pixel == 0)   cell = 100; // 障碍物
        }
    }

    area->use_occupancy_map = true;
}

} // namespace osm_ag
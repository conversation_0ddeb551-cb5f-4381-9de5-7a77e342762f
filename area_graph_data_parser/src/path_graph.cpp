#include "path_graph.h"
#include "area_grid_map.h"

namespace osm_ag {

/**
 * @brief 计算两点间A*路径的距离
 * @param grids 栅格地图
 * @param pt_s 起点栅格坐标
 * @param pt_e 终点栅格坐标
 * @param resPoints 存储路径点序列
 * @param if_show 是否显示结果
 * @return 路径距离
 */
double ComputeAstarDis(const GridMat& grids, cv::Point2i pt_s, cv::Point2i pt_e, 
                      std::vector<cv::Point>& resPoints, bool if_show = false) {
    // 构建sites矩阵(0表示可通行，1表示障碍)
    std::vector<std::vector<int>> sites;
    for (int col = 0; col < grids.grid_mat.cols; col++) {
        std::vector<int> rows;
        for (int row = 0; row < grids.grid_mat.rows; row++) {
            int color = grids.grid_mat.at<uchar>(row, col);
            rows.push_back(color == 255 ? 0 : 1);  // 白色区域可通行
        }
        sites.push_back(rows);
    }

    // 执行A*搜索
    AStarCalc calc;
    calc.InitSites(sites);
    double dis_result;
    vector<CalcPt*> reslist = calc.findPath(CalcPt(pt_s), CalcPt(pt_e), dis_result);

    // 提取路径点
    for (auto p : reslist) {
        resPoints.push_back(p->pt);
    }

    // 可视化结果
    if(if_show && !resPoints.empty()) {
        cv::Mat srccpy = grids.grid_mat.clone();
        cv::circle(srccpy, pt_s, 2, cv::Scalar(75));
        cv::circle(srccpy, pt_e, 2, cv::Scalar(75));
        
        // 绘制路径
        for(size_t j = 0; j < resPoints.size()-1; j++) {
            cv::line(srccpy, resPoints[j], resPoints[j+1], cv::Scalar(200), 1);
        }
        cv::imwrite("../data/path_in_one_area.png", srccpy);
        return dis_result;
    }

    // 处理规划失败情况
    if(resPoints.empty()) {
        printf("ERR: CANNOT ARRIVE!!!\n");
        return std::numeric_limits<double>::max();
    }
    return dis_result;
}

/**
 * @brief 栅格坐标转世界坐标
 */
Eigen::Vector3d Grid2XYZ(int x_grid, int y_grid, Eigen::Vector3d center_position, 
                        double resolution, int origin_x, int origin_y, int offset) {
    return Eigen::Vector3d(
        (x_grid - offset - origin_x) * resolution + center_position[0],
        (y_grid - offset - origin_y) * resolution + center_position[1],
        center_position[2]
    );
}

/**
 * @brief 寻找区域内通道间的路径（无轨迹版本）
 */
void Find_PathInArea(AreaGraph& graph, AreaId area_id, std::vector<PathEdge*>& pathedges) {
    // 检查区域是否存在
    if(graph.areas_.find(area_id) == graph.areas_.end()) {
        printf("The wrong area id is %ld\n", area_id);
        return;
    }

    auto& area = graph.areas_[area_id];
    auto path_in_area = area->passageids;        // 该区域的所有通道
    auto path_in_area_temp = path_in_area;       // 临时拷贝用于遍历

    // 至少需要两个通道才能形成连接
    if(path_in_area.size() < 2) return;

    printf("The current area id is %ld\n", area_id);
    
    // 遍历所有通道对，建立连接
    for(auto pathit_i = path_in_area.begin(); pathit_i != path_in_area.end(); pathit_i++) {
        std::unordered_set<AreaId> areas_i;      // 存储第一个通道连接的区域
        path_in_area_temp.erase(*pathit_i);      // 从临时集合中移除已处理的通道
        
        // 获取第一个通道连接的区域
        areas_i.insert(graph.passages_[*pathit_i]->area_from);
        areas_i.insert(graph.passages_[*pathit_i]->area_to);

        // 与剩余通道建立连接
        for(auto pathit_j = path_in_area_temp.begin(); pathit_j != path_in_area_temp.end(); pathit_j++) {
            AreaId idj_from = graph.passages_[*pathit_j]->area_from;
            AreaId idj_to = graph.passages_[*pathit_j]->area_to;
            
            // 检查两个通道是否连接相同的区域
            bool same_areas = areas_i.count(idj_from) && areas_i.count(idj_to);
            if(same_areas) {
                printf("This two passages %ld and %ld are similar!!!\n", *pathit_i, *pathit_j);
                continue;
            }

            // 创建新的路径边
            auto* edge = new PathEdge();
            Eigen::Vector3d pi = graph.passages_[*pathit_i]->center_position;
            Eigen::Vector3d pj = graph.passages_[*pathit_j]->center_position;
            
            edge->from = *pathit_i;
            edge->to = *pathit_j;
            edge->dist = (pi-pj).norm();
            edge->area_id_belongto = area_id;
            
            pathedges.push_back(edge);
        }
    }
}


/**
 * @brief 寻找区域内通道间的路径（带轨迹版本）
 * @param graph 区域图
 * @param area_id 区域ID
 * @param pathedges 存储路径边
 * @param edges_trj_map 存储路径轨迹
 */
void Find_PathInArea(AreaGraph& graph, AreaId area_id, 
                    std::vector<PathEdge*>& pathedges, 
                    PathedgesTrjMap& edges_trj_map) {
    // 确保区域有占据栅格地图
    if(!graph.areas_[area_id]->use_occupancy_map) {
        InitOccupancyMap_Area(graph, area_id, 0.1);
    }

    auto& area = graph.areas_[area_id];
    auto path_in_area = area->passageids;      // 区域内所有通道
    auto path_in_area_temp = path_in_area;     // 临时拷贝
    const auto& center_position = area->center_position;
    const auto& grid_mat_ = area->grid_mat_;
    
    // 至少需要两个通道才能形成连接
    if(path_in_area.size() < 2) return;
    printf("The current area id is %ld\n", area_id);

    // 遍历所有通道对
    for(auto pathit_i = path_in_area.begin(); pathit_i != path_in_area.end(); pathit_i++) {
        std::unordered_set<AreaId> areas_i;
        path_in_area_temp.erase(*pathit_i);
        
        // 获取第一个通道连接的区域
        const auto& passage_i = graph.passages_[*pathit_i];
        areas_i.insert(passage_i->area_from);
        areas_i.insert(passage_i->area_to);
        
        // 与剩余通道建立连接
        for(auto pathit_j = path_in_area_temp.begin(); pathit_j != path_in_area_temp.end(); pathit_j++) {
            const auto& passage_j = graph.passages_[*pathit_j];
            
            // 检查是否连接相同区域
            bool same_areas = areas_i.count(passage_j->area_from) && areas_i.count(passage_j->area_to);
            if(same_areas) {
                printf("This two passages %ld and %ld are similar!!!\n", *pathit_i, *pathit_j);
                continue;
            }

            // 创建新的路径边
            auto* edge = new PathEdge();
            const auto& pi = passage_i->center_position;
            const auto& pj = passage_j->center_position;

            // 转换为栅格坐标
            cv::Point2i point_i(
                (pi[0] - center_position[0])/grid_mat_.resolution + grid_mat_.origin_grid_x + grid_mat_.offset,
                (pi[1] - center_position[1])/grid_mat_.resolution + grid_mat_.origin_grid_y + grid_mat_.offset
            );
            cv::Point2i point_j(
                (pj[0] - center_position[0])/grid_mat_.resolution + grid_mat_.origin_grid_x + grid_mat_.offset,
                (pj[1] - center_position[1])/grid_mat_.resolution + grid_mat_.origin_grid_y + grid_mat_.offset
            );

            // 计算路径
            std::vector<cv::Point2i> grid_path;
            std::vector<Eigen::Vector3d> trj_astar_inv;
            edge->dist = ComputeAstarDis(grid_mat_, point_i, point_j, grid_path, false);

            // 转换路径点到世界坐标
            if(!grid_path.empty()) {
                for(const auto& pt : grid_path) {
                    trj_astar_inv.emplace_back(Grid2XYZ(pt.x, pt.y, center_position, 
                                             grid_mat_.resolution, grid_mat_.origin_grid_x, 
                                             grid_mat_.origin_grid_y, grid_mat_.offset));
                }
            } else {
                // 对于电梯等特殊情况，直接使用直线连接
                edge->dist = (pi-pj).norm();
                trj_astar_inv.emplace_back(pi);
                trj_astar_inv.emplace_back(pj);
            }

            // 设置边的属性
            edge->from = *pathit_i;
            edge->to = *pathit_j;
            edge->area_id_belongto = area_id;
            pathedges.push_back(edge);

            // 存储双向轨迹
            edges_trj_map[std::make_pair(*pathit_j, *pathit_i)] = trj_astar_inv;
            std::reverse(trj_astar_inv.begin(), trj_astar_inv.end());
            edge->trj_astar = trj_astar_inv;
            edges_trj_map[std::make_pair(*pathit_i, *pathit_j)] = edge->trj_astar;
        }
    }
}

/**
 * @brief PathGraph构造函数
 * 遍历区域图构建通道图，处理最底层区域
 * 需要搜索找到叶节点，不是某个区域的父亲
 */
PathGraph::PathGraph(AreaGraph& graph, PassageGraph& passage_graph) {
    for(const auto& [area_id, area_ptr] : graph.areas_) {
        // 检查是否是叶节点(不是任何区域的父节点)
        if(graph.area_trees.count(area_id) == 0) {
            // 构建该区域内的通道连接
            Find_PathInArea(graph, area_id, pathedges, pathedges_trj);
            
            // 添加到通道图
            passage_graph.pathedges.insert(passage_graph.pathedges.end(), 
                                         pathedges.begin(), pathedges.end());
            passage_graph.pathedges_trj.insert(pathedges_trj.begin(), 
                                             pathedges_trj.end());

            // 创建通道节点
            for(PassageId passage_id : area_ptr->passageids) {
                auto* path = new PathNode();
                if(path->previous != nullptr) {
                    printf("Err: initial pathnode's pre is not NULL! The pre passage id is %ld\n", 
                           path->passage_id);
                }
                path->passage_id = passage_id;
                path->position = graph.passages_[passage_id]->center_position;
                pathnodes[passage_id] = path;
                passage_graph.pathnodes[passage_id] = path;
            }
        } else {
            area_ptr->is_leaf = false;
        }
    }

    printf("The Number of edges of the whole passage graph: %ld\n", pathedges.size());
    std::ofstream out;
    PrintEdgesDistance(graph, out);
}


// ... (继续后面的代码)

/**
 * @brief PathLayer构造函数
 * @details 初始化特定层级的路径图，处理该层级内的通道连接
 * @param graph 区域图
 * @param parent_area_id 父区域ID
 */
PathLayer::PathLayer(AreaGraph& graph, AreaId parent_area_id) 
    : parent_area_id(parent_area_id) {
    // 获取该层级的所有通道
    const auto& passages_layer = graph.passage_trees[parent_area_id];
    printf("size of passages_layer %ld\n", passages_layer.size());

    // 为每个通道创建路径节点
    for(PassageId passage_id : passages_layer) {
        auto* path = new PathNode();
        if(path->previous != nullptr) {
            printf("Err: initial pathnode's pre is not NULL! The pre passage id is %ld\n", 
                   path->passage_id);
        }
        path->passage_id = passage_id;
        path->position = graph.passages_[passage_id]->center_position;
        pathnodes[passage_id] = path;
    }

    // 处理该层级内所有区域的通道连接
    const auto& areas_layer = graph.area_trees[parent_area_id];
    for(AreaId area_id : areas_layer) {
        Find_PathInArea(graph, area_id, pathedges);
    }
    printf("pathedges counts %ld\n", pathedges.size());
}

/**
 * @brief 使用A*算法在路径图中搜索最短路径
 * @param start_id 起始通道ID
 * @param end_id 目标通道ID
 */
void PathBase::AddEdge_AStar(PassageId start_id, PassageId end_id) {
    // 初始化所有节点的规划状态
    printf("pathnodes size: %ld\n", pathnodes.size());
    for(auto& [id, node] : pathnodes) {
        node->InitForPlanning();
    }

    // 创建路径记录
    auto* result_path = new Path(start_id, end_id);

    // 初始化开始节点
    PathNode* start_vertex = pathnodes[start_id];
    start_vertex->g_value = 0;
    start_vertex->update_f();
    
    // A*搜索相关的数据结构
    std::priority_queue<PathNode*> open_set;             // 开放列表（优先队列）
    std::unordered_set<PathNode*> closed_set;           // 关闭列表
    open_set.push(start_vertex);
    start_vertex->is_open = true;

    // A*主循环
    while(!open_set.empty()) {
        // 获取f值最小的节点
        PathNode* current = open_set.top();
        printf("current node id: %ld\n", current->passage_id);
        open_set.pop();
        
        // 更新节点状态
        closed_set.insert(current);
        current->is_closed = true;
        current->is_open = false;

        // 检查是否到达目标
        if(current->passage_id == end_id) {
            printf("Reached target node\n");
            result_path->path.push_back(end_id);

            // 回溯构建路径
            PathNode* pre = current->previous;
            while(pre != nullptr) {
                if(pre->passage_id != 0) {
                    result_path->path.push_back(pre->passage_id);
                }
                pre = pre->previous;
            }

            // 验证路径有效性
            if(result_path->path.back() == start_id) {
                printf("Found valid path successfully!\n");
                paths.push_back(result_path);
            } else {
                printf("Failed to find valid path!\n");
            }
            break;
        }

        // 扩展当前节点
        std::vector<std::pair<PassageId, double>> neighbors;
        FindNeighbors(neighbors, current->passage_id);
        
        if(neighbors.empty()) {
            printf("No neighbors found for current node\n");
            continue;
        }

        // 处理所有邻居节点
        for(const auto& [neighbor_id, cost] : neighbors) {
            PathNode* neighbor = pathnodes[neighbor_id];
            double new_g_value = current->g_value + cost;

            // 节点在开放列表中
            if(neighbor->is_open) {
                if(new_g_value < neighbor->g_value) {
                    neighbor->g_value = new_g_value;
                    neighbor->previous = current;
                    neighbor->update_f();
                }
            }
            // 节点在关闭列表中
            else if(neighbor->is_closed) {
                continue;
            }
            // 新节点
            else {
                neighbor->previous = current;
                neighbor->g_value = new_g_value;
                neighbor->update_f();
                neighbor->is_open = true;
                open_set.push(neighbor);
            }
        }
    }
}

/**
 * @brief 查找节点的邻居
 * @param subs [out] 存储邻居节点及对应代价
 * @param current_id 当前节点ID
 */
void PathBase::FindNeighbors(std::vector<std::pair<PassageId, double>>& subs, 
                           PassageId current_id) {
    for(const auto* edge : pathedges) {
        if(current_id == edge->from) {
            subs.emplace_back(edge->to, edge->dist);
        }
        else if(current_id == edge->to) {
            subs.emplace_back(edge->from, edge->dist);
        }
    }
    printf("neighbors count: %ld\n", subs.size());
}

/**
 * @brief 打印边的距离信息到文件
 */
void PathBase::PrintEdgesDistance(AreaGraph& graph, std::ofstream& outfile) {
    if(!outfile.is_open()) {
        outfile.open("distance_check.txt", std::ios::in);
    }

    // 写入时间戳
    time_t t = time(nullptr);
    struct tm* now = localtime(&t);
    std::stringstream time;
    time << now->tm_year + 1900 << "年"
         << now->tm_mon + 1 << "月"
         << now->tm_mday << "日 "
         << now->tm_hour << ":"
         << now->tm_min << ":"
         << now->tm_sec;
    outfile << "This file is recorded at " << time.str() << "!!!!" << std::endl;

    // 写入边的信息
    for(const auto* edge : pathedges) {
        const auto& passage_from = graph.passages_[edge->from];
        const auto& passage_to = graph.passages_[edge->to];
        
        outfile << "Edge!!! "
                << "from id: " << edge->from 
                << "(name: " << passage_from->info->tags["name"] << "); "
                << "to id: " << edge->to 
                << "(name: " << passage_to->info->tags["name"] << "); "
                << "Distance: " << edge->dist << std::endl;
    }
}

} // namespace osm_ag
/**
 * @file topology_publisher_node.cpp
 * @brief 拓扑地图发布节点，处理和发布Area Graph地图数据
 * <AUTHOR>
 * @date 2024-11-26
 */

#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <geometry_msgs/msg/point32.hpp>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>
#include "area_graph_data_parser/msg/a_gindex.hpp"
#include "area_graph_data_parser/msg/area_index.hpp"
#include "graph_data_handler.h"
#include "visualization_utils.h"

class TopologyPublisher : public rclcpp::Node {
public:
    TopologyPublisher() : Node("topology_publisher") {
        // 初始化PCL点云
        cloud_.reset(new pcl::PointCloud<pcl::PointXYZI>());
        cloud_->header.frame_id = "AGmap";

        // 声明参数
        this->declare_parameter("osm_file_path",
            "/home/<USER>/AGLoc_ws/src/area_graph_data_parser/data/fix_id/SIST1_1plus2_D.osm");

        // 关键修复：检查并获取use_sim_time参数设置
        // ROS2会自动声明use_sim_time参数，我们只需要获取它的值
        bool use_sim_time = false;
        try {
            use_sim_time = this->get_parameter("use_sim_time").as_bool();
            RCLCPP_INFO(this->get_logger(), "TopologyPublisher use_sim_time: %s",
                        use_sim_time ? "true" : "false");
        } catch (const rclcpp::exceptions::ParameterNotDeclaredException& e) {
            RCLCPP_WARN(this->get_logger(), "use_sim_time parameter not found, using default: false");
            use_sim_time = false;
        }

        // 创建发布者
        auto qos = rclcpp::QoS(rclcpp::KeepLast(10));
        qos.reliability(RMW_QOS_POLICY_RELIABILITY_RELIABLE);
        qos.durability(RMW_QOS_POLICY_DURABILITY_TRANSIENT_LOCAL);

        mappc_publisher_ = this->create_publisher<sensor_msgs::msg::PointCloud2>("/mapPC_AG", qos);
        agindex_publisher_ = this->create_publisher<area_graph_data_parser::msg::AGindex>("/AGindex", qos);

        // 初始化AreaGraph并加载地图
        if (!initialize_graph()) {
            RCLCPP_ERROR(this->get_logger(), "Failed to initialize graph, shutting down node");
            return;
        }

        // 创建定时器,周期性发布数据
        timer_ = this->create_wall_timer(
            std::chrono::seconds(1),
            std::bind(&TopologyPublisher::publish_data, this));

        RCLCPP_INFO(this->get_logger(), "Topology Publisher initialized successfully");
    }

private:
    bool initialize_graph() {
        // 读取OSM文件路径参数
        std::string osm_file = this->get_parameter("osm_file_path").as_string();
        RCLCPP_INFO(this->get_logger(), "Loading OSM file: %s", osm_file.c_str());

        // 加载并解析地图
        bool parse_success = false;
        try {
            parse_success = Parsing_Osm2AreaGraph(graph_, osm_file.c_str());
            if (!parse_success) {
                RCLCPP_ERROR(this->get_logger(), "Failed to parse OSM file");
                return false;
            }

            RCLCPP_INFO(this->get_logger(), "Successfully parsed OSM file");
            RCLCPP_INFO(this->get_logger(), "Areas: %ld, Passages: %ld, Nodes: %ld",
                      graph_.areas_.size(), graph_.passages_.size(), graph_.nodes_.size());

            // 遍历和构建关系
            TraverseArea(graph_);
            RCLCPP_INFO(this->get_logger(), "Area traversal completed");

            TraversePassage(graph_);
            RCLCPP_INFO(this->get_logger(), "Passage traversal completed");

            // 处理地图数据
            process_map_data();
            RCLCPP_INFO(this->get_logger(), "Map data processing completed");

            return true;
        }
        catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "Exception during graph initialization: %s", e.what());
            return false;
        }
    }

    void process_map_data() {
        // 清空现有点云
        cloud_->clear();
        cloud_->header.frame_id = "AGmap";

        // 遍历所有区域处理数据
        std::size_t total_index = 0;
        for (const auto& [area_id, area_ptr] : graph_.areas_) {
            if (area_ptr->type == "structure") continue;

            // 确保level标签存在
            if (area_ptr->attributes_->tags.find("level") == area_ptr->attributes_->tags.end()) {
                RCLCPP_WARN(this->get_logger(), "Area %ld missing level tag", area_id);
                continue;
            }

            double area_level = std::stod(area_ptr->attributes_->tags.at("level"));
            area_graph_data_parser::msg::AreaIndex area_index;
            std::size_t node_index = 0;
            const std::size_t total_nodes = area_ptr->nodes_inorder_.size();

            // 处理区域内的所有节点
            for (const auto& node_id : area_ptr->nodes_inorder_) {
                if (graph_.nodes_.find(node_id) == graph_.nodes_.end()) {
                    RCLCPP_ERROR(this->get_logger(), "Node %ld not found", node_id);
                    continue;
                }

                const auto& node = graph_.nodes_[node_id];

                // 创建PCL点
                pcl::PointXYZI point;
                point.x = static_cast<float>(node->attributes_->position[0]);
                point.y = static_cast<float>(node->attributes_->position[1]);
                point.z = static_cast<float>(area_level * 8);

                // 设置intensity值
                if (node_index == 0) {
                    point.intensity = 0.0f;
                    area_index.start = static_cast<int32_t>(cloud_->points.size());
                    areaID_mappcIndex_[std::abs(area_id)] = static_cast<int32_t>(cloud_->points.size());
                }
                else if (node_index == total_nodes - 1) {
                    point.intensity = 2.0f;
                    area_index.end = static_cast<int32_t>(cloud_->points.size());
                }
                else {
                    point.intensity = 1.0f;
                }

                // 检查节点是否属于通道
                for (const auto& [passage_id, passage_ptr] : graph_.passages_) {
                    const auto& nodes = passage_ptr->passage_nodes;
                    if (node_id == nodes.source || node_id == nodes.target) {
                        area_index.passage.push_back(static_cast<int32_t>(cloud_->points.size()));
                        area_index.passage_id.push_back(passage_id);
                        area_index.passage2start.push_back(passage_id);
                        point.intensity += 3.0f;
                    }
                }

                cloud_->points.push_back(point);
                node_index++;
                total_index++;
            }

            AG_index_.area_index.push_back(area_index);
        }

        cloud_->height = 1;
        cloud_->width = cloud_->points.size();

        // 处理passage2start映射
        process_passage_connections();

        RCLCPP_INFO(this->get_logger(), "Processed %zu points in total", total_index);
    }

    void process_passage_connections() {
        for (auto& area_index : AG_index_.area_index) {
            for (size_t i = 0; i < area_index.passage_id.size(); i++) {
                osm_ag::PassageId current_passage = area_index.passage_id[i];

                // 查找连接的另一个区域
                for (const auto& other_area_index : AG_index_.area_index) {
                    if (&other_area_index == &area_index) continue;

                    auto it = std::find(other_area_index.passage_id.begin(),
                                      other_area_index.passage_id.end(),
                                      current_passage);

                    if (it != other_area_index.passage_id.end()) {
                        area_index.passage2start[i] = other_area_index.start;
                        break;
                    }
                }
            }
        }
    }

    void publish_data() {
        if (cloud_->empty()) {
            RCLCPP_WARN(this->get_logger(), "Point cloud is empty, skipping publication");
            return;
        }

        // 转换PCL点云到ROS2消息
        sensor_msgs::msg::PointCloud2 cloud_msg;
        pcl::toROSMsg(*cloud_, cloud_msg);

        // 设置时间戳和frame_id
        cloud_msg.header.stamp = this->now();
        cloud_msg.header.frame_id = "AGmap";

        // 发布数据
        mappc_publisher_->publish(cloud_msg);
        agindex_publisher_->publish(AG_index_);

        RCLCPP_DEBUG(this->get_logger(), "Published map data with %zu points",
                     cloud_->points.size());
    }

    // 类成员变量
    osm_ag::AreaGraph graph_;
    std::map<osm_ag::AreaId, int32_t> areaID_mappcIndex_;
    pcl::PointCloud<pcl::PointXYZI>::Ptr cloud_;
    area_graph_data_parser::msg::AGindex AG_index_;

    rclcpp::Publisher<sensor_msgs::msg::PointCloud2>::SharedPtr mappc_publisher_;
    rclcpp::Publisher<area_graph_data_parser::msg::AGindex>::SharedPtr agindex_publisher_;
    rclcpp::TimerBase::SharedPtr timer_;
};

int main(int argc, char* argv[]) {
    rclcpp::init(argc, argv);
    auto node = std::make_shared<TopologyPublisher>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}
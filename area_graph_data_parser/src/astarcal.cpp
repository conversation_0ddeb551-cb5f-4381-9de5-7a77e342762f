#include "astar_cal.h"
 
vector<CalcPt*> AStarCalc::findPath(const CalcPt& startPoint,const CalcPt& endPoint, double& resDistance)
{
  //首先写入起点,拷贝开启一个节点，内外隔离
  vector<CalcPt*> path;
  resDistance = 0.0;
  CalcPt* firstpt = new CalcPt(cv::Point(startPoint.pt.x, startPoint.pt.y));
  firstpt->H = calcH(firstpt, &endPoint); // heuristic value
  firstpt->G = 0;
  firstpt->F = calcF(firstpt); //H + G
  string firstpt_id = getPointid(firstpt);
  openList.push_back(firstpt);
  statemap[firstpt_id] = 1;
  costGmap[firstpt_id] = firstpt;
  while (!openList.empty()) {
    //找到F值最小的点
    auto curPoint = getLeastFpoint();
    if(curPoint->pt.x == endPoint.pt.x && curPoint->pt.y == endPoint.pt.y){
      path.push_back(curPoint);
      while(curPoint->parent != nullptr){
        auto temppoint = curPoint;
        curPoint = curPoint->parent;
        path.push_back(curPoint);
        if(temppoint->pt.x == curPoint->pt.x || temppoint->pt.y == curPoint->pt.y){
          resDistance += 1.0;
        }else{resDistance += 1.414;}
      }
      return path;
    }
    else{
      string curpoint_id = getPointid(curPoint);
      statemap[curpoint_id] = 2;
      openList.remove(curPoint);
      vector<CalcPt*> surroundPoints = getSurroundPoints(curPoint);
      for(auto point: surroundPoints){
        string pointid = getPointid(point);
        if(statemap[pointid] == 2){
          continue;
        }
        else{
          if(statemap[pointid] == 1){
            double tempG = calcG(curPoint, point);
            if(tempG < point->G){
              point->parent = curPoint;
              point->G = tempG;
              point->F = calcF(point);
            }
          }
          else{
            point->parent = curPoint;
            point->G = calcG(curPoint, point);
            point->H = calcH(point, &endPoint);
            point->F = calcF(point);
            statemap[pointid] = 1;
            openList.push_back(point);
          }
        }
      }
    }
  }
  return path;
}
 
//计算当前点周围八个点
vector<CalcPt*> AStarCalc::getSurroundPoints(const CalcPt* point)
{
  vector<CalcPt*> surroundPoints;
  for (int x = point->pt.x - 1; x <= point->pt.x + 1; x++) {
    for (int y = point->pt.y - 1; y <= point->pt.y + 1; y++) {
      string neighbor_id = to_string(x) + "s" + to_string(y);
      if(costGmap.count(neighbor_id) == 0){
        CalcPt* neighbor = new CalcPt(cv::Point(x, y));
        if(isCanreach(point, neighbor)) {
          costGmap[neighbor_id] = neighbor;
          surroundPoints.push_back(neighbor);
        }
      }
      else{
        surroundPoints.push_back(costGmap[neighbor_id]);
      }

    }
  }
  return surroundPoints;
}
 
//判断是否可能进行规划
bool AStarCalc::isCanreach(const CalcPt* point, CalcPt* target)
{
  //坐标小于0直接不计算了
  string neighbor_id = getPointid(target);
  if (target->pt.x < 0 || target->pt.y < 0 
  || target->pt.x >= static_cast<int>(sites.size()) || target->pt.y >= static_cast<int>(sites[0].size())
  || (target->pt.x == point->pt.x && target->pt.y == point->pt.y)
  || isInSites(&target->pt)
  || statemap[neighbor_id] == 2) return false;

  else {
    //if line move
    if (abs(point->pt.x - target->pt.x) + abs(point->pt.y - target->pt.y) == 1){
      return true;
    }
    // if diagonal move
    else {
      cv::Point tmppt1 = cv::Point(point->pt.x, target->pt.y);
      cv::Point tmppt2 = cv::Point(target->pt.x, point->pt.y);
      if (isInSites(&tmppt1) && isInSites(&tmppt2)){
        return false;
      }
      else
        return true;
    }
  }
}
 
//判断是否是障碍点
bool AStarCalc::isInSites(const cv::Point* point) const
{
  return sites[point->x][point->y] == 1;
}
 
 
//从开启列表中返回F值最小的节点 
CalcPt* AStarCalc::getLeastFpoint()
{
  auto resPoint = openList.front();
  for(auto& point : openList){
    if(point->F < resPoint->F){
      resPoint = point;
    }
  }  
  return resPoint;
}
 
//计算G值
double AStarCalc::calcG(CalcPt* parentp, CalcPt* point)
{
  // straight move or diagonal move
  double tempG = (abs(point->pt.x - parentp->pt.x) + abs(point->pt.y - parentp->pt.y)) == 1 ? kCost1 : kCost2;
  //add parent's G value
  double parentG = parentp->G;
  return parentG + tempG;
}
 
 
//计算H值
double AStarCalc::calcH(CalcPt* point, const CalcPt* end)
{
  cv::Point tmppoint = end->pt - point->pt;
  return sqrt(pow(tmppoint.x, 2) + pow(tmppoint.y, 2));
  // return 0;
}
 
//计算F值
double AStarCalc::calcF(CalcPt* point)
{
  //F=G+H
  return point->F = point->G + point->H;
}
 
//init site
void AStarCalc::InitSites(vector<vector<int>> _sites)
{
  sites = _sites;
}
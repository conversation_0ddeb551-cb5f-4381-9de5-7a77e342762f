// 只保留了单纯用于Rviz osmAG的功能
#include <array>
#include <cmath>
#include <filesystem>
#include <fstream>
#include <iostream>
#include <map>
#include <sstream>
#include <string>
#include <thread>
#include <vector>
#include <set>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>
#include "area_graph_data_parser/msg/a_gindex.hpp"
#include "area_graph_data_parser/msg/area_index.hpp"

// ROS2 headers
#include <rclcpp/rclcpp.hpp>
#include <nav_msgs/msg/path.hpp>
#include <grid_map_ros/grid_map_ros.hpp>

// Custom headers
#include "math_utils.h"
#include "graph_planning.h"
#include "graph_data_handler.h"
#include "visualization_utils.h"

using namespace tinyxml2;
using namespace osm_ag;

void parseAndBuildGraph(AreaGraph& graph, const std::string& osm_file_path) {
    Parsing_Osm2AreaGraph(graph, osm_file_path.c_str());
    TraverseArea(graph);
    TraversePassage(graph);
}

class AreaGraphVisualizer : public rclcpp::Node {
private:
    AreaGraph graph_;
    // 每层一个发布者
    std::map<int, rclcpp::Publisher<visualization_msgs::msg::MarkerArray>::SharedPtr> level_publishers_;
    // 可视化数据
    std::map<int, LevelVisualization> level_visuals_;

    // 配置
    LevelConfig visual_config_;
    pcl::PointCloud<pcl::PointXYZI>::Ptr cloud_;
    area_graph_data_parser::msg::AGindex AG_index_;
    std::map<osm_ag::AreaId, int32_t> areaID_mappcIndex_;
    rclcpp::Publisher<sensor_msgs::msg::PointCloud2>::SharedPtr mappc_publisher_;
    rclcpp::Publisher<area_graph_data_parser::msg::AGindex>::SharedPtr agindex_publisher_;

    // 成员函数声明
    void processMapData();
    void processPassageConnections();
    void initializeLevelColors();
    void createPublishers();

public:
    AreaGraphVisualizer();
    void publishVisualization();
};

// 构造函数实现
AreaGraphVisualizer::AreaGraphVisualizer() : Node("area_graph_visualizer") {
    // 声明并获取参数
    this->declare_parameter("osm_file_path",
        "/home/<USER>/osmAG_ws/src/area_graph_data_parser/data/fix_id/SIST1_2F_D.osm");
    this->declare_parameter("height_per_level", 8.0);

    auto osm_file_path = this->get_parameter("osm_file_path").as_string();
    visual_config_.height_per_level = this->get_parameter("height_per_level").as_double();

    // 关键修复：检查并获取use_sim_time参数设置
    // ROS2会自动声明use_sim_time参数，我们只需要获取它的值
    bool use_sim_time = false;
    try {
        use_sim_time = this->get_parameter("use_sim_time").as_bool();
        RCLCPP_INFO(this->get_logger(), "AreaGraphVisualizer use_sim_time: %s",
                    use_sim_time ? "true" : "false");
    } catch (const rclcpp::exceptions::ParameterNotDeclaredException& e) {
        RCLCPP_WARN(this->get_logger(), "use_sim_time parameter not found, using default: false");
        use_sim_time = false;
    }

    // 解析并构建图
    parseAndBuildGraph(graph_, osm_file_path);
    RCLCPP_INFO(this->get_logger(), "Graph built successfully");

    // 初始化层级配色
    initializeLevelColors();

    // 创建发布者
    createPublishers();

    // 生成可视化数据
    createVisualizationData(graph_, level_visuals_, &visual_config_);

    RCLCPP_INFO(this->get_logger(), "Visualization data prepared");

    // 初始化PCL点云
    cloud_.reset(new pcl::PointCloud<pcl::PointXYZI>());
    cloud_->header.frame_id = "AGmap";

    // 创建新的发布者
    auto qos = rclcpp::QoS(rclcpp::KeepLast(10));
    qos.reliability(RMW_QOS_POLICY_RELIABILITY_RELIABLE);
    qos.durability(RMW_QOS_POLICY_DURABILITY_TRANSIENT_LOCAL);

    mappc_publisher_ = this->create_publisher<sensor_msgs::msg::PointCloud2>("/mapPC_AG", qos);
    agindex_publisher_ = this->create_publisher<area_graph_data_parser::msg::AGindex>("/AGindex", qos);

    // 处理地图数据
    processMapData();
}

// 私有成员函数实现
void AreaGraphVisualizer::initializeLevelColors() {
    // 可以为每层设置特定的颜色
    visual_config_.level_colors[1] = createColor(0.0, 0.8, 0.2, 0.7);  // 一层绿色
    visual_config_.level_colors[2] = createColor(0.0, 0.5, 1.0, 0.9);  // 二层蓝色
    // 可以继续添加更多层的颜色... 但如果不添加的话，那就是随机生成了
}

void AreaGraphVisualizer::createPublishers() {
    // 为每层创建一个MarkerArray发布者
    std::set<int> levels;
    for (const auto& [area_id, area] : graph_.areas_) {
        if (!area->attributes_->tags["level"].empty()) {
            levels.insert(std::stoi(area->attributes_->tags["level"]));
        }
    }

    for (int level : levels) {
        std::string topic = "/area_graph/level_" + std::to_string(level);
        level_publishers_[level] = this->create_publisher<visualization_msgs::msg::MarkerArray>(
            topic, 10);
        RCLCPP_INFO(this->get_logger(), "Created publisher for level %d: %s",
            level, topic.c_str());
    }
}

void AreaGraphVisualizer::processMapData() {
    // 清空现有点云
    cloud_->clear();
    cloud_->header.frame_id = "AGmap";
    AG_index_.area_index.clear();  // 添加这行

    // 遍历所有区域处理数据
    for (const auto& [area_id, area_ptr] : graph_.areas_) {
        if (area_ptr->type == "structure") continue;

        if (area_ptr->attributes_->tags.find("level") == area_ptr->attributes_->tags.end()) {
            RCLCPP_WARN(this->get_logger(), "Area %ld missing level tag", area_id);
            continue;
        }

        double area_level = std::stod(area_ptr->attributes_->tags.at("level"));
        area_graph_data_parser::msg::AreaIndex area_index;
        std::size_t node_index = 0;
        const std::size_t total_nodes = area_ptr->nodes_inorder_.size();

        for (const auto& node_id : area_ptr->nodes_inorder_) {
            const auto& node = graph_.nodes_[node_id];

            pcl::PointXYZI point;
            point.x = static_cast<float>(node->attributes_->position[0]);
            point.y = static_cast<float>(node->attributes_->position[1]);
            point.z = static_cast<float>(area_level * visual_config_.height_per_level);

            if (node_index == 0) {
                point.intensity = 0.0f;
                area_index.start = static_cast<int32_t>(cloud_->points.size());
                areaID_mappcIndex_[std::abs(area_id)] = static_cast<int32_t>(cloud_->points.size());
            }
            else if (node_index == total_nodes - 1) {
                point.intensity = 2.0f;
                area_index.end = static_cast<int32_t>(cloud_->points.size());
            }
            else {
                point.intensity = 1.0f;
            }

            for (const auto& [passage_id, passage_ptr] : graph_.passages_) {
                const auto& nodes = passage_ptr->passage_nodes;
                if (node_id == nodes.source || node_id == nodes.target) {
                    area_index.passage.push_back(static_cast<int32_t>(cloud_->points.size()));
                    area_index.passage_id.push_back(passage_id);
                    area_index.passage2start.push_back(passage_id);
                    point.intensity += 3.0f;
                }
            }

            cloud_->points.push_back(point);
            node_index++;
        }
        AG_index_.area_index.push_back(area_index);
    }

    cloud_->height = 1;
    cloud_->width = cloud_->points.size();

    processPassageConnections();
}

void AreaGraphVisualizer::processPassageConnections() {
    for (auto& area_index : AG_index_.area_index) {
        for (size_t i = 0; i < area_index.passage_id.size(); i++) {
            osm_ag::PassageId current_passage = area_index.passage_id[i];

            for (const auto& other_area_index : AG_index_.area_index) {
                if (&other_area_index == &area_index) continue;

                auto it = std::find(other_area_index.passage_id.begin(),
                                  other_area_index.passage_id.end(),
                                  current_passage);

                if (it != other_area_index.passage_id.end()) {
                    area_index.passage2start[i] = other_area_index.start;
                    break;
                }
            }
        }
    }
}

void AreaGraphVisualizer::publishVisualization() {
    // 获取当前时间戳，确保所有消息使用相同的时间戳
    auto current_time = this->now();

    // 发布每层的markers
    for (const auto& [level, visual] : level_visuals_) {
        if (level_publishers_.count(level)) {
            auto marker_array = visual.getMarkerArray();
            for (auto& marker : marker_array.markers) {
                marker.header.stamp = current_time;
            }
            level_publishers_[level]->publish(marker_array);
        }
    }

    // 发布点云和索引数据
    if (!cloud_->empty()) {
        sensor_msgs::msg::PointCloud2 cloud_msg;
        pcl::toROSMsg(*cloud_, cloud_msg);
        cloud_msg.header.stamp = current_time;  // 使用统一的时间戳
        cloud_msg.header.frame_id = "AGmap";

        mappc_publisher_->publish(cloud_msg);
        agindex_publisher_->publish(AG_index_);
    }
}

int main(int argc, char* argv[]) {
    rclcpp::init(argc, argv);
    auto node = std::make_shared<AreaGraphVisualizer>();

    // 等待RViz准备就绪
    std::this_thread::sleep_for(std::chrono::seconds(2));

    RCLCPP_INFO(node->get_logger(), "Starting visualization...");

    rclcpp::Rate rate(1);  // 1Hz刷新率
    while (rclcpp::ok()) {
        node->publishVisualization();
        rclcpp::spin_some(node);
        rate.sleep();
    }

    rclcpp::shutdown();
    return 0;
}
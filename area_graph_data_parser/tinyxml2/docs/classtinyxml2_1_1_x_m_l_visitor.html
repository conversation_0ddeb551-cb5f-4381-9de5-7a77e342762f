<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: tinyxml2::XMLVisitor Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>tinyxml2</b></li><li class="navelem"><a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classtinyxml2_1_1_x_m_l_visitor-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">tinyxml2::XMLVisitor Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for tinyxml2::XMLVisitor:</div>
<div class="dyncontent">
 <div class="center">
  <img src="classtinyxml2_1_1_x_m_l_visitor.png" usemap="#tinyxml2::XMLVisitor_map" alt=""/>
  <map id="tinyxml2::XMLVisitor_map" name="tinyxml2::XMLVisitor_map">
<area href="classtinyxml2_1_1_x_m_l_printer.html" alt="tinyxml2::XMLPrinter" shape="rect" coords="0,56,128,80"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:acb3c22fc5f60eb9db98f533f2761f67d"><td class="memItemLeft" align="right" valign="top"><a id="acb3c22fc5f60eb9db98f533f2761f67d"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html#acb3c22fc5f60eb9db98f533f2761f67d">VisitEnter</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> &amp;)</td></tr>
<tr class="memdesc:acb3c22fc5f60eb9db98f533f2761f67d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visit a document. <br /></td></tr>
<tr class="separator:acb3c22fc5f60eb9db98f533f2761f67d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a170e9989cd046ba904f302d087e07086"><td class="memItemLeft" align="right" valign="top"><a id="a170e9989cd046ba904f302d087e07086"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html#a170e9989cd046ba904f302d087e07086">VisitExit</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> &amp;)</td></tr>
<tr class="memdesc:a170e9989cd046ba904f302d087e07086"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visit a document. <br /></td></tr>
<tr class="separator:a170e9989cd046ba904f302d087e07086"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af97980a17dd4e37448b181f5ddfa92b5"><td class="memItemLeft" align="right" valign="top"><a id="af97980a17dd4e37448b181f5ddfa92b5"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html#af97980a17dd4e37448b181f5ddfa92b5">VisitEnter</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> &amp;, const <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a> *)</td></tr>
<tr class="memdesc:af97980a17dd4e37448b181f5ddfa92b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visit an element. <br /></td></tr>
<tr class="separator:af97980a17dd4e37448b181f5ddfa92b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a772f10ddc83f881956d32628faa16eb6"><td class="memItemLeft" align="right" valign="top"><a id="a772f10ddc83f881956d32628faa16eb6"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html#a772f10ddc83f881956d32628faa16eb6">VisitExit</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> &amp;)</td></tr>
<tr class="memdesc:a772f10ddc83f881956d32628faa16eb6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visit an element. <br /></td></tr>
<tr class="separator:a772f10ddc83f881956d32628faa16eb6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc75bd459fc7ba8223b50f0616767f9a"><td class="memItemLeft" align="right" valign="top"><a id="adc75bd459fc7ba8223b50f0616767f9a"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html#adc75bd459fc7ba8223b50f0616767f9a">Visit</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a> &amp;)</td></tr>
<tr class="memdesc:adc75bd459fc7ba8223b50f0616767f9a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visit a declaration. <br /></td></tr>
<tr class="separator:adc75bd459fc7ba8223b50f0616767f9a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af30233565856480ea48b6fa0d6dec65b"><td class="memItemLeft" align="right" valign="top"><a id="af30233565856480ea48b6fa0d6dec65b"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html#af30233565856480ea48b6fa0d6dec65b">Visit</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a> &amp;)</td></tr>
<tr class="memdesc:af30233565856480ea48b6fa0d6dec65b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visit a text node. <br /></td></tr>
<tr class="separator:af30233565856480ea48b6fa0d6dec65b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acc8147fb5a85f6c65721654e427752d7"><td class="memItemLeft" align="right" valign="top"><a id="acc8147fb5a85f6c65721654e427752d7"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html#acc8147fb5a85f6c65721654e427752d7">Visit</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a> &amp;)</td></tr>
<tr class="memdesc:acc8147fb5a85f6c65721654e427752d7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visit a comment node. <br /></td></tr>
<tr class="separator:acc8147fb5a85f6c65721654e427752d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a14e4748387c34bf53d24e8119bb1f292"><td class="memItemLeft" align="right" valign="top"><a id="a14e4748387c34bf53d24e8119bb1f292"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html#a14e4748387c34bf53d24e8119bb1f292">Visit</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a> &amp;)</td></tr>
<tr class="memdesc:a14e4748387c34bf53d24e8119bb1f292"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visit an unknown node. <br /></td></tr>
<tr class="separator:a14e4748387c34bf53d24e8119bb1f292"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Implements the interface to the "Visitor pattern" (see the Accept() method.) If you call the Accept() method, it requires being passed a <a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a> class to handle callbacks. For nodes that contain other nodes (Document, Element) you will get called with a VisitEnter/VisitExit pair. Nodes that are always leafs are simply called with <a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html#adc75bd459fc7ba8223b50f0616767f9a" title="Visit a declaration.">Visit()</a>.</p>
<p>If you return 'true' from a Visit method, recursive parsing will continue. If you return false, <b>no children of this node or its siblings</b> will be visited.</p>
<p>All flavors of Visit methods have a default implementation that returns 'true' (continue visiting). You need to only override methods that are interesting to you.</p>
<p>Generally Accept() is called on the <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>, although all nodes support visiting.</p>
<p>You should never change the document from a callback.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a81e66df0a44c67a7af17f3b77a152785">XMLNode::Accept()</a> </dd></dl>
</div><hr/>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

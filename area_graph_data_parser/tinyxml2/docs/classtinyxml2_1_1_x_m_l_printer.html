<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: tinyxml2::XMLPrinter Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>tinyxml2</b></li><li class="navelem"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">XMLPrinter</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="classtinyxml2_1_1_x_m_l_printer-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">tinyxml2::XMLPrinter Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for tinyxml2::XMLPrinter:</div>
<div class="dyncontent">
 <div class="center">
  <img src="classtinyxml2_1_1_x_m_l_printer.png" usemap="#tinyxml2::XMLPrinter_map" alt=""/>
  <map id="tinyxml2::XMLPrinter_map" name="tinyxml2::XMLPrinter_map">
<area href="classtinyxml2_1_1_x_m_l_visitor.html" alt="tinyxml2::XMLVisitor" shape="rect" coords="0,0,128,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aa6d3841c069085f5b8a27bc7103c04f7"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#aa6d3841c069085f5b8a27bc7103c04f7">XMLPrinter</a> (FILE *file=0, bool compact=false, int depth=0)</td></tr>
<tr class="separator:aa6d3841c069085f5b8a27bc7103c04f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a178c608ce8476043d5d6513819cde903"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a178c608ce8476043d5d6513819cde903">PushHeader</a> (bool writeBOM, bool writeDeclaration)</td></tr>
<tr class="separator:a178c608ce8476043d5d6513819cde903"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a20fb06c83bd13e5140d7dd13af06c010"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a20fb06c83bd13e5140d7dd13af06c010">OpenElement</a> (const char *name, bool compactMode=false)</td></tr>
<tr class="separator:a20fb06c83bd13e5140d7dd13af06c010"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9a4e2c9348b42e147629d5a99f4af3f0"><td class="memItemLeft" align="right" valign="top"><a id="a9a4e2c9348b42e147629d5a99f4af3f0"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a9a4e2c9348b42e147629d5a99f4af3f0">PushAttribute</a> (const char *name, const char *value)</td></tr>
<tr class="memdesc:a9a4e2c9348b42e147629d5a99f4af3f0"><td class="mdescLeft">&#160;</td><td class="mdescRight">If streaming, add an attribute to an open element. <br /></td></tr>
<tr class="separator:a9a4e2c9348b42e147629d5a99f4af3f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad04d29562b46fcdb23ab320f8b664240"><td class="memItemLeft" align="right" valign="top"><a id="ad04d29562b46fcdb23ab320f8b664240"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#ad04d29562b46fcdb23ab320f8b664240">CloseElement</a> (bool compactMode=false)</td></tr>
<tr class="memdesc:ad04d29562b46fcdb23ab320f8b664240"><td class="mdescLeft">&#160;</td><td class="mdescRight">If streaming, close the Element. <br /></td></tr>
<tr class="separator:ad04d29562b46fcdb23ab320f8b664240"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1cc16a9362df4332012cb13cff6441b3"><td class="memItemLeft" align="right" valign="top"><a id="a1cc16a9362df4332012cb13cff6441b3"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a1cc16a9362df4332012cb13cff6441b3">PushText</a> (const char *text, bool cdata=false)</td></tr>
<tr class="memdesc:a1cc16a9362df4332012cb13cff6441b3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Add a text node. <br /></td></tr>
<tr class="separator:a1cc16a9362df4332012cb13cff6441b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3e0d4d78de25d4cf081009e1431cea7e"><td class="memItemLeft" align="right" valign="top"><a id="a3e0d4d78de25d4cf081009e1431cea7e"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a3e0d4d78de25d4cf081009e1431cea7e">PushText</a> (int value)</td></tr>
<tr class="memdesc:a3e0d4d78de25d4cf081009e1431cea7e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Add a text node from an integer. <br /></td></tr>
<tr class="separator:a3e0d4d78de25d4cf081009e1431cea7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a661fb50e7e0a4918d2d259cb0fae647e"><td class="memItemLeft" align="right" valign="top"><a id="a661fb50e7e0a4918d2d259cb0fae647e"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a661fb50e7e0a4918d2d259cb0fae647e">PushText</a> (unsigned value)</td></tr>
<tr class="memdesc:a661fb50e7e0a4918d2d259cb0fae647e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Add a text node from an unsigned. <br /></td></tr>
<tr class="separator:a661fb50e7e0a4918d2d259cb0fae647e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a96b0a0bfe105154a0a6c37d725258f0a"><td class="memItemLeft" align="right" valign="top"><a id="a96b0a0bfe105154a0a6c37d725258f0a"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a96b0a0bfe105154a0a6c37d725258f0a">PushText</a> (int64_t value)</td></tr>
<tr class="memdesc:a96b0a0bfe105154a0a6c37d725258f0a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Add a text node from a signed 64bit integer. <br /></td></tr>
<tr class="separator:a96b0a0bfe105154a0a6c37d725258f0a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a60b0a4cf57371ff8679c2c7556ccb708"><td class="memItemLeft" align="right" valign="top"><a id="a60b0a4cf57371ff8679c2c7556ccb708"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a60b0a4cf57371ff8679c2c7556ccb708">PushText</a> (uint64_t value)</td></tr>
<tr class="memdesc:a60b0a4cf57371ff8679c2c7556ccb708"><td class="mdescLeft">&#160;</td><td class="mdescRight">Add a text node from an unsigned 64bit integer. <br /></td></tr>
<tr class="separator:a60b0a4cf57371ff8679c2c7556ccb708"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4390e5fa1ed05189a8686647345ab29f"><td class="memItemLeft" align="right" valign="top"><a id="a4390e5fa1ed05189a8686647345ab29f"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a4390e5fa1ed05189a8686647345ab29f">PushText</a> (bool value)</td></tr>
<tr class="memdesc:a4390e5fa1ed05189a8686647345ab29f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Add a text node from a bool. <br /></td></tr>
<tr class="separator:a4390e5fa1ed05189a8686647345ab29f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1dbb1390e829d0673af66b9cd1928bd7"><td class="memItemLeft" align="right" valign="top"><a id="a1dbb1390e829d0673af66b9cd1928bd7"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a1dbb1390e829d0673af66b9cd1928bd7">PushText</a> (float value)</td></tr>
<tr class="memdesc:a1dbb1390e829d0673af66b9cd1928bd7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Add a text node from a float. <br /></td></tr>
<tr class="separator:a1dbb1390e829d0673af66b9cd1928bd7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa715302dfc09473c77c853cbd5431965"><td class="memItemLeft" align="right" valign="top"><a id="aa715302dfc09473c77c853cbd5431965"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#aa715302dfc09473c77c853cbd5431965">PushText</a> (double value)</td></tr>
<tr class="memdesc:aa715302dfc09473c77c853cbd5431965"><td class="mdescLeft">&#160;</td><td class="mdescRight">Add a text node from a double. <br /></td></tr>
<tr class="separator:aa715302dfc09473c77c853cbd5431965"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afc8416814219591c2fd5656e0c233140"><td class="memItemLeft" align="right" valign="top"><a id="afc8416814219591c2fd5656e0c233140"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#afc8416814219591c2fd5656e0c233140">PushComment</a> (const char *comment)</td></tr>
<tr class="memdesc:afc8416814219591c2fd5656e0c233140"><td class="mdescLeft">&#160;</td><td class="mdescRight">Add a comment. <br /></td></tr>
<tr class="separator:afc8416814219591c2fd5656e0c233140"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae966b988a7a28c41e91c5ca17fb2054b"><td class="memItemLeft" align="right" valign="top"><a id="ae966b988a7a28c41e91c5ca17fb2054b"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#ae966b988a7a28c41e91c5ca17fb2054b">VisitEnter</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> &amp;)</td></tr>
<tr class="memdesc:ae966b988a7a28c41e91c5ca17fb2054b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visit a document. <br /></td></tr>
<tr class="separator:ae966b988a7a28c41e91c5ca17fb2054b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a15fc1f2b922f540917dcf52808737b29"><td class="memItemLeft" align="right" valign="top"><a id="a15fc1f2b922f540917dcf52808737b29"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a15fc1f2b922f540917dcf52808737b29">VisitExit</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> &amp;)</td></tr>
<tr class="memdesc:a15fc1f2b922f540917dcf52808737b29"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visit a document. <br /></td></tr>
<tr class="separator:a15fc1f2b922f540917dcf52808737b29"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2ce2aa508c21ac91615093ddb9c282c5"><td class="memItemLeft" align="right" valign="top"><a id="a2ce2aa508c21ac91615093ddb9c282c5"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a2ce2aa508c21ac91615093ddb9c282c5">VisitEnter</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> &amp;element, const <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a> *attribute)</td></tr>
<tr class="memdesc:a2ce2aa508c21ac91615093ddb9c282c5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visit an element. <br /></td></tr>
<tr class="separator:a2ce2aa508c21ac91615093ddb9c282c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae99e0a7086543591edfb565f24689098"><td class="memItemLeft" align="right" valign="top"><a id="ae99e0a7086543591edfb565f24689098"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#ae99e0a7086543591edfb565f24689098">VisitExit</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> &amp;element)</td></tr>
<tr class="memdesc:ae99e0a7086543591edfb565f24689098"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visit an element. <br /></td></tr>
<tr class="separator:ae99e0a7086543591edfb565f24689098"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a275ae25544a12199ae40b6994ca6e4de"><td class="memItemLeft" align="right" valign="top"><a id="a275ae25544a12199ae40b6994ca6e4de"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a275ae25544a12199ae40b6994ca6e4de">Visit</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a> &amp;text)</td></tr>
<tr class="memdesc:a275ae25544a12199ae40b6994ca6e4de"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visit a text node. <br /></td></tr>
<tr class="separator:a275ae25544a12199ae40b6994ca6e4de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3f16a30be1537ac141d9bd2db824ba9e"><td class="memItemLeft" align="right" valign="top"><a id="a3f16a30be1537ac141d9bd2db824ba9e"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a3f16a30be1537ac141d9bd2db824ba9e">Visit</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a> &amp;comment)</td></tr>
<tr class="memdesc:a3f16a30be1537ac141d9bd2db824ba9e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visit a comment node. <br /></td></tr>
<tr class="separator:a3f16a30be1537ac141d9bd2db824ba9e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ceff5cd85e5db65838962174fcdcc46"><td class="memItemLeft" align="right" valign="top"><a id="a9ceff5cd85e5db65838962174fcdcc46"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a9ceff5cd85e5db65838962174fcdcc46">Visit</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a> &amp;declaration)</td></tr>
<tr class="memdesc:a9ceff5cd85e5db65838962174fcdcc46"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visit a declaration. <br /></td></tr>
<tr class="separator:a9ceff5cd85e5db65838962174fcdcc46"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa15e1da81e17dea5da6499ac5b08d9d8"><td class="memItemLeft" align="right" valign="top"><a id="aa15e1da81e17dea5da6499ac5b08d9d8"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#aa15e1da81e17dea5da6499ac5b08d9d8">Visit</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a> &amp;unknown)</td></tr>
<tr class="memdesc:aa15e1da81e17dea5da6499ac5b08d9d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visit an unknown node. <br /></td></tr>
<tr class="separator:aa15e1da81e17dea5da6499ac5b08d9d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a180671d73844f159f2d4aafbc11d106e"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a180671d73844f159f2d4aafbc11d106e">CStr</a> () const</td></tr>
<tr class="separator:a180671d73844f159f2d4aafbc11d106e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3256cf3523d4898b91abb18b924be04c"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a3256cf3523d4898b91abb18b924be04c">CStrSize</a> () const</td></tr>
<tr class="separator:a3256cf3523d4898b91abb18b924be04c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a690cb140ba98b7339734ff865f56b0b3"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a690cb140ba98b7339734ff865f56b0b3">ClearBuffer</a> (bool resetToFirstElement=true)</td></tr>
<tr class="separator:a690cb140ba98b7339734ff865f56b0b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:a01148e2ebe6776e38c5a3e41bc5feb74"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a01148e2ebe6776e38c5a3e41bc5feb74">PrintSpace</a> (int depth)</td></tr>
<tr class="separator:a01148e2ebe6776e38c5a3e41bc5feb74"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Printing functionality. The <a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">XMLPrinter</a> gives you more options than the <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a867cf5fa3e3ff6ae4847a8b7ee8ec083">XMLDocument::Print()</a> method.</p>
<p>It can:</p><ol type="1">
<li>Print to memory.</li>
<li>Print to a file you provide.</li>
<li>Print XML without a <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>.</li>
</ol>
<p>Print to Memory</p>
<pre class="fragment">XMLPrinter printer;
doc.Print( &amp;printer );
SomeFunction( printer.CStr() );
</pre><p>Print to a File</p>
<p>You provide the file pointer. </p><pre class="fragment">XMLPrinter printer( fp );
doc.Print( &amp;printer );
</pre><p>Print without a <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a></p>
<p>When loading, an XML parser is very useful. However, sometimes when saving, it just gets in the way. The code is often set up for streaming, and constructing the DOM is just overhead.</p>
<p>The Printer supports the streaming case. The following code prints out a trivially simple XML file without ever creating an XML document.</p>
<pre class="fragment">XMLPrinter printer( fp );
printer.OpenElement( "foo" );
printer.PushAttribute( "foo", "bar" );
printer.CloseElement();
</pre> </div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="aa6d3841c069085f5b8a27bc7103c04f7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa6d3841c069085f5b8a27bc7103c04f7">&#9670;&nbsp;</a></span>XMLPrinter()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">tinyxml2::XMLPrinter::XMLPrinter </td>
          <td>(</td>
          <td class="paramtype">FILE *&#160;</td>
          <td class="paramname"><em>file</em> = <code>0</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>compact</em> = <code>false</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>depth</em> = <code>0</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Construct the printer. If the FILE* is specified, this will print to the FILE. Else it will print to memory, and the result is available in <a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a180671d73844f159f2d4aafbc11d106e">CStr()</a>. If 'compact' is set to true, then output is created with only required whitespace and newlines. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a690cb140ba98b7339734ff865f56b0b3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a690cb140ba98b7339734ff865f56b0b3">&#9670;&nbsp;</a></span>ClearBuffer()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void tinyxml2::XMLPrinter::ClearBuffer </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>resetToFirstElement</em> = <code>true</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If in print to memory mode, reset the buffer to the beginning. </p>

</div>
</div>
<a id="a180671d73844f159f2d4aafbc11d106e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a180671d73844f159f2d4aafbc11d106e">&#9670;&nbsp;</a></span>CStr()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* tinyxml2::XMLPrinter::CStr </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If in print to memory mode, return a pointer to the XML file in memory. </p>

</div>
</div>
<a id="a3256cf3523d4898b91abb18b924be04c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3256cf3523d4898b91abb18b924be04c">&#9670;&nbsp;</a></span>CStrSize()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int tinyxml2::XMLPrinter::CStrSize </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>If in print to memory mode, return the size of the XML file in memory. (Note the size returned includes the terminating null.) </p>

</div>
</div>
<a id="a20fb06c83bd13e5140d7dd13af06c010"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a20fb06c83bd13e5140d7dd13af06c010">&#9670;&nbsp;</a></span>OpenElement()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void tinyxml2::XMLPrinter::OpenElement </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>name</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>compactMode</em> = <code>false</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If streaming, start writing an element. The element must be closed with <a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#ad04d29562b46fcdb23ab320f8b664240" title="If streaming, close the Element.">CloseElement()</a> </p>

</div>
</div>
<a id="a01148e2ebe6776e38c5a3e41bc5feb74"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a01148e2ebe6776e38c5a3e41bc5feb74">&#9670;&nbsp;</a></span>PrintSpace()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void tinyxml2::XMLPrinter::PrintSpace </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>depth</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Prints out the space before an element. You may override to change the space and tabs used. A <a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a01148e2ebe6776e38c5a3e41bc5feb74">PrintSpace()</a> override should call Print(). </p>

</div>
</div>
<a id="a178c608ce8476043d5d6513819cde903"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a178c608ce8476043d5d6513819cde903">&#9670;&nbsp;</a></span>PushHeader()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void tinyxml2::XMLPrinter::PushHeader </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>writeBOM</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>writeDeclaration</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>If streaming, write the BOM and declaration. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: tinyxml2::XMLElement Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>tinyxml2</b></li><li class="navelem"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classtinyxml2_1_1_x_m_l_element-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">tinyxml2::XMLElement Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for tinyxml2::XMLElement:</div>
<div class="dyncontent">
 <div class="center">
  <img src="classtinyxml2_1_1_x_m_l_element.png" usemap="#tinyxml2::XMLElement_map" alt=""/>
  <map id="tinyxml2::XMLElement_map" name="tinyxml2::XMLElement_map">
<area href="classtinyxml2_1_1_x_m_l_node.html" alt="tinyxml2::XMLNode" shape="rect" coords="0,0,134,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a63e057fb5baee1dd29f323cb85907b35"><td class="memItemLeft" align="right" valign="top"><a id="a63e057fb5baee1dd29f323cb85907b35"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a63e057fb5baee1dd29f323cb85907b35">Name</a> () const</td></tr>
<tr class="memdesc:a63e057fb5baee1dd29f323cb85907b35"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the name of an element (which is the <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a66344989a4b436155bcda72bd6b07b82">Value()</a> of the node.) <br /></td></tr>
<tr class="separator:a63e057fb5baee1dd29f323cb85907b35"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a97712009a530d8cb8a63bf705f02b4f1"><td class="memItemLeft" align="right" valign="top"><a id="a97712009a530d8cb8a63bf705f02b4f1"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a97712009a530d8cb8a63bf705f02b4f1">SetName</a> (const char *str, bool staticMem=false)</td></tr>
<tr class="memdesc:a97712009a530d8cb8a63bf705f02b4f1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the name of the element. <br /></td></tr>
<tr class="separator:a97712009a530d8cb8a63bf705f02b4f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad9ff5c2dbc15df36cf664ce1b0ea0a5d"><td class="memItemLeft" align="right" valign="top"><a id="ad9ff5c2dbc15df36cf664ce1b0ea0a5d"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ad9ff5c2dbc15df36cf664ce1b0ea0a5d">ToElement</a> ()</td></tr>
<tr class="memdesc:ad9ff5c2dbc15df36cf664ce1b0ea0a5d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to an Element, or null. <br /></td></tr>
<tr class="separator:ad9ff5c2dbc15df36cf664ce1b0ea0a5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3ea8a40e788fb9ad876c28a32932c6d5"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a3ea8a40e788fb9ad876c28a32932c6d5">Accept</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a> *visitor) const</td></tr>
<tr class="separator:a3ea8a40e788fb9ad876c28a32932c6d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a70e49ed60b11212ae35f7e354cfe1de9"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a70e49ed60b11212ae35f7e354cfe1de9">Attribute</a> (const char *name, const char *value=0) const</td></tr>
<tr class="separator:a70e49ed60b11212ae35f7e354cfe1de9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a95a89b13bb14a2d4655e2b5b406c00d4"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a95a89b13bb14a2d4655e2b5b406c00d4">IntAttribute</a> (const char *name, int defaultValue=0) const</td></tr>
<tr class="separator:a95a89b13bb14a2d4655e2b5b406c00d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afea43a1d4aa33e3703ddee5fc9adc26c"><td class="memItemLeft" align="right" valign="top"><a id="afea43a1d4aa33e3703ddee5fc9adc26c"></a>
unsigned&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#afea43a1d4aa33e3703ddee5fc9adc26c">UnsignedAttribute</a> (const char *name, unsigned defaultValue=0) const</td></tr>
<tr class="memdesc:afea43a1d4aa33e3703ddee5fc9adc26c"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a95a89b13bb14a2d4655e2b5b406c00d4">IntAttribute()</a> <br /></td></tr>
<tr class="separator:afea43a1d4aa33e3703ddee5fc9adc26c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66d96972adecd816194191f13cc4a0a0"><td class="memItemLeft" align="right" valign="top"><a id="a66d96972adecd816194191f13cc4a0a0"></a>
int64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a66d96972adecd816194191f13cc4a0a0">Int64Attribute</a> (const char *name, int64_t defaultValue=0) const</td></tr>
<tr class="memdesc:a66d96972adecd816194191f13cc4a0a0"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a95a89b13bb14a2d4655e2b5b406c00d4">IntAttribute()</a> <br /></td></tr>
<tr class="separator:a66d96972adecd816194191f13cc4a0a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a226502bab8f1be7ede1fdd255398eb85"><td class="memItemLeft" align="right" valign="top"><a id="a226502bab8f1be7ede1fdd255398eb85"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a226502bab8f1be7ede1fdd255398eb85">Unsigned64Attribute</a> (const char *name, uint64_t defaultValue=0) const</td></tr>
<tr class="memdesc:a226502bab8f1be7ede1fdd255398eb85"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a95a89b13bb14a2d4655e2b5b406c00d4">IntAttribute()</a> <br /></td></tr>
<tr class="separator:a226502bab8f1be7ede1fdd255398eb85"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a53eda26131e1ad1031ef8ec8adb51bd8"><td class="memItemLeft" align="right" valign="top"><a id="a53eda26131e1ad1031ef8ec8adb51bd8"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a53eda26131e1ad1031ef8ec8adb51bd8">BoolAttribute</a> (const char *name, bool defaultValue=false) const</td></tr>
<tr class="memdesc:a53eda26131e1ad1031ef8ec8adb51bd8"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a95a89b13bb14a2d4655e2b5b406c00d4">IntAttribute()</a> <br /></td></tr>
<tr class="separator:a53eda26131e1ad1031ef8ec8adb51bd8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a10a90c505aea716bf073eea1c97f33b5"><td class="memItemLeft" align="right" valign="top"><a id="a10a90c505aea716bf073eea1c97f33b5"></a>
double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a10a90c505aea716bf073eea1c97f33b5">DoubleAttribute</a> (const char *name, double defaultValue=0) const</td></tr>
<tr class="memdesc:a10a90c505aea716bf073eea1c97f33b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a95a89b13bb14a2d4655e2b5b406c00d4">IntAttribute()</a> <br /></td></tr>
<tr class="separator:a10a90c505aea716bf073eea1c97f33b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab1f4be2332e27dc640e9b6abd01d64dd"><td class="memItemLeft" align="right" valign="top"><a id="ab1f4be2332e27dc640e9b6abd01d64dd"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ab1f4be2332e27dc640e9b6abd01d64dd">FloatAttribute</a> (const char *name, float defaultValue=0) const</td></tr>
<tr class="memdesc:ab1f4be2332e27dc640e9b6abd01d64dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a95a89b13bb14a2d4655e2b5b406c00d4">IntAttribute()</a> <br /></td></tr>
<tr class="separator:ab1f4be2332e27dc640e9b6abd01d64dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a78bc1187c1c45ad89f2690eab567b1"><td class="memItemLeft" align="right" valign="top">XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a8a78bc1187c1c45ad89f2690eab567b1">QueryIntAttribute</a> (const char *name, int *value) const</td></tr>
<tr class="separator:a8a78bc1187c1c45ad89f2690eab567b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a26fc84cbfba6769dafcfbf256c05e22f"><td class="memItemLeft" align="right" valign="top"><a id="a26fc84cbfba6769dafcfbf256c05e22f"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a26fc84cbfba6769dafcfbf256c05e22f">QueryUnsignedAttribute</a> (const char *name, unsigned int *value) const</td></tr>
<tr class="memdesc:a26fc84cbfba6769dafcfbf256c05e22f"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a8a78bc1187c1c45ad89f2690eab567b1">QueryIntAttribute()</a> <br /></td></tr>
<tr class="separator:a26fc84cbfba6769dafcfbf256c05e22f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7c0955d80b6f8d196744eacb0f6e90a8"><td class="memItemLeft" align="right" valign="top"><a id="a7c0955d80b6f8d196744eacb0f6e90a8"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a7c0955d80b6f8d196744eacb0f6e90a8">QueryInt64Attribute</a> (const char *name, int64_t *value) const</td></tr>
<tr class="memdesc:a7c0955d80b6f8d196744eacb0f6e90a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a8a78bc1187c1c45ad89f2690eab567b1">QueryIntAttribute()</a> <br /></td></tr>
<tr class="separator:a7c0955d80b6f8d196744eacb0f6e90a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a13dd590b5d3958ce2ed79844aacd9405"><td class="memItemLeft" align="right" valign="top"><a id="a13dd590b5d3958ce2ed79844aacd9405"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a13dd590b5d3958ce2ed79844aacd9405">QueryUnsigned64Attribute</a> (const char *name, uint64_t *value) const</td></tr>
<tr class="memdesc:a13dd590b5d3958ce2ed79844aacd9405"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a8a78bc1187c1c45ad89f2690eab567b1">QueryIntAttribute()</a> <br /></td></tr>
<tr class="separator:a13dd590b5d3958ce2ed79844aacd9405"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a14c1bb77c39689838be01838d86ca872"><td class="memItemLeft" align="right" valign="top"><a id="a14c1bb77c39689838be01838d86ca872"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a14c1bb77c39689838be01838d86ca872">QueryBoolAttribute</a> (const char *name, bool *value) const</td></tr>
<tr class="memdesc:a14c1bb77c39689838be01838d86ca872"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a8a78bc1187c1c45ad89f2690eab567b1">QueryIntAttribute()</a> <br /></td></tr>
<tr class="separator:a14c1bb77c39689838be01838d86ca872"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f0964e2dbd8e2ee7fce9beab689443c"><td class="memItemLeft" align="right" valign="top"><a id="a5f0964e2dbd8e2ee7fce9beab689443c"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a5f0964e2dbd8e2ee7fce9beab689443c">QueryDoubleAttribute</a> (const char *name, double *value) const</td></tr>
<tr class="memdesc:a5f0964e2dbd8e2ee7fce9beab689443c"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a8a78bc1187c1c45ad89f2690eab567b1">QueryIntAttribute()</a> <br /></td></tr>
<tr class="separator:a5f0964e2dbd8e2ee7fce9beab689443c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd5eeddf6002ef90806af794b9d9a5a5"><td class="memItemLeft" align="right" valign="top"><a id="acd5eeddf6002ef90806af794b9d9a5a5"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#acd5eeddf6002ef90806af794b9d9a5a5">QueryFloatAttribute</a> (const char *name, float *value) const</td></tr>
<tr class="memdesc:acd5eeddf6002ef90806af794b9d9a5a5"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a8a78bc1187c1c45ad89f2690eab567b1">QueryIntAttribute()</a> <br /></td></tr>
<tr class="separator:acd5eeddf6002ef90806af794b9d9a5a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adb8ae765f98d0c5037faec48deea78bc"><td class="memItemLeft" align="right" valign="top"><a id="adb8ae765f98d0c5037faec48deea78bc"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#adb8ae765f98d0c5037faec48deea78bc">QueryStringAttribute</a> (const char *name, const char **value) const</td></tr>
<tr class="memdesc:adb8ae765f98d0c5037faec48deea78bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a8a78bc1187c1c45ad89f2690eab567b1">QueryIntAttribute()</a> <br /></td></tr>
<tr class="separator:adb8ae765f98d0c5037faec48deea78bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5b7df3bed2b8954eabf227fa204522eb"><td class="memItemLeft" align="right" valign="top">XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a5b7df3bed2b8954eabf227fa204522eb">QueryAttribute</a> (const char *name, int *value) const</td></tr>
<tr class="separator:a5b7df3bed2b8954eabf227fa204522eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a11943abf2d0831548c3790dd5d9f119c"><td class="memItemLeft" align="right" valign="top"><a id="a11943abf2d0831548c3790dd5d9f119c"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a11943abf2d0831548c3790dd5d9f119c">SetAttribute</a> (const char *name, const char *value)</td></tr>
<tr class="memdesc:a11943abf2d0831548c3790dd5d9f119c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the named attribute to value. <br /></td></tr>
<tr class="separator:a11943abf2d0831548c3790dd5d9f119c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aae6568c64c7f1cc88be8461ba41a79cf"><td class="memItemLeft" align="right" valign="top"><a id="aae6568c64c7f1cc88be8461ba41a79cf"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#aae6568c64c7f1cc88be8461ba41a79cf">SetAttribute</a> (const char *name, int value)</td></tr>
<tr class="memdesc:aae6568c64c7f1cc88be8461ba41a79cf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the named attribute to value. <br /></td></tr>
<tr class="separator:aae6568c64c7f1cc88be8461ba41a79cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae143997e90064ba82326b29a9930ea8f"><td class="memItemLeft" align="right" valign="top"><a id="ae143997e90064ba82326b29a9930ea8f"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ae143997e90064ba82326b29a9930ea8f">SetAttribute</a> (const char *name, unsigned value)</td></tr>
<tr class="memdesc:ae143997e90064ba82326b29a9930ea8f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the named attribute to value. <br /></td></tr>
<tr class="separator:ae143997e90064ba82326b29a9930ea8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaeefdf9171fec91b13a776b42299b0dd"><td class="memItemLeft" align="right" valign="top"><a id="aaeefdf9171fec91b13a776b42299b0dd"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#aaeefdf9171fec91b13a776b42299b0dd">SetAttribute</a> (const char *name, int64_t value)</td></tr>
<tr class="memdesc:aaeefdf9171fec91b13a776b42299b0dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the named attribute to value. <br /></td></tr>
<tr class="separator:aaeefdf9171fec91b13a776b42299b0dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad598868c0599ddc4695dab18552c308d"><td class="memItemLeft" align="right" valign="top"><a id="ad598868c0599ddc4695dab18552c308d"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ad598868c0599ddc4695dab18552c308d">SetAttribute</a> (const char *name, uint64_t value)</td></tr>
<tr class="memdesc:ad598868c0599ddc4695dab18552c308d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the named attribute to value. <br /></td></tr>
<tr class="separator:ad598868c0599ddc4695dab18552c308d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa848b696e6a75e4e545c6da9893b11e1"><td class="memItemLeft" align="right" valign="top"><a id="aa848b696e6a75e4e545c6da9893b11e1"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#aa848b696e6a75e4e545c6da9893b11e1">SetAttribute</a> (const char *name, bool value)</td></tr>
<tr class="memdesc:aa848b696e6a75e4e545c6da9893b11e1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the named attribute to value. <br /></td></tr>
<tr class="separator:aa848b696e6a75e4e545c6da9893b11e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a233397ee81e70eb5d4b814c5f8698533"><td class="memItemLeft" align="right" valign="top"><a id="a233397ee81e70eb5d4b814c5f8698533"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a233397ee81e70eb5d4b814c5f8698533">SetAttribute</a> (const char *name, double value)</td></tr>
<tr class="memdesc:a233397ee81e70eb5d4b814c5f8698533"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the named attribute to value. <br /></td></tr>
<tr class="separator:a233397ee81e70eb5d4b814c5f8698533"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a554b70d882e65b28fc084b23df9b9759"><td class="memItemLeft" align="right" valign="top"><a id="a554b70d882e65b28fc084b23df9b9759"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a554b70d882e65b28fc084b23df9b9759">SetAttribute</a> (const char *name, float value)</td></tr>
<tr class="memdesc:a554b70d882e65b28fc084b23df9b9759"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the named attribute to value. <br /></td></tr>
<tr class="separator:a554b70d882e65b28fc084b23df9b9759"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aebd45aa7118964c30b32fe12e944628a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#aebd45aa7118964c30b32fe12e944628a">DeleteAttribute</a> (const char *name)</td></tr>
<tr class="separator:aebd45aa7118964c30b32fe12e944628a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3e191704c8d499906ec11fe2f60c6686"><td class="memItemLeft" align="right" valign="top"><a id="a3e191704c8d499906ec11fe2f60c6686"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a3e191704c8d499906ec11fe2f60c6686">FirstAttribute</a> () const</td></tr>
<tr class="memdesc:a3e191704c8d499906ec11fe2f60c6686"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the first attribute in the list. <br /></td></tr>
<tr class="separator:a3e191704c8d499906ec11fe2f60c6686"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dcd4d5d6fb63396cd2f257c318b42c4"><td class="memItemLeft" align="right" valign="top"><a id="a2dcd4d5d6fb63396cd2f257c318b42c4"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a2dcd4d5d6fb63396cd2f257c318b42c4">FindAttribute</a> (const char *name) const</td></tr>
<tr class="memdesc:a2dcd4d5d6fb63396cd2f257c318b42c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Query a specific attribute in the list. <br /></td></tr>
<tr class="separator:a2dcd4d5d6fb63396cd2f257c318b42c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6d5c8d115561ade4e4456b71d91b6f51"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a6d5c8d115561ade4e4456b71d91b6f51">GetText</a> () const</td></tr>
<tr class="separator:a6d5c8d115561ade4e4456b71d91b6f51"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1f9c2cd61b72af5ae708d37b7ad283ce"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a1f9c2cd61b72af5ae708d37b7ad283ce">SetText</a> (const char *inText)</td></tr>
<tr class="separator:a1f9c2cd61b72af5ae708d37b7ad283ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeae8917b5ea6060b3c08d4e3d8d632d7"><td class="memItemLeft" align="right" valign="top"><a id="aeae8917b5ea6060b3c08d4e3d8d632d7"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#aeae8917b5ea6060b3c08d4e3d8d632d7">SetText</a> (int value)</td></tr>
<tr class="memdesc:aeae8917b5ea6060b3c08d4e3d8d632d7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convenience method for setting text inside an element. See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a1f9c2cd61b72af5ae708d37b7ad283ce">SetText()</a> for important limitations. <br /></td></tr>
<tr class="separator:aeae8917b5ea6060b3c08d4e3d8d632d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7bbfcc11d516598bc924a8fba4d08597"><td class="memItemLeft" align="right" valign="top"><a id="a7bbfcc11d516598bc924a8fba4d08597"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a7bbfcc11d516598bc924a8fba4d08597">SetText</a> (unsigned value)</td></tr>
<tr class="memdesc:a7bbfcc11d516598bc924a8fba4d08597"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convenience method for setting text inside an element. See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a1f9c2cd61b72af5ae708d37b7ad283ce">SetText()</a> for important limitations. <br /></td></tr>
<tr class="separator:a7bbfcc11d516598bc924a8fba4d08597"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7b62cd33acdfeff7ea2b1b330d4368e4"><td class="memItemLeft" align="right" valign="top"><a id="a7b62cd33acdfeff7ea2b1b330d4368e4"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a7b62cd33acdfeff7ea2b1b330d4368e4">SetText</a> (int64_t value)</td></tr>
<tr class="memdesc:a7b62cd33acdfeff7ea2b1b330d4368e4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convenience method for setting text inside an element. See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a1f9c2cd61b72af5ae708d37b7ad283ce">SetText()</a> for important limitations. <br /></td></tr>
<tr class="separator:a7b62cd33acdfeff7ea2b1b330d4368e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e615bc745afd1ca8ded56d7aac02657"><td class="memItemLeft" align="right" valign="top"><a id="a6e615bc745afd1ca8ded56d7aac02657"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a6e615bc745afd1ca8ded56d7aac02657">SetText</a> (uint64_t value)</td></tr>
<tr class="memdesc:a6e615bc745afd1ca8ded56d7aac02657"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convenience method for setting text inside an element. See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a1f9c2cd61b72af5ae708d37b7ad283ce">SetText()</a> for important limitations. <br /></td></tr>
<tr class="separator:a6e615bc745afd1ca8ded56d7aac02657"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae4b543d6770de76fb6ab68e541c192a4"><td class="memItemLeft" align="right" valign="top"><a id="ae4b543d6770de76fb6ab68e541c192a4"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ae4b543d6770de76fb6ab68e541c192a4">SetText</a> (bool value)</td></tr>
<tr class="memdesc:ae4b543d6770de76fb6ab68e541c192a4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convenience method for setting text inside an element. See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a1f9c2cd61b72af5ae708d37b7ad283ce">SetText()</a> for important limitations. <br /></td></tr>
<tr class="separator:ae4b543d6770de76fb6ab68e541c192a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a67bd77ac9aaeff58ff20b4275a65ba4e"><td class="memItemLeft" align="right" valign="top"><a id="a67bd77ac9aaeff58ff20b4275a65ba4e"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a67bd77ac9aaeff58ff20b4275a65ba4e">SetText</a> (double value)</td></tr>
<tr class="memdesc:a67bd77ac9aaeff58ff20b4275a65ba4e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convenience method for setting text inside an element. See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a1f9c2cd61b72af5ae708d37b7ad283ce">SetText()</a> for important limitations. <br /></td></tr>
<tr class="separator:a67bd77ac9aaeff58ff20b4275a65ba4e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51d560da5ae3ad6b75e0ab9ffb2ae42a"><td class="memItemLeft" align="right" valign="top"><a id="a51d560da5ae3ad6b75e0ab9ffb2ae42a"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a51d560da5ae3ad6b75e0ab9ffb2ae42a">SetText</a> (float value)</td></tr>
<tr class="memdesc:a51d560da5ae3ad6b75e0ab9ffb2ae42a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convenience method for setting text inside an element. See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a1f9c2cd61b72af5ae708d37b7ad283ce">SetText()</a> for important limitations. <br /></td></tr>
<tr class="separator:a51d560da5ae3ad6b75e0ab9ffb2ae42a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a926357996bef633cb736e1a558419632"><td class="memItemLeft" align="right" valign="top">XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">QueryIntText</a> (int *ival) const</td></tr>
<tr class="separator:a926357996bef633cb736e1a558419632"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a14d38aa4b5e18a46274a27425188a6a1"><td class="memItemLeft" align="right" valign="top"><a id="a14d38aa4b5e18a46274a27425188a6a1"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a14d38aa4b5e18a46274a27425188a6a1">QueryUnsignedText</a> (unsigned *uval) const</td></tr>
<tr class="memdesc:a14d38aa4b5e18a46274a27425188a6a1"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">QueryIntText()</a> <br /></td></tr>
<tr class="separator:a14d38aa4b5e18a46274a27425188a6a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a120c538c8eead169e635dbc70fb226d8"><td class="memItemLeft" align="right" valign="top"><a id="a120c538c8eead169e635dbc70fb226d8"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a120c538c8eead169e635dbc70fb226d8">QueryInt64Text</a> (int64_t *uval) const</td></tr>
<tr class="memdesc:a120c538c8eead169e635dbc70fb226d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">QueryIntText()</a> <br /></td></tr>
<tr class="separator:a120c538c8eead169e635dbc70fb226d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2239b3bd172ad8f5b78d04d4236144b"><td class="memItemLeft" align="right" valign="top"><a id="ac2239b3bd172ad8f5b78d04d4236144b"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ac2239b3bd172ad8f5b78d04d4236144b">QueryUnsigned64Text</a> (uint64_t *uval) const</td></tr>
<tr class="memdesc:ac2239b3bd172ad8f5b78d04d4236144b"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">QueryIntText()</a> <br /></td></tr>
<tr class="separator:ac2239b3bd172ad8f5b78d04d4236144b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3fe5417d59eb8f5c4afe924b7d332736"><td class="memItemLeft" align="right" valign="top"><a id="a3fe5417d59eb8f5c4afe924b7d332736"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a3fe5417d59eb8f5c4afe924b7d332736">QueryBoolText</a> (bool *bval) const</td></tr>
<tr class="memdesc:a3fe5417d59eb8f5c4afe924b7d332736"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">QueryIntText()</a> <br /></td></tr>
<tr class="separator:a3fe5417d59eb8f5c4afe924b7d332736"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a684679c99bb036a25652744cec6c4d96"><td class="memItemLeft" align="right" valign="top"><a id="a684679c99bb036a25652744cec6c4d96"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a684679c99bb036a25652744cec6c4d96">QueryDoubleText</a> (double *dval) const</td></tr>
<tr class="memdesc:a684679c99bb036a25652744cec6c4d96"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">QueryIntText()</a> <br /></td></tr>
<tr class="separator:a684679c99bb036a25652744cec6c4d96"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa332afedd93210daa6d44b88eb11e29"><td class="memItemLeft" align="right" valign="top"><a id="afa332afedd93210daa6d44b88eb11e29"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#afa332afedd93210daa6d44b88eb11e29">QueryFloatText</a> (float *fval) const</td></tr>
<tr class="memdesc:afa332afedd93210daa6d44b88eb11e29"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">QueryIntText()</a> <br /></td></tr>
<tr class="separator:afa332afedd93210daa6d44b88eb11e29"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a49bad014ffcc17b0b6119d5b2c97dfb5"><td class="memItemLeft" align="right" valign="top"><a id="a49bad014ffcc17b0b6119d5b2c97dfb5"></a>
unsigned&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a49bad014ffcc17b0b6119d5b2c97dfb5">UnsignedText</a> (unsigned defaultValue=0) const</td></tr>
<tr class="memdesc:a49bad014ffcc17b0b6119d5b2c97dfb5"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">QueryIntText()</a> <br /></td></tr>
<tr class="separator:a49bad014ffcc17b0b6119d5b2c97dfb5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab6151f7e3b4c2c0a8234e262d7b6b8a"><td class="memItemLeft" align="right" valign="top"><a id="aab6151f7e3b4c2c0a8234e262d7b6b8a"></a>
int64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#aab6151f7e3b4c2c0a8234e262d7b6b8a">Int64Text</a> (int64_t defaultValue=0) const</td></tr>
<tr class="memdesc:aab6151f7e3b4c2c0a8234e262d7b6b8a"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">QueryIntText()</a> <br /></td></tr>
<tr class="separator:aab6151f7e3b4c2c0a8234e262d7b6b8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af48c1023abbac1acdf4927c51c3a5f0c"><td class="memItemLeft" align="right" valign="top"><a id="af48c1023abbac1acdf4927c51c3a5f0c"></a>
uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#af48c1023abbac1acdf4927c51c3a5f0c">Unsigned64Text</a> (uint64_t defaultValue=0) const</td></tr>
<tr class="memdesc:af48c1023abbac1acdf4927c51c3a5f0c"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">QueryIntText()</a> <br /></td></tr>
<tr class="separator:af48c1023abbac1acdf4927c51c3a5f0c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a68569f59f6382bcea7f5013ec59736d2"><td class="memItemLeft" align="right" valign="top"><a id="a68569f59f6382bcea7f5013ec59736d2"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a68569f59f6382bcea7f5013ec59736d2">BoolText</a> (bool defaultValue=false) const</td></tr>
<tr class="memdesc:a68569f59f6382bcea7f5013ec59736d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">QueryIntText()</a> <br /></td></tr>
<tr class="separator:a68569f59f6382bcea7f5013ec59736d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a81b1ff0cf2f2cd09be8badc08b39a2b7"><td class="memItemLeft" align="right" valign="top"><a id="a81b1ff0cf2f2cd09be8badc08b39a2b7"></a>
double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a81b1ff0cf2f2cd09be8badc08b39a2b7">DoubleText</a> (double defaultValue=0) const</td></tr>
<tr class="memdesc:a81b1ff0cf2f2cd09be8badc08b39a2b7"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">QueryIntText()</a> <br /></td></tr>
<tr class="separator:a81b1ff0cf2f2cd09be8badc08b39a2b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a45444eb21f99ca46101545992dc2e927"><td class="memItemLeft" align="right" valign="top"><a id="a45444eb21f99ca46101545992dc2e927"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a45444eb21f99ca46101545992dc2e927">FloatText</a> (float defaultValue=0) const</td></tr>
<tr class="memdesc:a45444eb21f99ca46101545992dc2e927"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">QueryIntText()</a> <br /></td></tr>
<tr class="separator:a45444eb21f99ca46101545992dc2e927"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc9506eff9780f666f49dc3d5e5cae13"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#abc9506eff9780f666f49dc3d5e5cae13">InsertNewChildElement</a> (const char *name)</td></tr>
<tr class="separator:abc9506eff9780f666f49dc3d5e5cae13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae4f2c2e781b8dc030411d84cd20fa46d"><td class="memItemLeft" align="right" valign="top"><a id="ae4f2c2e781b8dc030411d84cd20fa46d"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ae4f2c2e781b8dc030411d84cd20fa46d">InsertNewComment</a> (const char *comment)</td></tr>
<tr class="memdesc:ae4f2c2e781b8dc030411d84cd20fa46d"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#abc9506eff9780f666f49dc3d5e5cae13">InsertNewChildElement()</a> <br /></td></tr>
<tr class="separator:ae4f2c2e781b8dc030411d84cd20fa46d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a189e155810fc9fdd4da1409cbadee187"><td class="memItemLeft" align="right" valign="top"><a id="a189e155810fc9fdd4da1409cbadee187"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a189e155810fc9fdd4da1409cbadee187">InsertNewText</a> (const char *text)</td></tr>
<tr class="memdesc:a189e155810fc9fdd4da1409cbadee187"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#abc9506eff9780f666f49dc3d5e5cae13">InsertNewChildElement()</a> <br /></td></tr>
<tr class="separator:a189e155810fc9fdd4da1409cbadee187"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adec237e788b50c4ed73c918a166adde6"><td class="memItemLeft" align="right" valign="top"><a id="adec237e788b50c4ed73c918a166adde6"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#adec237e788b50c4ed73c918a166adde6">InsertNewDeclaration</a> (const char *text)</td></tr>
<tr class="memdesc:adec237e788b50c4ed73c918a166adde6"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#abc9506eff9780f666f49dc3d5e5cae13">InsertNewChildElement()</a> <br /></td></tr>
<tr class="separator:adec237e788b50c4ed73c918a166adde6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acaa5fe3957760e68185006965e2c11c2"><td class="memItemLeft" align="right" valign="top"><a id="acaa5fe3957760e68185006965e2c11c2"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#acaa5fe3957760e68185006965e2c11c2">InsertNewUnknown</a> (const char *text)</td></tr>
<tr class="memdesc:acaa5fe3957760e68185006965e2c11c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">See <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#abc9506eff9780f666f49dc3d5e5cae13">InsertNewChildElement()</a> <br /></td></tr>
<tr class="separator:acaa5fe3957760e68185006965e2c11c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac035742d68b0c50c3f676374e59fe750"><td class="memItemLeft" align="right" valign="top">virtual <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ac035742d68b0c50c3f676374e59fe750">ShallowClone</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *document) const</td></tr>
<tr class="separator:ac035742d68b0c50c3f676374e59fe750"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad9ea913a460b48979bd83cf9871c99f6"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ad9ea913a460b48979bd83cf9871c99f6">ShallowEqual</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *compare) const</td></tr>
<tr class="separator:ad9ea913a460b48979bd83cf9871c99f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classtinyxml2_1_1_x_m_l_node"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classtinyxml2_1_1_x_m_l_node')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td></tr>
<tr class="memitem:a2de84cfa4ec3fe249bad745069d145f1 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a2de84cfa4ec3fe249bad745069d145f1"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a2de84cfa4ec3fe249bad745069d145f1">GetDocument</a> () const</td></tr>
<tr class="memdesc:a2de84cfa4ec3fe249bad745069d145f1 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> that owns this <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>. <br /></td></tr>
<tr class="separator:a2de84cfa4ec3fe249bad745069d145f1 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af343d1ef0b45c0020e62d784d7e67a68 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="af343d1ef0b45c0020e62d784d7e67a68"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#af343d1ef0b45c0020e62d784d7e67a68">GetDocument</a> ()</td></tr>
<tr class="memdesc:af343d1ef0b45c0020e62d784d7e67a68 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> that owns this <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>. <br /></td></tr>
<tr class="separator:af343d1ef0b45c0020e62d784d7e67a68 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41c55dab9162d1eb62db2008430e376b inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a41c55dab9162d1eb62db2008430e376b"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a41c55dab9162d1eb62db2008430e376b">ToText</a> ()</td></tr>
<tr class="memdesc:a41c55dab9162d1eb62db2008430e376b inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to Text, or null. <br /></td></tr>
<tr class="separator:a41c55dab9162d1eb62db2008430e376b inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff47671055aa99840a1c1ebd661e63e3 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="aff47671055aa99840a1c1ebd661e63e3"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aff47671055aa99840a1c1ebd661e63e3">ToComment</a> ()</td></tr>
<tr class="memdesc:aff47671055aa99840a1c1ebd661e63e3 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to a Comment, or null. <br /></td></tr>
<tr class="separator:aff47671055aa99840a1c1ebd661e63e3 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a836e2966ed736fc3c94f70e12a2a3357 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a836e2966ed736fc3c94f70e12a2a3357"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a836e2966ed736fc3c94f70e12a2a3357">ToDocument</a> ()</td></tr>
<tr class="memdesc:a836e2966ed736fc3c94f70e12a2a3357 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to a Document, or null. <br /></td></tr>
<tr class="separator:a836e2966ed736fc3c94f70e12a2a3357 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a174fd4c22c010b58138c1b84a0dfbd51 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a174fd4c22c010b58138c1b84a0dfbd51"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a174fd4c22c010b58138c1b84a0dfbd51">ToDeclaration</a> ()</td></tr>
<tr class="memdesc:a174fd4c22c010b58138c1b84a0dfbd51 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to a Declaration, or null. <br /></td></tr>
<tr class="separator:a174fd4c22c010b58138c1b84a0dfbd51 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8675a74aa0ada6eccab0c77ef3e5b9bd inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a8675a74aa0ada6eccab0c77ef3e5b9bd"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8675a74aa0ada6eccab0c77ef3e5b9bd">ToUnknown</a> ()</td></tr>
<tr class="memdesc:a8675a74aa0ada6eccab0c77ef3e5b9bd inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to an Unknown, or null. <br /></td></tr>
<tr class="separator:a8675a74aa0ada6eccab0c77ef3e5b9bd inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66344989a4b436155bcda72bd6b07b82 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a66344989a4b436155bcda72bd6b07b82">Value</a> () const</td></tr>
<tr class="separator:a66344989a4b436155bcda72bd6b07b82 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09dd68cf9eae137579f6e50f36487513 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a09dd68cf9eae137579f6e50f36487513">SetValue</a> (const char *val, bool staticMem=false)</td></tr>
<tr class="separator:a09dd68cf9eae137579f6e50f36487513 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b5fc636646fda761d342c72e91cb286 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a9b5fc636646fda761d342c72e91cb286"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a9b5fc636646fda761d342c72e91cb286">GetLineNum</a> () const</td></tr>
<tr class="memdesc:a9b5fc636646fda761d342c72e91cb286 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the line number the node is in, if the document was parsed from a file. <br /></td></tr>
<tr class="separator:a9b5fc636646fda761d342c72e91cb286 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0f62bc186c56c2e0483ebd52dbfbe34 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="ae0f62bc186c56c2e0483ebd52dbfbe34"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ae0f62bc186c56c2e0483ebd52dbfbe34">Parent</a> () const</td></tr>
<tr class="memdesc:ae0f62bc186c56c2e0483ebd52dbfbe34 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the parent of this node on the DOM. <br /></td></tr>
<tr class="separator:ae0f62bc186c56c2e0483ebd52dbfbe34 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3ab489e6e202a3cd1762d3b332e89d4 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="ac3ab489e6e202a3cd1762d3b332e89d4"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ac3ab489e6e202a3cd1762d3b332e89d4">NoChildren</a> () const</td></tr>
<tr class="memdesc:ac3ab489e6e202a3cd1762d3b332e89d4 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if this node has no children. <br /></td></tr>
<tr class="separator:ac3ab489e6e202a3cd1762d3b332e89d4 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae7dc225e1018cdd685f7563593a1fe08 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="ae7dc225e1018cdd685f7563593a1fe08"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ae7dc225e1018cdd685f7563593a1fe08">FirstChild</a> () const</td></tr>
<tr class="memdesc:ae7dc225e1018cdd685f7563593a1fe08 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the first child node, or null if none exists. <br /></td></tr>
<tr class="separator:ae7dc225e1018cdd685f7563593a1fe08 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1795a35852dc8aae877cc8ded986e59b inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a1795a35852dc8aae877cc8ded986e59b">FirstChildElement</a> (const char *name=0) const</td></tr>
<tr class="separator:a1795a35852dc8aae877cc8ded986e59b inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b8583a277e8e26f4cbbb5492786778e inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a9b8583a277e8e26f4cbbb5492786778e"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a9b8583a277e8e26f4cbbb5492786778e">LastChild</a> () const</td></tr>
<tr class="memdesc:a9b8583a277e8e26f4cbbb5492786778e inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the last child node, or null if none exists. <br /></td></tr>
<tr class="separator:a9b8583a277e8e26f4cbbb5492786778e inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a173e9d1341bc56992e2d320a35936551 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a173e9d1341bc56992e2d320a35936551">LastChildElement</a> (const char *name=0) const</td></tr>
<tr class="separator:a173e9d1341bc56992e2d320a35936551 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac667c513d445f8b783e1e15ef9d3551 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="aac667c513d445f8b783e1e15ef9d3551"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aac667c513d445f8b783e1e15ef9d3551">PreviousSibling</a> () const</td></tr>
<tr class="memdesc:aac667c513d445f8b783e1e15ef9d3551 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the previous (left) sibling node of this node. <br /></td></tr>
<tr class="separator:aac667c513d445f8b783e1e15ef9d3551 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a872936cae46fb473eb47fec99129fc70 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a872936cae46fb473eb47fec99129fc70"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a872936cae46fb473eb47fec99129fc70">PreviousSiblingElement</a> (const char *name=0) const</td></tr>
<tr class="memdesc:a872936cae46fb473eb47fec99129fc70 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the previous (left) sibling element of this node, with an optionally supplied name. <br /></td></tr>
<tr class="separator:a872936cae46fb473eb47fec99129fc70 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79db9ef0fe014d27790f2218b87bcbb5 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a79db9ef0fe014d27790f2218b87bcbb5"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a79db9ef0fe014d27790f2218b87bcbb5">NextSibling</a> () const</td></tr>
<tr class="memdesc:a79db9ef0fe014d27790f2218b87bcbb5 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the next (right) sibling node of this node. <br /></td></tr>
<tr class="separator:a79db9ef0fe014d27790f2218b87bcbb5 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1264c86233328f0cd36297552d982f80 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a1264c86233328f0cd36297552d982f80"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a1264c86233328f0cd36297552d982f80">NextSiblingElement</a> (const char *name=0) const</td></tr>
<tr class="memdesc:a1264c86233328f0cd36297552d982f80 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the next (right) sibling element of this node, with an optionally supplied name. <br /></td></tr>
<tr class="separator:a1264c86233328f0cd36297552d982f80 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeb249ed60f4e8bfad3709151c3ee4286 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aeb249ed60f4e8bfad3709151c3ee4286">InsertEndChild</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *addThis)</td></tr>
<tr class="separator:aeb249ed60f4e8bfad3709151c3ee4286 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8ff7dc071f3a1a6ae2ac25a37492865d inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8ff7dc071f3a1a6ae2ac25a37492865d">InsertFirstChild</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *addThis)</td></tr>
<tr class="separator:a8ff7dc071f3a1a6ae2ac25a37492865d inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a85adb8f0b7477eec30f9a41d420b09c2 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a85adb8f0b7477eec30f9a41d420b09c2">InsertAfterChild</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *afterThis, <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *addThis)</td></tr>
<tr class="separator:a85adb8f0b7477eec30f9a41d420b09c2 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0360085cc54df5bff85d5c5da13afdce inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a0360085cc54df5bff85d5c5da13afdce">DeleteChildren</a> ()</td></tr>
<tr class="separator:a0360085cc54df5bff85d5c5da13afdce inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a363b6edbd6ebd55f8387d2b89f2b0921 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a363b6edbd6ebd55f8387d2b89f2b0921">DeleteChild</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *node)</td></tr>
<tr class="separator:a363b6edbd6ebd55f8387d2b89f2b0921 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62c71b6bf8734b5424063b8d9a61c266 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a62c71b6bf8734b5424063b8d9a61c266">DeepClone</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *target) const</td></tr>
<tr class="separator:a62c71b6bf8734b5424063b8d9a61c266 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a002978fc889cc011d143185f2377eca2 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a002978fc889cc011d143185f2377eca2">SetUserData</a> (void *userData)</td></tr>
<tr class="separator:a002978fc889cc011d143185f2377eca2 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f0687574afa03bc479dc44f29db0afe inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a7f0687574afa03bc479dc44f29db0afe">GetUserData</a> () const</td></tr>
<tr class="separator:a7f0687574afa03bc479dc44f29db0afe inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The element is a container class. It has a value, the element name, and can contain other elements, text, comments, and unknowns. Elements also contain an arbitrary number of attributes. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a3ea8a40e788fb9ad876c28a32932c6d5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3ea8a40e788fb9ad876c28a32932c6d5">&#9670;&nbsp;</a></span>Accept()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool tinyxml2::XMLElement::Accept </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a> *&#160;</td>
          <td class="paramname"><em>visitor</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Accept a hierarchical visit of the nodes in the TinyXML-2 DOM. Every node in the XML tree will be conditionally visited and the host will be called back via the <a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a> interface.</p>
<p>This is essentially a SAX interface for TinyXML-2. (Note however it doesn't re-parse the XML for the callbacks, so the performance of TinyXML-2 is unchanged by using this interface versus any other.)</p>
<p>The interface has been based on ideas from:</p>
<ul>
<li><a href="http://www.saxproject.org/">http://www.saxproject.org/</a></li>
<li><a href="http://c2.com/cgi/wiki?HierarchicalVisitorPattern">http://c2.com/cgi/wiki?HierarchicalVisitorPattern</a></li>
</ul>
<p>Which are both good references for "visiting".</p>
<p>An example of using <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a3ea8a40e788fb9ad876c28a32932c6d5">Accept()</a>: </p><pre class="fragment">XMLPrinter printer;
tinyxmlDoc.Accept( &amp;printer );
const char* xmlcstr = printer.CStr();
</pre> 
<p>Implements <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a81e66df0a44c67a7af17f3b77a152785">tinyxml2::XMLNode</a>.</p>

</div>
</div>
<a id="a70e49ed60b11212ae35f7e354cfe1de9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a70e49ed60b11212ae35f7e354cfe1de9">&#9670;&nbsp;</a></span>Attribute()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* tinyxml2::XMLElement::Attribute </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>name</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>value</em> = <code>0</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Given an attribute name, <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a70e49ed60b11212ae35f7e354cfe1de9">Attribute()</a> returns the value for the attribute of that name, or null if none exists. For example:</p>
<pre class="fragment">const char* value = ele-&gt;Attribute( "foo" );
</pre><p>The 'value' parameter is normally null. However, if specified, the attribute will only be returned if the 'name' and 'value' match. This allow you to write code:</p>
<pre class="fragment">if ( ele-&gt;Attribute( "foo", "bar" ) ) callFooIsBar();
</pre><p>rather than: </p><pre class="fragment">if ( ele-&gt;Attribute( "foo" ) ) {
    if ( strcmp( ele-&gt;Attribute( "foo" ), "bar" ) == 0 ) callFooIsBar();
}
</pre> 
</div>
</div>
<a id="aebd45aa7118964c30b32fe12e944628a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aebd45aa7118964c30b32fe12e944628a">&#9670;&nbsp;</a></span>DeleteAttribute()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void tinyxml2::XMLElement::DeleteAttribute </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>name</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Delete an attribute. </p>

</div>
</div>
<a id="a6d5c8d115561ade4e4456b71d91b6f51"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6d5c8d115561ade4e4456b71d91b6f51">&#9670;&nbsp;</a></span>GetText()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* tinyxml2::XMLElement::GetText </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Convenience function for easy access to the text inside an element. Although easy and concise, <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a6d5c8d115561ade4e4456b71d91b6f51">GetText()</a> is limited compared to getting the <a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a> child and accessing it directly.</p>
<p>If the first child of 'this' is a <a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>, the <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a6d5c8d115561ade4e4456b71d91b6f51">GetText()</a> returns the character string of the Text node, else null is returned.</p>
<p>This is a convenient method for getting the text of simple contained text: </p><pre class="fragment">&lt;foo&gt;This is text&lt;/foo&gt;
    const char* str = fooElement-&gt;GetText();
</pre><p>'str' will be a pointer to "This is text".</p>
<p>Note that this function can be misleading. If the element foo was created from this XML: </p><pre class="fragment">    &lt;foo&gt;&lt;b&gt;This is text&lt;/b&gt;&lt;/foo&gt;
</pre><p>then the value of str would be null. The first child node isn't a text node, it is another element. From this XML: </p><pre class="fragment">    &lt;foo&gt;This is &lt;b&gt;text&lt;/b&gt;&lt;/foo&gt;
</pre><p> <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a6d5c8d115561ade4e4456b71d91b6f51">GetText()</a> will return "This is ". </p>

</div>
</div>
<a id="abc9506eff9780f666f49dc3d5e5cae13"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc9506eff9780f666f49dc3d5e5cae13">&#9670;&nbsp;</a></span>InsertNewChildElement()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* tinyxml2::XMLElement::InsertNewChildElement </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>name</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Convenience method to create a new <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> and add it as last (right) child of this node. Returns the created and inserted element. </p>

</div>
</div>
<a id="a95a89b13bb14a2d4655e2b5b406c00d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a95a89b13bb14a2d4655e2b5b406c00d4">&#9670;&nbsp;</a></span>IntAttribute()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int tinyxml2::XMLElement::IntAttribute </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>name</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>defaultValue</em> = <code>0</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Given an attribute name, <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a95a89b13bb14a2d4655e2b5b406c00d4">IntAttribute()</a> returns the value of the attribute interpreted as an integer. The default value will be returned if the attribute isn't present, or if there is an error. (For a method with error checking, see <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a8a78bc1187c1c45ad89f2690eab567b1">QueryIntAttribute()</a>). </p>

</div>
</div>
<a id="a5b7df3bed2b8954eabf227fa204522eb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5b7df3bed2b8954eabf227fa204522eb">&#9670;&nbsp;</a></span>QueryAttribute()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">XMLError tinyxml2::XMLElement::QueryAttribute </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>name</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Given an attribute name, <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a5b7df3bed2b8954eabf227fa204522eb">QueryAttribute()</a> returns XML_SUCCESS, XML_WRONG_ATTRIBUTE_TYPE if the conversion can't be performed, or XML_NO_ATTRIBUTE if the attribute doesn't exist. It is overloaded for the primitive types, and is a generally more convenient replacement of <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a8a78bc1187c1c45ad89f2690eab567b1">QueryIntAttribute()</a> and related functions.</p>
<p>If successful, the result of the conversion will be written to 'value'. If not successful, nothing will be written to 'value'. This allows you to provide default value:</p>
<pre class="fragment">int value = 10;
QueryAttribute( "foo", &amp;value );        // if "foo" isn't found, value will still be 10
</pre> 
</div>
</div>
<a id="a8a78bc1187c1c45ad89f2690eab567b1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8a78bc1187c1c45ad89f2690eab567b1">&#9670;&nbsp;</a></span>QueryIntAttribute()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">XMLError tinyxml2::XMLElement::QueryIntAttribute </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>name</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Given an attribute name, <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a8a78bc1187c1c45ad89f2690eab567b1">QueryIntAttribute()</a> returns XML_SUCCESS, XML_WRONG_ATTRIBUTE_TYPE if the conversion can't be performed, or XML_NO_ATTRIBUTE if the attribute doesn't exist. If successful, the result of the conversion will be written to 'value'. If not successful, nothing will be written to 'value'. This allows you to provide default value:</p>
<pre class="fragment">int value = 10;
QueryIntAttribute( "foo", &amp;value );     // if "foo" isn't found, value will still be 10
</pre> 
</div>
</div>
<a id="a926357996bef633cb736e1a558419632"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a926357996bef633cb736e1a558419632">&#9670;&nbsp;</a></span>QueryIntText()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">XMLError tinyxml2::XMLElement::QueryIntText </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>ival</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Convenience method to query the value of a child text node. This is probably best shown by example. Given you have a document is this form: </p><pre class="fragment">    &lt;point&gt;
        &lt;x&gt;1&lt;/x&gt;
        &lt;y&gt;1.4&lt;/y&gt;
    &lt;/point&gt;
</pre><p>The <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">QueryIntText()</a> and similar functions provide a safe and easier way to get to the "value" of x and y.</p>
<pre class="fragment">    int x = 0;
    float y = 0;    // types of x and y are contrived for example
    const XMLElement* xElement = pointElement-&gt;FirstChildElement( "x" );
    const XMLElement* yElement = pointElement-&gt;FirstChildElement( "y" );
    xElement-&gt;QueryIntText( &amp;x );
    yElement-&gt;QueryFloatText( &amp;y );
</pre><dl class="section return"><dt>Returns</dt><dd>XML_SUCCESS (0) on success, XML_CAN_NOT_CONVERT_TEXT if the text cannot be converted to the requested type, and XML_NO_TEXT_NODE if there is no child text to query. </dd></dl>

</div>
</div>
<a id="a1f9c2cd61b72af5ae708d37b7ad283ce"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1f9c2cd61b72af5ae708d37b7ad283ce">&#9670;&nbsp;</a></span>SetText()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void tinyxml2::XMLElement::SetText </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>inText</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Convenience function for easy access to the text inside an element. Although easy and concise, <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a1f9c2cd61b72af5ae708d37b7ad283ce">SetText()</a> is limited compared to creating an <a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a> child and mutating it directly.</p>
<p>If the first child of 'this' is a <a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>, <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a1f9c2cd61b72af5ae708d37b7ad283ce">SetText()</a> sets its value to the given string, otherwise it will create a first child that is an <a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>.</p>
<p>This is a convenient method for setting the text of simple contained text: </p><pre class="fragment">&lt;foo&gt;This is text&lt;/foo&gt;
    fooElement-&gt;SetText( "Hullaballoo!" );
&lt;foo&gt;Hullaballoo!&lt;/foo&gt;
</pre><p>Note that this function can be misleading. If the element foo was created from this XML: </p><pre class="fragment">    &lt;foo&gt;&lt;b&gt;This is text&lt;/b&gt;&lt;/foo&gt;
</pre><p>then it will not change "This is text", but rather prefix it with a text element: </p><pre class="fragment">    &lt;foo&gt;Hullaballoo!&lt;b&gt;This is text&lt;/b&gt;&lt;/foo&gt;
</pre><p>For this XML: </p><pre class="fragment">    &lt;foo /&gt;
</pre><p> <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a1f9c2cd61b72af5ae708d37b7ad283ce">SetText()</a> will generate </p><pre class="fragment">    &lt;foo&gt;Hullaballoo!&lt;/foo&gt;
</pre> 
</div>
</div>
<a id="ac035742d68b0c50c3f676374e59fe750"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac035742d68b0c50c3f676374e59fe750">&#9670;&nbsp;</a></span>ShallowClone()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* tinyxml2::XMLElement::ShallowClone </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td>
          <td class="paramname"><em>document</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Make a copy of this node, but not its children. You may pass in a Document pointer that will be the owner of the new Node. If the 'document' is null, then the node returned will be allocated from the current Document. (this-&gt;<a class="el" href="classtinyxml2_1_1_x_m_l_node.html#af343d1ef0b45c0020e62d784d7e67a68" title="Get the XMLDocument that owns this XMLNode.">GetDocument()</a>)</p>
<p>Note: if called on a <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>, this will return null. </p>

<p>Implements <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8402cbd3129d20e9e6024bbcc0531283">tinyxml2::XMLNode</a>.</p>

</div>
</div>
<a id="ad9ea913a460b48979bd83cf9871c99f6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad9ea913a460b48979bd83cf9871c99f6">&#9670;&nbsp;</a></span>ShallowEqual()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool tinyxml2::XMLElement::ShallowEqual </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td>
          <td class="paramname"><em>compare</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Test if 2 nodes are the same, but don't test children. The 2 nodes do not need to be in the same Document.</p>
<p>Note: if called on a <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>, this will return false. </p>

<p>Implements <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a7ce18b751c3ea09eac292dca264f9226">tinyxml2::XMLNode</a>.</p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

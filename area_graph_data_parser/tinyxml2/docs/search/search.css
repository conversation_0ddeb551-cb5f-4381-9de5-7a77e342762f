/*---------------- Search Box */

#MSearchBox {
    white-space : nowrap;
    background: white;
    border-radius: 0.65em;
    box-shadow: inset 0.5px 0.5px 3px 0px #555;
    z-index: 102;
}

#MSearchBox .left {
    display: inline-block;
    vertical-align: middle;
    height: 1.4em;
}

#MSearchSelect {
    display: inline-block;
    vertical-align: middle;
    height: 1.4em;
    padding: 0 0 0 0.3em;
    margin: 0;
}

#MSearchField {
    display: inline-block;
    vertical-align: middle;
    width: 7.5em;
    height: 1.1em;
    margin: 0 0.15em;
    padding: 0;
    line-height: 1em;
    border:none;
    color: #909090;
    outline: none;
    font-family: Aria<PERSON>, Verdana, sans-serif;
    -webkit-border-radius: 0px;
    border-radius: 0px;
    background: none;
}


#MSearchBox .right {
    display: inline-block;
    vertical-align: middle;
    width: 1.4em;
    height: 1.4em;
}

#MSearchClose {
    display: none;
    font-size: inherit;
    background : none;
    border: none;
    margin: 0;
    padding: 0;
    outline: none;

}

#MSearchCloseImg {
    height: 1.4em;
    padding: 0.3em;
    margin: 0;
}

.MSearchBoxActive #MSearchField {
    color: #000000;
}

#main-menu > li:last-child {
    /* This <li> object is the parent of the search bar */
    display: flex;
    justify-content: center;
    align-items: center;
    height: 36px;
    margin-right: 1em;
}

/*---------------- Search filter selection */

#MSearchSelectWindow {
    display: none;
    position: absolute;
    left: 0; top: 0;
    border: 1px solid #90A5CE;
    background-color: #F9FAFC;
    z-index: 10001;
    padding-top: 4px;
    padding-bottom: 4px;
    -moz-border-radius: 4px;
    -webkit-border-top-left-radius: 4px;
    -webkit-border-top-right-radius: 4px;
    -webkit-border-bottom-left-radius: 4px;
    -webkit-border-bottom-right-radius: 4px;
    -webkit-box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.15);
}

.SelectItem {
    font: 8pt Arial, Verdana, sans-serif;
    padding-left:  2px;
    padding-right: 12px;
    border: 0px;
}

span.SelectionMark {
    margin-right: 4px;
    font-family: monospace;
    outline-style: none;
    text-decoration: none;
}

a.SelectItem {
    display: block;
    outline-style: none;
    color: #000000; 
    text-decoration: none;
    padding-left:   6px;
    padding-right: 12px;
}

a.SelectItem:focus,
a.SelectItem:active {
    color: #000000; 
    outline-style: none;
    text-decoration: none;
}

a.SelectItem:hover {
    color: #FFFFFF;
    background-color: #3D578C;
    outline-style: none;
    text-decoration: none;
    cursor: pointer;
    display: block;
}

/*---------------- Search results window */

iframe#MSearchResults {
    width: 60ex;
    height: 15em;
}

#MSearchResultsWindow {
    display: none;
    position: absolute;
    left: 0; top: 0;
    border: 1px solid #000;
    background-color: #EEF1F7;
    z-index:10000;
}

/* ----------------------------------- */


#SRIndex {
    clear:both; 
    padding-bottom: 15px;
}

.SREntry {
    font-size: 10pt;
    padding-left: 1ex;
}

.SRPage .SREntry {
    font-size: 8pt;
    padding: 1px 5px;
}

body.SRPage {
    margin: 5px 2px;
}

.SRChildren {
    padding-left: 3ex; padding-bottom: .5em 
}

.SRPage .SRChildren {
    display: none;
}

.SRSymbol {
    font-weight: bold; 
    color: #425E97;
    font-family: Arial, Verdana, sans-serif;
    text-decoration: none;
    outline: none;
}

a.SRScope {
    display: block;
    color: #425E97; 
    font-family: Arial, Verdana, sans-serif;
    text-decoration: none;
    outline: none;
}

a.SRSymbol:focus, a.SRSymbol:active,
a.SRScope:focus, a.SRScope:active {
    text-decoration: underline;
}

span.SRScope {
    padding-left: 4px;
    font-family: Arial, Verdana, sans-serif;
}

.SRPage .SRStatus {
    padding: 2px 5px;
    font-size: 8pt;
    font-style: italic;
    font-family: Arial, Verdana, sans-serif;
}

.SRResult {
    display: none;
}

div.searchresults {
    margin-left: 10px;
    margin-right: 10px;
}

/*---------------- External search page results */

.searchresult {
    background-color: #F0F3F8;
}

.pages b {
   color: white;
   padding: 5px 5px 3px 5px;
   background-image: url("../tab_a.png");
   background-repeat: repeat-x;
   text-shadow: 0 1px 1px #000000;
}

.pages {
    line-height: 17px;
    margin-left: 4px;
    text-decoration: none;
}

.hl {
    font-weight: bold;
}

#searchresults {
    margin-bottom: 20px;
}

.searchpages {
    margin-top: 10px;
}


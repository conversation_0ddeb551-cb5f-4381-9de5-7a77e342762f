<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: TinyXML-2</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="PageDoc"><div class="header">
  <div class="headertitle">
<div class="title">TinyXML-2 </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p><a class="anchor" id="md_readme"></a></p>
<p><img src="https://github.com/leethomason/tinyxml2/actions/workflows/test.yml/badge.svg" alt="Build" style="pointer-events: none;" class="inline"/></p>
<p><img src="http://www.grinninglizard.com/tinyxml2/TinyXML2_small.png" alt="TinyXML-2 Logo" class="inline"/></p>
<p>TinyXML-2 is a simple, small, efficient, C++ XML parser that can be easily integrated into other programs.</p>
<p>The master is hosted on github: <a href="https://github.com/leethomason/tinyxml2">https://github.com/leethomason/tinyxml2</a></p>
<p>The online HTML version of these docs: <a href="http://leethomason.github.io/tinyxml2/">http://leethomason.github.io/tinyxml2/</a></p>
<p>Examples are in the "related pages" tab of the HTML docs.</p>
<h1>What it does. </h1>
<p>In brief, TinyXML-2 parses an XML document, and builds from that a Document Object Model (DOM) that can be read, modified, and saved.</p>
<p>XML stands for "eXtensible Markup Language." It is a general purpose human and machine readable markup language to describe arbitrary data. All those random file formats created to store application data can all be replaced with XML. One parser for everything.</p>
<p><a href="http://en.wikipedia.org/wiki/XML">http://en.wikipedia.org/wiki/XML</a></p>
<p>There are different ways to access and interact with XML data. TinyXML-2 uses a Document Object Model (DOM), meaning the XML data is parsed into a C++ objects that can be browsed and manipulated, and then written to disk or another output stream. You can also construct an XML document from scratch with C++ objects and write this to disk or another output stream. You can even use TinyXML-2 to stream XML programmatically from code without creating a document first.</p>
<p>TinyXML-2 is designed to be easy and fast to learn. It is one header and one cpp file. Simply add these to your project and off you go. There is an example file - xmltest.cpp - to get you started.</p>
<p>TinyXML-2 is released under the ZLib license, so you can use it in open source or commercial code. The details of the license are at the top of every source file.</p>
<p>TinyXML-2 attempts to be a flexible parser, but with truly correct and compliant XML output. TinyXML-2 should compile on any reasonably C++ compliant system. It does not rely on exceptions, RTTI, or the STL.</p>
<h1>What it doesn't do. </h1>
<p>TinyXML-2 doesn't parse or use DTDs (Document Type Definitions) or XSLs (eXtensible Stylesheet Language.) There are other parsers out there that are much more fully featured. But they are generally bigger and more difficult to use. If you are working with browsers or have more complete XML needs, TinyXML-2 is not the parser for you.</p>
<h1>TinyXML-1 vs. TinyXML-2 </h1>
<p>TinyXML-2 long been the focus of all development. It is well tested and should be used instead of TinyXML-1.</p>
<p>TinyXML-2 uses a similar API to TinyXML-1 and the same rich test cases. But the implementation of the parser is completely re-written to make it more appropriate for use in a game. It uses less memory, is faster, and uses far fewer memory allocations.</p>
<p>TinyXML-2 has no requirement or support for STL.</p>
<h1>Features </h1>
<h2>Code Page</h2>
<p>TinyXML-2 uses UTF-8 exclusively when interpreting XML. All XML is assumed to be UTF-8.</p>
<p>Filenames for loading / saving are passed unchanged to the underlying OS.</p>
<h2>Memory Model</h2>
<p>An XMLDocument is a C++ object like any other, that can be on the stack, or new'd and deleted on the heap.</p>
<p>However, any sub-node of the Document, XMLElement, XMLText, etc, can only be created by calling the appropriate XMLDocument::NewElement, NewText, etc. method. Although you have pointers to these objects, they are still owned by the Document. When the Document is deleted, so are all the nodes it contains.</p>
<h2>White Space</h2>
<h3>Whitespace Preservation (default)</h3>
<p>Microsoft has an excellent article on white space: <a href="http://msdn.microsoft.com/en-us/library/ms256097.aspx">http://msdn.microsoft.com/en-us/library/ms256097.aspx</a></p>
<p>By default, TinyXML-2 preserves white space in a (hopefully) sane way that is almost compliant with the spec. (TinyXML-1 used a completely different model, much more similar to 'collapse', below.)</p>
<p>As a first step, all newlines / carriage-returns / line-feeds are normalized to a line-feed character, as required by the XML spec.</p>
<p>White space in text is preserved. For example: </p><pre class="fragment">&lt;element&gt; Hello,  World&lt;/element&gt;
</pre><p> The leading space before the "Hello" and the double space after the comma are preserved. Line-feeds are preserved, as in this example: </p><pre class="fragment">&lt;element&gt; Hello again,
          World&lt;/element&gt;
</pre><p> However, white space between elements is <b>not</b> preserved. Although not strictly compliant, tracking and reporting inter-element space is awkward, and not normally valuable. TinyXML-2 sees these as the same XML: </p><pre class="fragment">&lt;document&gt;
    &lt;data&gt;1&lt;/data&gt;
    &lt;data&gt;2&lt;/data&gt;
    &lt;data&gt;3&lt;/data&gt;
&lt;/document&gt;

&lt;document&gt;&lt;data&gt;1&lt;/data&gt;&lt;data&gt;2&lt;/data&gt;&lt;data&gt;3&lt;/data&gt;&lt;/document&gt;
</pre> <h3>Whitespace Collapse</h3>
<p>For some applications, it is preferable to collapse whitespace. Collapsing whitespace gives you "HTML-like" behavior, which is sometimes more suitable for hand typed documents.</p>
<p>TinyXML-2 supports this with the 'whitespace' parameter to the XMLDocument constructor. (The default is to preserve whitespace, as described above.)</p>
<p>However, you may also use COLLAPSE_WHITESPACE, which will:</p>
<ul>
<li>Remove leading and trailing whitespace</li>
<li>Convert newlines and line-feeds into a space character</li>
<li>Collapse a run of any number of space characters into a single space character</li>
</ul>
<p>Note that (currently) there is a performance impact for using COLLAPSE_WHITESPACE. It essentially causes the XML to be parsed twice.</p>
<h3>Error Reporting</h3>
<p>TinyXML-2 reports the line number of any errors in an XML document that cannot be parsed correctly. In addition, all nodes (elements, declarations, text, comments etc.) and attributes have a line number recorded as they are parsed. This allows an application that performs additional validation of the parsed XML document (e.g. application-implemented DTD validation) to report line number information for error messages.</p>
<h2>Entities</h2>
<p>TinyXML-2 recognizes the pre-defined "character entities", meaning special characters. Namely: </p><pre class="fragment">&amp;amp;   &amp;
&amp;lt;    &lt;
&amp;gt;    &gt;
&amp;quot;  "
&amp;apos;  '
</pre><p> These are recognized when the XML document is read, and translated to their UTF-8 equivalents. For instance, text with the XML of: </p><pre class="fragment">Far &amp;amp; Away
</pre><p> will have the Value() of "Far &amp; Away" when queried from the XMLText object, and will be written back to the XML stream/file as an ampersand.</p>
<p>Additionally, any character can be specified by its Unicode code point: The syntax <code>&amp;#xA0;</code> or <code>&amp;#160;</code> are both to the non-breaking space character. This is called a 'numeric character reference'. Any numeric character reference that isn't one of the special entities above, will be read, but written as a regular code point. The output is correct, but the entity syntax isn't preserved.</p>
<h2>Printing</h2>
<h3>Print to file</h3>
<p>You can directly use the convenience function: </p><pre class="fragment">XMLDocument doc;
...
doc.SaveFile( "foo.xml" );
</pre><p> Or the XMLPrinter class: </p><pre class="fragment">XMLPrinter printer( fp );
doc.Print( &amp;printer );
</pre> <h3>Print to memory</h3>
<p>Printing to memory is supported by the XMLPrinter. </p><pre class="fragment">XMLPrinter printer;
doc.Print( &amp;printer );
// printer.CStr() has a const char* to the XML
</pre> <h3>Print without an XMLDocument</h3>
<p>When loading, an XML parser is very useful. However, sometimes when saving, it just gets in the way. The code is often set up for streaming, and constructing the DOM is just overhead.</p>
<p>The Printer supports the streaming case. The following code prints out a trivially simple XML file without ever creating an XML document. </p><pre class="fragment">XMLPrinter printer( fp );
printer.OpenElement( "foo" );
printer.PushAttribute( "foo", "bar" );
printer.CloseElement();
</pre> <h1>Examples </h1>
<h3>Load and parse an XML file.</h3>
<pre class="fragment">/* ------ Example 1: Load and parse an XML file. ---- */
{
    XMLDocument doc;
    doc.LoadFile( "dream.xml" );
}
</pre> <h3>Lookup information.</h3>
<pre class="fragment">/* ------ Example 2: Lookup information. ---- */
{
    XMLDocument doc;
    doc.LoadFile( "dream.xml" );

    // Structure of the XML file:
    // - Element "PLAY"      the root Element, which is the
    //                       FirstChildElement of the Document
    // - - Element "TITLE"   child of the root PLAY Element
    // - - - Text            child of the TITLE Element

    // Navigate to the title, using the convenience function,
    // with a dangerous lack of error checking.
    const char* title = doc.FirstChildElement( "PLAY" )-&gt;FirstChildElement( "TITLE" )-&gt;GetText();
    printf( "Name of play (1): %s\n", title );

    // Text is just another Node to TinyXML-2. The more
    // general way to get to the XMLText:
    XMLText* textNode = doc.FirstChildElement( "PLAY" )-&gt;FirstChildElement( "TITLE" )-&gt;FirstChild()-&gt;ToText();
    title = textNode-&gt;Value();
    printf( "Name of play (2): %s\n", title );
}
</pre> <h1>Using and Installing </h1>
<p>There are 2 files in TinyXML-2:</p><ul>
<li>tinyxml2.cpp</li>
<li><a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a></li>
</ul>
<p>And additionally a test file:</p><ul>
<li>xmltest.cpp</li>
</ul>
<p>Generally speaking, the intent is that you simply include the tinyxml2.cpp and <a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a> files in your project and build with your other source code.</p>
<p>There is also a CMake build included. CMake is the general build for TinyXML-2. Additional build systems are costly to maintain, and tend to bit-rot.</p>
<p>A Visual Studio project is included, but that is largely for developer convenience, and is not intended to integrate well with other builds.</p>
<h1>Building TinyXML-2 - Using vcpkg </h1>
<p>You can download and install TinyXML-2 using the <a href="https://github.com/Microsoft/vcpkg">vcpkg</a> dependency manager: </p><pre class="fragment">git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
./bootstrap-vcpkg.sh
./vcpkg integrate install
./vcpkg install tinyxml2
</pre><p> The TinyXML-2 port in vcpkg is kept up to date by Microsoft team members and community contributors. If the version is out of date, please <a href="https://github.com/Microsoft/vcpkg">create an issue or pull request</a> on the vcpkg repository.</p>
<h1>Versioning </h1>
<p>TinyXML-2 uses semantic versioning. <a href="http://semver.org/">http://semver.org/</a> Releases are now tagged in github.</p>
<p>Note that the major version will (probably) change fairly rapidly. API changes are fairly common.</p>
<h1>License </h1>
<p>TinyXML-2 is released under the zlib license:</p>
<p>This software is provided 'as-is', without any express or implied warranty. In no event will the authors be held liable for any damages arising from the use of this software.</p>
<p>Permission is granted to anyone to use this software for any purpose, including commercial applications, and to alter it and redistribute it freely, subject to the following restrictions:</p>
<ol type="1">
<li>The origin of this software must not be misrepresented; you must not claim that you wrote the original software. If you use this software in a product, an acknowledgment in the product documentation would be appreciated but is not required.</li>
<li>Altered source versions must be plainly marked as such, and must not be misrepresented as being the original software.</li>
<li>This notice may not be removed or altered from any source distribution.</li>
</ol>
<h1>Contributors </h1>
<p>Thanks very much to everyone who sends suggestions, bugs, ideas, and encouragement. It all helps, and makes this project fun.</p>
<p>The original TinyXML-1 has many contributors, who all deserve thanks in shaping what is a very successful library. Extra thanks to Yves Berquin and Andrew Ellerton who were key contributors.</p>
<p>TinyXML-2 grew from that effort. Lee Thomason is the original author of TinyXML-2 (and TinyXML-1) but TinyXML-2 has been and is being improved by many contributors.</p>
<p>Thanks to John Mackay at <a href="http://john.mackay.rosalilastudio.com">http://john.mackay.rosalilastudio.com</a> for the TinyXML-2 logo! </p>
</div></div><!-- PageDoc -->
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

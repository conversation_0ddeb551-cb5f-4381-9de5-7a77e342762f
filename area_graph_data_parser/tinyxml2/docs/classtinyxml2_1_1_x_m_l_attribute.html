<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: tinyxml2::XMLAttribute Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>tinyxml2</b></li><li class="navelem"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classtinyxml2_1_1_x_m_l_attribute-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">tinyxml2::XMLAttribute Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ab886c486ec19f02ed826f8dc129e5ad8"><td class="memItemLeft" align="right" valign="top"><a id="ab886c486ec19f02ed826f8dc129e5ad8"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#ab886c486ec19f02ed826f8dc129e5ad8">Name</a> () const</td></tr>
<tr class="memdesc:ab886c486ec19f02ed826f8dc129e5ad8"><td class="mdescLeft">&#160;</td><td class="mdescRight">The name of the attribute. <br /></td></tr>
<tr class="separator:ab886c486ec19f02ed826f8dc129e5ad8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1aab1dd0e43ecbcfa306adbcf3a3d853"><td class="memItemLeft" align="right" valign="top"><a id="a1aab1dd0e43ecbcfa306adbcf3a3d853"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a1aab1dd0e43ecbcfa306adbcf3a3d853">Value</a> () const</td></tr>
<tr class="memdesc:a1aab1dd0e43ecbcfa306adbcf3a3d853"><td class="mdescLeft">&#160;</td><td class="mdescRight">The value of the attribute. <br /></td></tr>
<tr class="separator:a1aab1dd0e43ecbcfa306adbcf3a3d853"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a02d5ea924586e35f9c13857d1671b765"><td class="memItemLeft" align="right" valign="top"><a id="a02d5ea924586e35f9c13857d1671b765"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a02d5ea924586e35f9c13857d1671b765">GetLineNum</a> () const</td></tr>
<tr class="memdesc:a02d5ea924586e35f9c13857d1671b765"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the line number the attribute is in, if the document was parsed from a file. <br /></td></tr>
<tr class="separator:a02d5ea924586e35f9c13857d1671b765"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aee53571b21e7ce5421eb929523a8bbe6"><td class="memItemLeft" align="right" valign="top"><a id="aee53571b21e7ce5421eb929523a8bbe6"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#aee53571b21e7ce5421eb929523a8bbe6">Next</a> () const</td></tr>
<tr class="memdesc:aee53571b21e7ce5421eb929523a8bbe6"><td class="mdescLeft">&#160;</td><td class="mdescRight">The next attribute in the list. <br /></td></tr>
<tr class="separator:aee53571b21e7ce5421eb929523a8bbe6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adfa2433f0fdafd5c3880936de9affa80"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#adfa2433f0fdafd5c3880936de9affa80">IntValue</a> () const</td></tr>
<tr class="separator:adfa2433f0fdafd5c3880936de9affa80"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0be5343b08a957c42c02c5d32c35d338"><td class="memItemLeft" align="right" valign="top"><a id="a0be5343b08a957c42c02c5d32c35d338"></a>
unsigned&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a0be5343b08a957c42c02c5d32c35d338">UnsignedValue</a> () const</td></tr>
<tr class="memdesc:a0be5343b08a957c42c02c5d32c35d338"><td class="mdescLeft">&#160;</td><td class="mdescRight">Query as an unsigned integer. See <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#adfa2433f0fdafd5c3880936de9affa80">IntValue()</a> <br /></td></tr>
<tr class="separator:a0be5343b08a957c42c02c5d32c35d338"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a98ce5207344ad33a265b0422addae1ff"><td class="memItemLeft" align="right" valign="top"><a id="a98ce5207344ad33a265b0422addae1ff"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a98ce5207344ad33a265b0422addae1ff">BoolValue</a> () const</td></tr>
<tr class="memdesc:a98ce5207344ad33a265b0422addae1ff"><td class="mdescLeft">&#160;</td><td class="mdescRight">Query as a boolean. See <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#adfa2433f0fdafd5c3880936de9affa80">IntValue()</a> <br /></td></tr>
<tr class="separator:a98ce5207344ad33a265b0422addae1ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4aa73513f54ff0087d3e804f0f54e30f"><td class="memItemLeft" align="right" valign="top"><a id="a4aa73513f54ff0087d3e804f0f54e30f"></a>
double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a4aa73513f54ff0087d3e804f0f54e30f">DoubleValue</a> () const</td></tr>
<tr class="memdesc:a4aa73513f54ff0087d3e804f0f54e30f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Query as a double. See <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#adfa2433f0fdafd5c3880936de9affa80">IntValue()</a> <br /></td></tr>
<tr class="separator:a4aa73513f54ff0087d3e804f0f54e30f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27797b45d21c981257720db94f5f8801"><td class="memItemLeft" align="right" valign="top"><a id="a27797b45d21c981257720db94f5f8801"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a27797b45d21c981257720db94f5f8801">FloatValue</a> () const</td></tr>
<tr class="memdesc:a27797b45d21c981257720db94f5f8801"><td class="mdescLeft">&#160;</td><td class="mdescRight">Query as a float. See <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#adfa2433f0fdafd5c3880936de9affa80">IntValue()</a> <br /></td></tr>
<tr class="separator:a27797b45d21c981257720db94f5f8801"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6d5176260db00ea301c01af8457cd993"><td class="memItemLeft" align="right" valign="top">XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a6d5176260db00ea301c01af8457cd993">QueryIntValue</a> (int *value) const</td></tr>
<tr class="separator:a6d5176260db00ea301c01af8457cd993"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a48a7f3496f1415832e451bd8d09c9cb9"><td class="memItemLeft" align="right" valign="top"><a id="a48a7f3496f1415832e451bd8d09c9cb9"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a48a7f3496f1415832e451bd8d09c9cb9">QueryUnsignedValue</a> (unsigned int *value) const</td></tr>
<tr class="memdesc:a48a7f3496f1415832e451bd8d09c9cb9"><td class="mdescLeft">&#160;</td><td class="mdescRight">See QueryIntValue. <br /></td></tr>
<tr class="separator:a48a7f3496f1415832e451bd8d09c9cb9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e25344d6e4159026be34dbddf1dcac2"><td class="memItemLeft" align="right" valign="top"><a id="a4e25344d6e4159026be34dbddf1dcac2"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a4e25344d6e4159026be34dbddf1dcac2">QueryInt64Value</a> (int64_t *value) const</td></tr>
<tr class="memdesc:a4e25344d6e4159026be34dbddf1dcac2"><td class="mdescLeft">&#160;</td><td class="mdescRight">See QueryIntValue. <br /></td></tr>
<tr class="separator:a4e25344d6e4159026be34dbddf1dcac2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af793c695e7ee65cf20b8010d38b1d157"><td class="memItemLeft" align="right" valign="top"><a id="af793c695e7ee65cf20b8010d38b1d157"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#af793c695e7ee65cf20b8010d38b1d157">QueryUnsigned64Value</a> (uint64_t *value) const</td></tr>
<tr class="memdesc:af793c695e7ee65cf20b8010d38b1d157"><td class="mdescLeft">&#160;</td><td class="mdescRight">See QueryIntValue. <br /></td></tr>
<tr class="separator:af793c695e7ee65cf20b8010d38b1d157"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f32e038954256f61c21ff20fd13a09c"><td class="memItemLeft" align="right" valign="top"><a id="a5f32e038954256f61c21ff20fd13a09c"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a5f32e038954256f61c21ff20fd13a09c">QueryBoolValue</a> (bool *value) const</td></tr>
<tr class="memdesc:a5f32e038954256f61c21ff20fd13a09c"><td class="mdescLeft">&#160;</td><td class="mdescRight">See QueryIntValue. <br /></td></tr>
<tr class="separator:a5f32e038954256f61c21ff20fd13a09c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2aa6e55e8ea03af0609cf6690bff79b9"><td class="memItemLeft" align="right" valign="top"><a id="a2aa6e55e8ea03af0609cf6690bff79b9"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a2aa6e55e8ea03af0609cf6690bff79b9">QueryDoubleValue</a> (double *value) const</td></tr>
<tr class="memdesc:a2aa6e55e8ea03af0609cf6690bff79b9"><td class="mdescLeft">&#160;</td><td class="mdescRight">See QueryIntValue. <br /></td></tr>
<tr class="separator:a2aa6e55e8ea03af0609cf6690bff79b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a049dea6449a6259b6cfed44a9427b607"><td class="memItemLeft" align="right" valign="top"><a id="a049dea6449a6259b6cfed44a9427b607"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a049dea6449a6259b6cfed44a9427b607">QueryFloatValue</a> (float *value) const</td></tr>
<tr class="memdesc:a049dea6449a6259b6cfed44a9427b607"><td class="mdescLeft">&#160;</td><td class="mdescRight">See QueryIntValue. <br /></td></tr>
<tr class="separator:a049dea6449a6259b6cfed44a9427b607"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a406d2c4a13c7af99a65edb59dd9f7581"><td class="memItemLeft" align="right" valign="top"><a id="a406d2c4a13c7af99a65edb59dd9f7581"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a406d2c4a13c7af99a65edb59dd9f7581">SetAttribute</a> (const char *value)</td></tr>
<tr class="memdesc:a406d2c4a13c7af99a65edb59dd9f7581"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the attribute to a string value. <br /></td></tr>
<tr class="separator:a406d2c4a13c7af99a65edb59dd9f7581"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad86d7d7058d76761c3a80662566a57e5"><td class="memItemLeft" align="right" valign="top"><a id="ad86d7d7058d76761c3a80662566a57e5"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#ad86d7d7058d76761c3a80662566a57e5">SetAttribute</a> (int value)</td></tr>
<tr class="memdesc:ad86d7d7058d76761c3a80662566a57e5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the attribute to value. <br /></td></tr>
<tr class="separator:ad86d7d7058d76761c3a80662566a57e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae70468c0f6df2748ba3529c716999fae"><td class="memItemLeft" align="right" valign="top"><a id="ae70468c0f6df2748ba3529c716999fae"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#ae70468c0f6df2748ba3529c716999fae">SetAttribute</a> (unsigned value)</td></tr>
<tr class="memdesc:ae70468c0f6df2748ba3529c716999fae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the attribute to value. <br /></td></tr>
<tr class="separator:ae70468c0f6df2748ba3529c716999fae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7c1240f479722b9aa29b6c030aa116c2"><td class="memItemLeft" align="right" valign="top"><a id="a7c1240f479722b9aa29b6c030aa116c2"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a7c1240f479722b9aa29b6c030aa116c2">SetAttribute</a> (int64_t value)</td></tr>
<tr class="memdesc:a7c1240f479722b9aa29b6c030aa116c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the attribute to value. <br /></td></tr>
<tr class="separator:a7c1240f479722b9aa29b6c030aa116c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a10964060a5c0d92486ecf8705bdf37da"><td class="memItemLeft" align="right" valign="top"><a id="a10964060a5c0d92486ecf8705bdf37da"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a10964060a5c0d92486ecf8705bdf37da">SetAttribute</a> (uint64_t value)</td></tr>
<tr class="memdesc:a10964060a5c0d92486ecf8705bdf37da"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the attribute to value. <br /></td></tr>
<tr class="separator:a10964060a5c0d92486ecf8705bdf37da"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab3516def4fe058fe328f2b89fc2d77da"><td class="memItemLeft" align="right" valign="top"><a id="ab3516def4fe058fe328f2b89fc2d77da"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#ab3516def4fe058fe328f2b89fc2d77da">SetAttribute</a> (bool value)</td></tr>
<tr class="memdesc:ab3516def4fe058fe328f2b89fc2d77da"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the attribute to value. <br /></td></tr>
<tr class="separator:ab3516def4fe058fe328f2b89fc2d77da"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9a65ab3147abe8ccbbd373ce8791e818"><td class="memItemLeft" align="right" valign="top"><a id="a9a65ab3147abe8ccbbd373ce8791e818"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a9a65ab3147abe8ccbbd373ce8791e818">SetAttribute</a> (double value)</td></tr>
<tr class="memdesc:a9a65ab3147abe8ccbbd373ce8791e818"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the attribute to value. <br /></td></tr>
<tr class="separator:a9a65ab3147abe8ccbbd373ce8791e818"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae95e843313aaf5d56c32530b6456df02"><td class="memItemLeft" align="right" valign="top"><a id="ae95e843313aaf5d56c32530b6456df02"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#ae95e843313aaf5d56c32530b6456df02">SetAttribute</a> (float value)</td></tr>
<tr class="memdesc:ae95e843313aaf5d56c32530b6456df02"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the attribute to value. <br /></td></tr>
<tr class="separator:ae95e843313aaf5d56c32530b6456df02"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>An attribute is a name-value pair. Elements have an arbitrary number of attributes, each with a unique name.</p>
<dl class="section note"><dt>Note</dt><dd>The attributes are not XMLNodes. You may only query the <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#aee53571b21e7ce5421eb929523a8bbe6" title="The next attribute in the list.">Next()</a> attribute in a list. </dd></dl>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="adfa2433f0fdafd5c3880936de9affa80"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adfa2433f0fdafd5c3880936de9affa80">&#9670;&nbsp;</a></span>IntValue()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int tinyxml2::XMLAttribute::IntValue </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>IntValue interprets the attribute as an integer, and returns the value. If the value isn't an integer, 0 will be returned. There is no error checking; use <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a6d5176260db00ea301c01af8457cd993">QueryIntValue()</a> if you need error checking. </p>

</div>
</div>
<a id="a6d5176260db00ea301c01af8457cd993"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6d5176260db00ea301c01af8457cd993">&#9670;&nbsp;</a></span>QueryIntValue()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">XMLError tinyxml2::XMLAttribute::QueryIntValue </td>
          <td>(</td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>QueryIntValue interprets the attribute as an integer, and returns the value in the provided parameter. The function will return XML_SUCCESS on success, and XML_WRONG_ATTRIBUTE_TYPE if the conversion is not successful. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

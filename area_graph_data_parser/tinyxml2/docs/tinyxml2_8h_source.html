<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: tinyxml2.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">tinyxml2.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/*</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment">Original code by Lee Thomason (www.grinninglizard.com)</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment">This software is provided &#39;as-is&#39;, without any express or implied</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment">warranty. In no event will the authors be held liable for any</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment">damages arising from the use of this software.</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment">Permission is granted to anyone to use this software for any</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment">purpose, including commercial applications, and to alter it and</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment">redistribute it freely, subject to the following restrictions:</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment">1. The origin of this software must not be misrepresented; you must</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment">not claim that you wrote the original software. If you use this</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment">software in a product, an acknowledgment in the product documentation</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">would be appreciated but is not required.</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment">2. Altered source versions must be plainly marked as such, and</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment">must not be misrepresented as being the original software.</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment">3. This notice may not be removed or altered from any source</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment">distribution.</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment">*/</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160; </div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#ifndef TINYXML2_INCLUDED</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#define TINYXML2_INCLUDED</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160; </div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#if defined(ANDROID_NDK) || defined(__BORLANDC__) || defined(__QNXNTO__)</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#   include &lt;ctype.h&gt;</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#   include &lt;limits.h&gt;</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#   include &lt;stdio.h&gt;</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#   include &lt;stdlib.h&gt;</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#   include &lt;string.h&gt;</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#   if defined(__PS3__)</span></div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#       include &lt;stddef.h&gt;</span></div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#   endif</span></div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#   include &lt;cctype&gt;</span></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#   include &lt;climits&gt;</span></div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#   include &lt;cstdio&gt;</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#   include &lt;cstdlib&gt;</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#   include &lt;cstring&gt;</span></div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor">#include &lt;stdint.h&gt;</span></div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160; </div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="comment">/*</span></div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="comment">   TODO: intern strings instead of allocation.</span></div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="comment">*/</span></div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="comment">/*</span></div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="comment">    gcc:</span></div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="comment">        g++ -Wall -DTINYXML2_DEBUG tinyxml2.cpp xmltest.cpp -o gccxmltest.exe</span></div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;<span class="comment">    Formatting, Artistic Style:</span></div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="comment">        AStyle.exe --style=1tbs --indent-switches --break-closing-brackets --indent-preprocessor tinyxml2.cpp tinyxml2.h</span></div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="comment">*/</span></div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160; </div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;<span class="preprocessor">#if defined( _DEBUG ) || defined (__DEBUG__)</span></div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="preprocessor">#   ifndef TINYXML2_DEBUG</span></div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;<span class="preprocessor">#       define TINYXML2_DEBUG</span></div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="preprocessor">#   endif</span></div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160; </div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;<span class="preprocessor">#ifdef _MSC_VER</span></div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="preprocessor">#   pragma warning(push)</span></div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;<span class="preprocessor">#   pragma warning(disable: 4251)</span></div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160; </div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;<span class="preprocessor">#ifdef _WIN32</span></div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;<span class="preprocessor">#   ifdef TINYXML2_EXPORT</span></div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;<span class="preprocessor">#       define TINYXML2_LIB __declspec(dllexport)</span></div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;<span class="preprocessor">#   elif defined(TINYXML2_IMPORT)</span></div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;<span class="preprocessor">#       define TINYXML2_LIB __declspec(dllimport)</span></div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;<span class="preprocessor">#   else</span></div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;<span class="preprocessor">#       define TINYXML2_LIB</span></div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;<span class="preprocessor">#   endif</span></div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;<span class="preprocessor">#elif __GNUC__ &gt;= 4</span></div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;<span class="preprocessor">#   define TINYXML2_LIB __attribute__((visibility(&quot;default&quot;</span>)))</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;<span class="preprocessor">#   define TINYXML2_LIB</span></div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160; </div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160; </div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;<span class="preprocessor">#if !defined(TIXMLASSERT)</span></div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;<span class="preprocessor">#if defined(TINYXML2_DEBUG)</span></div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;<span class="preprocessor">#   if defined(_MSC_VER)</span></div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;<span class="preprocessor">#       </span><span class="comment">// &quot;(void)0,&quot; is for suppressing C4127 warning in &quot;assert(false)&quot;, &quot;assert(true)&quot; and the like</span></div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;<span class="preprocessor">#       define TIXMLASSERT( x )           if ( !((void)0,(x))) { __debugbreak(); }</span></div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;<span class="preprocessor">#   elif defined (ANDROID_NDK)</span></div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;<span class="preprocessor">#       include &lt;android/log.h&gt;</span></div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;<span class="preprocessor">#       define TIXMLASSERT( x )           if ( !(x)) { __android_log_assert( &quot;assert&quot;</span>, &quot;grinliz&quot;, &quot;ASSERT in &#39;%s&#39; at %d.&quot;, __FILE__, __LINE__ ); }</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;<span class="preprocessor">#   else</span></div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;<span class="preprocessor">#       include &lt;assert.h&gt;</span></div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;<span class="preprocessor">#       define TIXMLASSERT                assert</span></div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;<span class="preprocessor">#   endif</span></div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;<span class="preprocessor">#   define TIXMLASSERT( x )               {}</span></div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160; </div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;<span class="comment">/* Versioning, past 1.0.14:</span></div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;<span class="comment">    http://semver.org/</span></div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;<span class="comment">*/</span></div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;<span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">int</span> TIXML2_MAJOR_VERSION = 9;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">int</span> TIXML2_MINOR_VERSION = 0;</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;<span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">int</span> TIXML2_PATCH_VERSION = 0;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160; </div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;<span class="preprocessor">#define TINYXML2_MAJOR_VERSION 9</span></div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;<span class="preprocessor">#define TINYXML2_MINOR_VERSION 0</span></div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;<span class="preprocessor">#define TINYXML2_PATCH_VERSION 0</span></div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160; </div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;<span class="comment">// A fixed element depth limit is problematic. There needs to be a</span></div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;<span class="comment">// limit to avoid a stack overflow. However, that limit varies per</span></div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;<span class="comment">// system, and the capacity of the stack. On the other hand, it&#39;s a trivial</span></div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;<span class="comment">// attack that can result from ill, malicious, or even correctly formed XML,</span></div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;<span class="comment">// so there needs to be a limit in place.</span></div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;<span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">int</span> TINYXML2_MAX_ELEMENT_DEPTH = 100;</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160; </div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;<span class="keyword">namespace </span>tinyxml2</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;{</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;<span class="keyword">class </span>XMLDocument;</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="keyword">class </span>XMLElement;</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;<span class="keyword">class </span>XMLAttribute;</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;<span class="keyword">class </span>XMLComment;</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;<span class="keyword">class </span>XMLText;</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;<span class="keyword">class </span>XMLDeclaration;</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;<span class="keyword">class </span>XMLUnknown;</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;<span class="keyword">class </span>XMLPrinter;</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160; </div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;<span class="comment">/*</span></div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;<span class="comment">    A class that wraps strings. Normally stores the start and end</span></div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;<span class="comment">    pointers into the XML file itself, and will apply normalization</span></div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;<span class="comment">    and entity translation if actually read. Can also store (and memory</span></div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;<span class="comment">    manage) a traditional char[]</span></div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;<span class="comment">    Isn&#39;t clear why TINYXML2_LIB is needed; but seems to fix #719</span></div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;<span class="comment">*/</span></div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;<span class="keyword">class </span>TINYXML2_LIB StrPair</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;{</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;    <span class="keyword">enum</span> Mode {</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;        NEEDS_ENTITY_PROCESSING         = 0x01,</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;        NEEDS_NEWLINE_NORMALIZATION     = 0x02,</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;        NEEDS_WHITESPACE_COLLAPSING     = 0x04,</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160; </div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        TEXT_ELEMENT                    = NEEDS_ENTITY_PROCESSING | NEEDS_NEWLINE_NORMALIZATION,</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;        TEXT_ELEMENT_LEAVE_ENTITIES     = NEEDS_NEWLINE_NORMALIZATION,</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;        ATTRIBUTE_NAME                  = 0,</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;        ATTRIBUTE_VALUE                 = NEEDS_ENTITY_PROCESSING | NEEDS_NEWLINE_NORMALIZATION,</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;        ATTRIBUTE_VALUE_LEAVE_ENTITIES  = NEEDS_NEWLINE_NORMALIZATION,</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;        COMMENT                         = NEEDS_NEWLINE_NORMALIZATION</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;    };</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160; </div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;    StrPair() : _flags( 0 ), _start( 0 ), _end( 0 ) {}</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;    ~StrPair();</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160; </div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    <span class="keywordtype">void</span> Set( <span class="keywordtype">char</span>* start, <span class="keywordtype">char</span>* end, <span class="keywordtype">int</span> flags ) {</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;        TIXMLASSERT( start );</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;        TIXMLASSERT( end );</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;        Reset();</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;        _start  = start;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;        _end    = end;</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;        _flags  = flags | NEEDS_FLUSH;</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;    }</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160; </div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;    <span class="keyword">const</span> <span class="keywordtype">char</span>* GetStr();</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160; </div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;    <span class="keywordtype">bool</span> Empty()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;        <span class="keywordflow">return</span> _start == _end;</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;    }</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160; </div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;    <span class="keywordtype">void</span> SetInternedStr( <span class="keyword">const</span> <span class="keywordtype">char</span>* str ) {</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;        Reset();</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;        _start = <span class="keyword">const_cast&lt;</span><span class="keywordtype">char</span>*<span class="keyword">&gt;</span>(str);</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;    }</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160; </div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;    <span class="keywordtype">void</span> SetStr( <span class="keyword">const</span> <span class="keywordtype">char</span>* str, <span class="keywordtype">int</span> flags=0 );</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160; </div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;    <span class="keywordtype">char</span>* ParseText( <span class="keywordtype">char</span>* in, <span class="keyword">const</span> <span class="keywordtype">char</span>* endTag, <span class="keywordtype">int</span> strFlags, <span class="keywordtype">int</span>* curLineNumPtr );</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;    <span class="keywordtype">char</span>* ParseName( <span class="keywordtype">char</span>* in );</div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160; </div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;    <span class="keywordtype">void</span> TransferTo( StrPair* other );</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;    <span class="keywordtype">void</span> Reset();</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160; </div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;<span class="keyword">private</span>:</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;    <span class="keywordtype">void</span> CollapseWhitespace();</div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160; </div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;    <span class="keyword">enum</span> {</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;        NEEDS_FLUSH = 0x100,</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;        NEEDS_DELETE = 0x200</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;    };</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160; </div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;    <span class="keywordtype">int</span>     _flags;</div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;    <span class="keywordtype">char</span>*   _start;</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;    <span class="keywordtype">char</span>*   _end;</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160; </div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;    StrPair( <span class="keyword">const</span> StrPair&amp; other );    <span class="comment">// not supported</span></div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;    <span class="keywordtype">void</span> operator=( <span class="keyword">const</span> StrPair&amp; other ); <span class="comment">// not supported, use TransferTo()</span></div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;};</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160; </div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160; </div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;<span class="comment">/*</span></div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;<span class="comment">    A dynamic array of Plain Old Data. Doesn&#39;t support constructors, etc.</span></div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;<span class="comment">    Has a small initial memory pool, so that low or no usage will not</span></div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;<span class="comment">    cause a call to new/delete</span></div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;<span class="comment">*/</span></div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">class</span> T, <span class="keywordtype">int</span> INITIAL_SIZE&gt;</div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;<span class="keyword">class </span>DynArray</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;{</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;    DynArray() :</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;        _mem( _pool ),</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;        _allocated( INITIAL_SIZE ),</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;        _size( 0 )</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;    {</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;    }</div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160; </div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;    ~DynArray() {</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;        <span class="keywordflow">if</span> ( _mem != _pool ) {</div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;            <span class="keyword">delete</span> [] _mem;</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;        }</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;    }</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160; </div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;    <span class="keywordtype">void</span> Clear() {</div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;        _size = 0;</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;    }</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160; </div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;    <span class="keywordtype">void</span> Push( T t ) {</div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;        TIXMLASSERT( _size &lt; INT_MAX );</div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;        EnsureCapacity( _size+1 );</div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;        _mem[_size] = t;</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;        ++_size;</div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;    }</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160; </div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;    T* PushArr( <span class="keywordtype">int</span> count ) {</div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;        TIXMLASSERT( count &gt;= 0 );</div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;        TIXMLASSERT( _size &lt;= INT_MAX - count );</div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;        EnsureCapacity( _size+count );</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;        T* ret = &amp;_mem[_size];</div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;        _size += count;</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;        <span class="keywordflow">return</span> ret;</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;    }</div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160; </div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;    T Pop() {</div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;        TIXMLASSERT( _size &gt; 0 );</div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;        --_size;</div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;        <span class="keywordflow">return</span> _mem[_size];</div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;    }</div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160; </div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;    <span class="keywordtype">void</span> PopArr( <span class="keywordtype">int</span> count ) {</div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;        TIXMLASSERT( _size &gt;= count );</div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;        _size -= count;</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;    }</div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160; </div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;    <span class="keywordtype">bool</span> Empty()<span class="keyword"> const                  </span>{</div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;        <span class="keywordflow">return</span> _size == 0;</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;    }</div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160; </div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;    T&amp; operator[](<span class="keywordtype">int</span> i)                {</div>
<div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;        TIXMLASSERT( i&gt;= 0 &amp;&amp; i &lt; _size );</div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;        <span class="keywordflow">return</span> _mem[i];</div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;    }</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160; </div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;    <span class="keyword">const</span> T&amp; operator[](<span class="keywordtype">int</span> i)<span class="keyword"> const    </span>{</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;        TIXMLASSERT( i&gt;= 0 &amp;&amp; i &lt; _size );</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;        <span class="keywordflow">return</span> _mem[i];</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;    }</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160; </div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;    <span class="keyword">const</span> T&amp; PeekTop()<span class="keyword"> const            </span>{</div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;        TIXMLASSERT( _size &gt; 0 );</div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;        <span class="keywordflow">return</span> _mem[ _size - 1];</div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;    }</div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160; </div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;    <span class="keywordtype">int</span> Size()<span class="keyword"> const                    </span>{</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;        TIXMLASSERT( _size &gt;= 0 );</div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;        <span class="keywordflow">return</span> _size;</div>
<div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;    }</div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160; </div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;    <span class="keywordtype">int</span> Capacity()<span class="keyword"> const                </span>{</div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;        TIXMLASSERT( _allocated &gt;= INITIAL_SIZE );</div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;        <span class="keywordflow">return</span> _allocated;</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;    }</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160; </div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;    <span class="keywordtype">void</span> SwapRemove(<span class="keywordtype">int</span> i) {</div>
<div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;        TIXMLASSERT(i &gt;= 0 &amp;&amp; i &lt; _size);</div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;        TIXMLASSERT(_size &gt; 0);</div>
<div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;        _mem[i] = _mem[_size - 1];</div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;        --_size;</div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;    }</div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160; </div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;    <span class="keyword">const</span> T* Mem()<span class="keyword"> const                </span>{</div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;        TIXMLASSERT( _mem );</div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;        <span class="keywordflow">return</span> _mem;</div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;    }</div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160; </div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;    T* Mem() {</div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;        TIXMLASSERT( _mem );</div>
<div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;        <span class="keywordflow">return</span> _mem;</div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;    }</div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160; </div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;<span class="keyword">private</span>:</div>
<div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;    DynArray( <span class="keyword">const</span> DynArray&amp; ); <span class="comment">// not supported</span></div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;    <span class="keywordtype">void</span> operator=( <span class="keyword">const</span> DynArray&amp; ); <span class="comment">// not supported</span></div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160; </div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;    <span class="keywordtype">void</span> EnsureCapacity( <span class="keywordtype">int</span> cap ) {</div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;        TIXMLASSERT( cap &gt; 0 );</div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;        <span class="keywordflow">if</span> ( cap &gt; _allocated ) {</div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;            TIXMLASSERT( cap &lt;= INT_MAX / 2 );</div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;            <span class="keyword">const</span> <span class="keywordtype">int</span> newAllocated = cap * 2;</div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;            T* newMem = <span class="keyword">new</span> T[newAllocated];</div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;            TIXMLASSERT( newAllocated &gt;= _size );</div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;            memcpy( newMem, _mem, <span class="keyword">sizeof</span>(T)*_size );    <span class="comment">// warning: not using constructors, only works for PODs</span></div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;            <span class="keywordflow">if</span> ( _mem != _pool ) {</div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;                <span class="keyword">delete</span> [] _mem;</div>
<div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;            }</div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;            _mem = newMem;</div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;            _allocated = newAllocated;</div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;        }</div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;    }</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160; </div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;    T*  _mem;</div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;    T   _pool[INITIAL_SIZE];</div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;    <span class="keywordtype">int</span> _allocated;     <span class="comment">// objects allocated</span></div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;    <span class="keywordtype">int</span> _size;          <span class="comment">// number objects in use</span></div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;};</div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160; </div>
<div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160; </div>
<div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;<span class="comment">/*</span></div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;<span class="comment">    Parent virtual class of a pool for fast allocation</span></div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;<span class="comment">    and deallocation of objects.</span></div>
<div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;<span class="comment">*/</span></div>
<div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;<span class="keyword">class </span>MemPool</div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;{</div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;    MemPool() {}</div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;    <span class="keyword">virtual</span> ~MemPool() {}</div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160; </div>
<div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">int</span> ItemSize() <span class="keyword">const</span> = 0;</div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">void</span>* Alloc() = 0;</div>
<div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">void</span> Free( <span class="keywordtype">void</span>* ) = 0;</div>
<div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">void</span> SetTracked() = 0;</div>
<div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;};</div>
<div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160; </div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160; </div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;<span class="comment">/*</span></div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;<span class="comment">    Template child class to create pools of the correct type.</span></div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;<span class="comment">*/</span></div>
<div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;<span class="keyword">template</span>&lt; <span class="keywordtype">int</span> ITEM_SIZE &gt;</div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;<span class="keyword">class </span>MemPoolT : <span class="keyword">public</span> MemPool</div>
<div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;{</div>
<div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;    MemPoolT() : _blockPtrs(), _root(0), _currentAllocs(0), _nAllocs(0), _maxAllocs(0), _nUntracked(0)  {}</div>
<div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;    ~MemPoolT() {</div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;        MemPoolT&lt; ITEM_SIZE &gt;::Clear();</div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;    }</div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160; </div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;    <span class="keywordtype">void</span> Clear() {</div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;        <span class="comment">// Delete the blocks.</span></div>
<div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;        <span class="keywordflow">while</span>( !_blockPtrs.Empty()) {</div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;            Block* lastBlock = _blockPtrs.Pop();</div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;            <span class="keyword">delete</span> lastBlock;</div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;        }</div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;        _root = 0;</div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;        _currentAllocs = 0;</div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;        _nAllocs = 0;</div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;        _maxAllocs = 0;</div>
<div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;        _nUntracked = 0;</div>
<div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;    }</div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160; </div>
<div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">int</span> ItemSize()<span class="keyword"> const    </span>{</div>
<div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;        <span class="keywordflow">return</span> ITEM_SIZE;</div>
<div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;    }</div>
<div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;    <span class="keywordtype">int</span> CurrentAllocs()<span class="keyword"> const       </span>{</div>
<div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;        <span class="keywordflow">return</span> _currentAllocs;</div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;    }</div>
<div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160; </div>
<div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">void</span>* Alloc() {</div>
<div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;        <span class="keywordflow">if</span> ( !_root ) {</div>
<div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;            <span class="comment">// Need a new block.</span></div>
<div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;            Block* block = <span class="keyword">new</span> Block();</div>
<div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;            _blockPtrs.Push( block );</div>
<div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160; </div>
<div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;            Item* blockItems = block-&gt;items;</div>
<div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;            <span class="keywordflow">for</span>( <span class="keywordtype">int</span> i = 0; i &lt; ITEMS_PER_BLOCK - 1; ++i ) {</div>
<div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;                blockItems[i].next = &amp;(blockItems[i + 1]);</div>
<div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;            }</div>
<div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;            blockItems[ITEMS_PER_BLOCK - 1].next = 0;</div>
<div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160;            _root = blockItems;</div>
<div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;        }</div>
<div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;        Item* <span class="keyword">const</span> result = _root;</div>
<div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;        TIXMLASSERT( result != 0 );</div>
<div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;        _root = _root-&gt;next;</div>
<div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160; </div>
<div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;        ++_currentAllocs;</div>
<div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;        <span class="keywordflow">if</span> ( _currentAllocs &gt; _maxAllocs ) {</div>
<div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;            _maxAllocs = _currentAllocs;</div>
<div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;        }</div>
<div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;        ++_nAllocs;</div>
<div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;        ++_nUntracked;</div>
<div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;        <span class="keywordflow">return</span> result;</div>
<div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;    }</div>
<div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160; </div>
<div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">void</span> Free( <span class="keywordtype">void</span>* mem ) {</div>
<div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;        <span class="keywordflow">if</span> ( !mem ) {</div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;            <span class="keywordflow">return</span>;</div>
<div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;        }</div>
<div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160;        --_currentAllocs;</div>
<div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;        Item* item = <span class="keyword">static_cast&lt;</span>Item*<span class="keyword">&gt;</span>( mem );</div>
<div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;<span class="preprocessor">#ifdef TINYXML2_DEBUG</span></div>
<div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;        memset( item, 0xfe, <span class="keyword">sizeof</span>( *item ) );</div>
<div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;        item-&gt;next = _root;</div>
<div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;        _root = item;</div>
<div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;    }</div>
<div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;    <span class="keywordtype">void</span> Trace( <span class="keyword">const</span> <span class="keywordtype">char</span>* name ) {</div>
<div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;        printf( <span class="stringliteral">&quot;Mempool %s watermark=%d [%dk] current=%d size=%d nAlloc=%d blocks=%d\n&quot;</span>,</div>
<div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;                name, _maxAllocs, _maxAllocs * ITEM_SIZE / 1024, _currentAllocs,</div>
<div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;                ITEM_SIZE, _nAllocs, _blockPtrs.Size() );</div>
<div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;    }</div>
<div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160; </div>
<div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;    <span class="keywordtype">void</span> SetTracked() {</div>
<div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;        --_nUntracked;</div>
<div class="line"><a name="l00421"></a><span class="lineno">  421</span>&#160;    }</div>
<div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160; </div>
<div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;    <span class="keywordtype">int</span> Untracked()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;        <span class="keywordflow">return</span> _nUntracked;</div>
<div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;    }</div>
<div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160; </div>
<div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;    <span class="comment">// This number is perf sensitive. 4k seems like a good tradeoff on my machine.</span></div>
<div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;    <span class="comment">// The test file is large, 170k.</span></div>
<div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;    <span class="comment">// Release:     VS2010 gcc(no opt)</span></div>
<div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160;    <span class="comment">//      1k:     4000</span></div>
<div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;    <span class="comment">//      2k:     4000</span></div>
<div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;    <span class="comment">//      4k:     3900    21000</span></div>
<div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;    <span class="comment">//      16k:    5200</span></div>
<div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160;    <span class="comment">//      32k:    4300</span></div>
<div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;    <span class="comment">//      64k:    4000    21000</span></div>
<div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;    <span class="comment">// Declared public because some compilers do not accept to use ITEMS_PER_BLOCK</span></div>
<div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;    <span class="comment">// in private part if ITEMS_PER_BLOCK is private</span></div>
<div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160;    <span class="keyword">enum</span> { ITEMS_PER_BLOCK = (4 * 1024) / ITEM_SIZE };</div>
<div class="line"><a name="l00439"></a><span class="lineno">  439</span>&#160; </div>
<div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;<span class="keyword">private</span>:</div>
<div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160;    MemPoolT( <span class="keyword">const</span> MemPoolT&amp; ); <span class="comment">// not supported</span></div>
<div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;    <span class="keywordtype">void</span> operator=( <span class="keyword">const</span> MemPoolT&amp; ); <span class="comment">// not supported</span></div>
<div class="line"><a name="l00443"></a><span class="lineno">  443</span>&#160; </div>
<div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;    <span class="keyword">union </span>Item {</div>
<div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;        Item*   next;</div>
<div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160;        <span class="keywordtype">char</span>    itemData[ITEM_SIZE];</div>
<div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;    };</div>
<div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160;    <span class="keyword">struct </span>Block {</div>
<div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;        Item items[ITEMS_PER_BLOCK];</div>
<div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;    };</div>
<div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;    DynArray&lt; Block*, 10 &gt; _blockPtrs;</div>
<div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;    Item* _root;</div>
<div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160; </div>
<div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160;    <span class="keywordtype">int</span> _currentAllocs;</div>
<div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;    <span class="keywordtype">int</span> _nAllocs;</div>
<div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160;    <span class="keywordtype">int</span> _maxAllocs;</div>
<div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;    <span class="keywordtype">int</span> _nUntracked;</div>
<div class="line"><a name="l00458"></a><span class="lineno">  458</span>&#160;};</div>
<div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160; </div>
<div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160; </div>
<div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160; </div>
<div class="line"><a name="l00481"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_visitor.html">  481</a></span>&#160;<span class="keyword">class </span>TINYXML2_LIB <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a></div>
<div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;{</div>
<div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00484"></a><span class="lineno">  484</span>&#160;    <span class="keyword">virtual</span> ~<a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a>() {}</div>
<div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160; </div>
<div class="line"><a name="l00487"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_visitor.html#acb3c22fc5f60eb9db98f533f2761f67d">  487</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html#acb3c22fc5f60eb9db98f533f2761f67d">VisitEnter</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>&amp; <span class="comment">/*doc*/</span> )           {</div>
<div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">true</span>;</div>
<div class="line"><a name="l00489"></a><span class="lineno">  489</span>&#160;    }</div>
<div class="line"><a name="l00491"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_visitor.html#a170e9989cd046ba904f302d087e07086">  491</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html#a170e9989cd046ba904f302d087e07086">VisitExit</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>&amp; <span class="comment">/*doc*/</span> )            {</div>
<div class="line"><a name="l00492"></a><span class="lineno">  492</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">true</span>;</div>
<div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;    }</div>
<div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160; </div>
<div class="line"><a name="l00496"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_visitor.html#af97980a17dd4e37448b181f5ddfa92b5">  496</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html#af97980a17dd4e37448b181f5ddfa92b5">VisitEnter</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>&amp; <span class="comment">/*element*/</span>, <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* <span class="comment">/*firstAttribute*/</span> )    {</div>
<div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">true</span>;</div>
<div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;    }</div>
<div class="line"><a name="l00500"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_visitor.html#a772f10ddc83f881956d32628faa16eb6">  500</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html#a772f10ddc83f881956d32628faa16eb6">VisitExit</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>&amp; <span class="comment">/*element*/</span> )         {</div>
<div class="line"><a name="l00501"></a><span class="lineno">  501</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">true</span>;</div>
<div class="line"><a name="l00502"></a><span class="lineno">  502</span>&#160;    }</div>
<div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160; </div>
<div class="line"><a name="l00505"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_visitor.html#adc75bd459fc7ba8223b50f0616767f9a">  505</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html#adc75bd459fc7ba8223b50f0616767f9a">Visit</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>&amp; <span class="comment">/*declaration*/</span> )     {</div>
<div class="line"><a name="l00506"></a><span class="lineno">  506</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">true</span>;</div>
<div class="line"><a name="l00507"></a><span class="lineno">  507</span>&#160;    }</div>
<div class="line"><a name="l00509"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_visitor.html#af30233565856480ea48b6fa0d6dec65b">  509</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html#af30233565856480ea48b6fa0d6dec65b">Visit</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>&amp; <span class="comment">/*text*/</span> )                   {</div>
<div class="line"><a name="l00510"></a><span class="lineno">  510</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">true</span>;</div>
<div class="line"><a name="l00511"></a><span class="lineno">  511</span>&#160;    }</div>
<div class="line"><a name="l00513"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_visitor.html#acc8147fb5a85f6c65721654e427752d7">  513</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html#acc8147fb5a85f6c65721654e427752d7">Visit</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a>&amp; <span class="comment">/*comment*/</span> )             {</div>
<div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">true</span>;</div>
<div class="line"><a name="l00515"></a><span class="lineno">  515</span>&#160;    }</div>
<div class="line"><a name="l00517"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_visitor.html#a14e4748387c34bf53d24e8119bb1f292">  517</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html#a14e4748387c34bf53d24e8119bb1f292">Visit</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>&amp; <span class="comment">/*unknown*/</span> )             {</div>
<div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">true</span>;</div>
<div class="line"><a name="l00519"></a><span class="lineno">  519</span>&#160;    }</div>
<div class="line"><a name="l00520"></a><span class="lineno">  520</span>&#160;};</div>
<div class="line"><a name="l00521"></a><span class="lineno">  521</span>&#160; </div>
<div class="line"><a name="l00522"></a><span class="lineno">  522</span>&#160;<span class="comment">// WARNING: must match XMLDocument::_errorNames[]</span></div>
<div class="line"><a name="l00523"></a><span class="lineno">  523</span>&#160;<span class="keyword">enum</span> XMLError {</div>
<div class="line"><a name="l00524"></a><span class="lineno">  524</span>&#160;    XML_SUCCESS = 0,</div>
<div class="line"><a name="l00525"></a><span class="lineno">  525</span>&#160;    XML_NO_ATTRIBUTE,</div>
<div class="line"><a name="l00526"></a><span class="lineno">  526</span>&#160;    XML_WRONG_ATTRIBUTE_TYPE,</div>
<div class="line"><a name="l00527"></a><span class="lineno">  527</span>&#160;    XML_ERROR_FILE_NOT_FOUND,</div>
<div class="line"><a name="l00528"></a><span class="lineno">  528</span>&#160;    XML_ERROR_FILE_COULD_NOT_BE_OPENED,</div>
<div class="line"><a name="l00529"></a><span class="lineno">  529</span>&#160;    XML_ERROR_FILE_READ_ERROR,</div>
<div class="line"><a name="l00530"></a><span class="lineno">  530</span>&#160;    XML_ERROR_PARSING_ELEMENT,</div>
<div class="line"><a name="l00531"></a><span class="lineno">  531</span>&#160;    XML_ERROR_PARSING_ATTRIBUTE,</div>
<div class="line"><a name="l00532"></a><span class="lineno">  532</span>&#160;    XML_ERROR_PARSING_TEXT,</div>
<div class="line"><a name="l00533"></a><span class="lineno">  533</span>&#160;    XML_ERROR_PARSING_CDATA,</div>
<div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;    XML_ERROR_PARSING_COMMENT,</div>
<div class="line"><a name="l00535"></a><span class="lineno">  535</span>&#160;    XML_ERROR_PARSING_DECLARATION,</div>
<div class="line"><a name="l00536"></a><span class="lineno">  536</span>&#160;    XML_ERROR_PARSING_UNKNOWN,</div>
<div class="line"><a name="l00537"></a><span class="lineno">  537</span>&#160;    XML_ERROR_EMPTY_DOCUMENT,</div>
<div class="line"><a name="l00538"></a><span class="lineno">  538</span>&#160;    XML_ERROR_MISMATCHED_ELEMENT,</div>
<div class="line"><a name="l00539"></a><span class="lineno">  539</span>&#160;    XML_ERROR_PARSING,</div>
<div class="line"><a name="l00540"></a><span class="lineno">  540</span>&#160;    XML_CAN_NOT_CONVERT_TEXT,</div>
<div class="line"><a name="l00541"></a><span class="lineno">  541</span>&#160;    XML_NO_TEXT_NODE,</div>
<div class="line"><a name="l00542"></a><span class="lineno">  542</span>&#160;    XML_ELEMENT_DEPTH_EXCEEDED,</div>
<div class="line"><a name="l00543"></a><span class="lineno">  543</span>&#160; </div>
<div class="line"><a name="l00544"></a><span class="lineno">  544</span>&#160;    XML_ERROR_COUNT</div>
<div class="line"><a name="l00545"></a><span class="lineno">  545</span>&#160;};</div>
<div class="line"><a name="l00546"></a><span class="lineno">  546</span>&#160; </div>
<div class="line"><a name="l00547"></a><span class="lineno">  547</span>&#160; </div>
<div class="line"><a name="l00548"></a><span class="lineno">  548</span>&#160;<span class="comment">/*</span></div>
<div class="line"><a name="l00549"></a><span class="lineno">  549</span>&#160;<span class="comment">    Utility functionality.</span></div>
<div class="line"><a name="l00550"></a><span class="lineno">  550</span>&#160;<span class="comment">*/</span></div>
<div class="line"><a name="l00551"></a><span class="lineno">  551</span>&#160;<span class="keyword">class </span>TINYXML2_LIB XMLUtil</div>
<div class="line"><a name="l00552"></a><span class="lineno">  552</span>&#160;{</div>
<div class="line"><a name="l00553"></a><span class="lineno">  553</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00554"></a><span class="lineno">  554</span>&#160;    <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span>* SkipWhiteSpace( <span class="keyword">const</span> <span class="keywordtype">char</span>* p, <span class="keywordtype">int</span>* curLineNumPtr )  {</div>
<div class="line"><a name="l00555"></a><span class="lineno">  555</span>&#160;        TIXMLASSERT( p );</div>
<div class="line"><a name="l00556"></a><span class="lineno">  556</span>&#160; </div>
<div class="line"><a name="l00557"></a><span class="lineno">  557</span>&#160;        <span class="keywordflow">while</span>( IsWhiteSpace(*p) ) {</div>
<div class="line"><a name="l00558"></a><span class="lineno">  558</span>&#160;            <span class="keywordflow">if</span> (curLineNumPtr &amp;&amp; *p == <span class="charliteral">&#39;\n&#39;</span>) {</div>
<div class="line"><a name="l00559"></a><span class="lineno">  559</span>&#160;                ++(*curLineNumPtr);</div>
<div class="line"><a name="l00560"></a><span class="lineno">  560</span>&#160;            }</div>
<div class="line"><a name="l00561"></a><span class="lineno">  561</span>&#160;            ++p;</div>
<div class="line"><a name="l00562"></a><span class="lineno">  562</span>&#160;        }</div>
<div class="line"><a name="l00563"></a><span class="lineno">  563</span>&#160;        TIXMLASSERT( p );</div>
<div class="line"><a name="l00564"></a><span class="lineno">  564</span>&#160;        <span class="keywordflow">return</span> p;</div>
<div class="line"><a name="l00565"></a><span class="lineno">  565</span>&#160;    }</div>
<div class="line"><a name="l00566"></a><span class="lineno">  566</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">char</span>* SkipWhiteSpace( <span class="keywordtype">char</span>* <span class="keyword">const</span> p, <span class="keywordtype">int</span>* curLineNumPtr ) {</div>
<div class="line"><a name="l00567"></a><span class="lineno">  567</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">const_cast&lt;</span><span class="keywordtype">char</span>*<span class="keyword">&gt;</span>( SkipWhiteSpace( <span class="keyword">const_cast&lt;</span><span class="keyword">const </span><span class="keywordtype">char</span>*<span class="keyword">&gt;</span>(p), curLineNumPtr ) );</div>
<div class="line"><a name="l00568"></a><span class="lineno">  568</span>&#160;    }</div>
<div class="line"><a name="l00569"></a><span class="lineno">  569</span>&#160; </div>
<div class="line"><a name="l00570"></a><span class="lineno">  570</span>&#160;    <span class="comment">// Anything in the high order range of UTF-8 is assumed to not be whitespace. This isn&#39;t</span></div>
<div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160;    <span class="comment">// correct, but simple, and usually works.</span></div>
<div class="line"><a name="l00572"></a><span class="lineno">  572</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">bool</span> IsWhiteSpace( <span class="keywordtype">char</span> p )                  {</div>
<div class="line"><a name="l00573"></a><span class="lineno">  573</span>&#160;        <span class="keywordflow">return</span> !IsUTF8Continuation(p) &amp;&amp; isspace( <span class="keyword">static_cast&lt;</span><span class="keywordtype">unsigned</span> <span class="keywordtype">char</span><span class="keyword">&gt;</span>(p) );</div>
<div class="line"><a name="l00574"></a><span class="lineno">  574</span>&#160;    }</div>
<div class="line"><a name="l00575"></a><span class="lineno">  575</span>&#160; </div>
<div class="line"><a name="l00576"></a><span class="lineno">  576</span>&#160;    <span class="keyword">inline</span> <span class="keyword">static</span> <span class="keywordtype">bool</span> IsNameStartChar( <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> ch ) {</div>
<div class="line"><a name="l00577"></a><span class="lineno">  577</span>&#160;        <span class="keywordflow">if</span> ( ch &gt;= 128 ) {</div>
<div class="line"><a name="l00578"></a><span class="lineno">  578</span>&#160;            <span class="comment">// This is a heuristic guess in attempt to not implement Unicode-aware isalpha()</span></div>
<div class="line"><a name="l00579"></a><span class="lineno">  579</span>&#160;            <span class="keywordflow">return</span> <span class="keyword">true</span>;</div>
<div class="line"><a name="l00580"></a><span class="lineno">  580</span>&#160;        }</div>
<div class="line"><a name="l00581"></a><span class="lineno">  581</span>&#160;        <span class="keywordflow">if</span> ( isalpha( ch ) ) {</div>
<div class="line"><a name="l00582"></a><span class="lineno">  582</span>&#160;            <span class="keywordflow">return</span> <span class="keyword">true</span>;</div>
<div class="line"><a name="l00583"></a><span class="lineno">  583</span>&#160;        }</div>
<div class="line"><a name="l00584"></a><span class="lineno">  584</span>&#160;        <span class="keywordflow">return</span> ch == <span class="charliteral">&#39;:&#39;</span> || ch == <span class="charliteral">&#39;_&#39;</span>;</div>
<div class="line"><a name="l00585"></a><span class="lineno">  585</span>&#160;    }</div>
<div class="line"><a name="l00586"></a><span class="lineno">  586</span>&#160; </div>
<div class="line"><a name="l00587"></a><span class="lineno">  587</span>&#160;    <span class="keyword">inline</span> <span class="keyword">static</span> <span class="keywordtype">bool</span> IsNameChar( <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> ch ) {</div>
<div class="line"><a name="l00588"></a><span class="lineno">  588</span>&#160;        <span class="keywordflow">return</span> IsNameStartChar( ch )</div>
<div class="line"><a name="l00589"></a><span class="lineno">  589</span>&#160;               || isdigit( ch )</div>
<div class="line"><a name="l00590"></a><span class="lineno">  590</span>&#160;               || ch == <span class="charliteral">&#39;.&#39;</span></div>
<div class="line"><a name="l00591"></a><span class="lineno">  591</span>&#160;               || ch == <span class="charliteral">&#39;-&#39;</span>;</div>
<div class="line"><a name="l00592"></a><span class="lineno">  592</span>&#160;    }</div>
<div class="line"><a name="l00593"></a><span class="lineno">  593</span>&#160; </div>
<div class="line"><a name="l00594"></a><span class="lineno">  594</span>&#160;    <span class="keyword">inline</span> <span class="keyword">static</span> <span class="keywordtype">bool</span> IsPrefixHex( <span class="keyword">const</span> <span class="keywordtype">char</span>* p) {</div>
<div class="line"><a name="l00595"></a><span class="lineno">  595</span>&#160;        p = SkipWhiteSpace(p, 0);</div>
<div class="line"><a name="l00596"></a><span class="lineno">  596</span>&#160;        <span class="keywordflow">return</span> p &amp;&amp; *p == <span class="charliteral">&#39;0&#39;</span> &amp;&amp; ( *(p + 1) == <span class="charliteral">&#39;x&#39;</span> || *(p + 1) == <span class="charliteral">&#39;X&#39;</span>);</div>
<div class="line"><a name="l00597"></a><span class="lineno">  597</span>&#160;    }</div>
<div class="line"><a name="l00598"></a><span class="lineno">  598</span>&#160; </div>
<div class="line"><a name="l00599"></a><span class="lineno">  599</span>&#160;    <span class="keyword">inline</span> <span class="keyword">static</span> <span class="keywordtype">bool</span> StringEqual( <span class="keyword">const</span> <span class="keywordtype">char</span>* p, <span class="keyword">const</span> <span class="keywordtype">char</span>* q, <span class="keywordtype">int</span> nChar=INT_MAX )  {</div>
<div class="line"><a name="l00600"></a><span class="lineno">  600</span>&#160;        <span class="keywordflow">if</span> ( p == q ) {</div>
<div class="line"><a name="l00601"></a><span class="lineno">  601</span>&#160;            <span class="keywordflow">return</span> <span class="keyword">true</span>;</div>
<div class="line"><a name="l00602"></a><span class="lineno">  602</span>&#160;        }</div>
<div class="line"><a name="l00603"></a><span class="lineno">  603</span>&#160;        TIXMLASSERT( p );</div>
<div class="line"><a name="l00604"></a><span class="lineno">  604</span>&#160;        TIXMLASSERT( q );</div>
<div class="line"><a name="l00605"></a><span class="lineno">  605</span>&#160;        TIXMLASSERT( nChar &gt;= 0 );</div>
<div class="line"><a name="l00606"></a><span class="lineno">  606</span>&#160;        <span class="keywordflow">return</span> strncmp( p, q, nChar ) == 0;</div>
<div class="line"><a name="l00607"></a><span class="lineno">  607</span>&#160;    }</div>
<div class="line"><a name="l00608"></a><span class="lineno">  608</span>&#160; </div>
<div class="line"><a name="l00609"></a><span class="lineno">  609</span>&#160;    <span class="keyword">inline</span> <span class="keyword">static</span> <span class="keywordtype">bool</span> IsUTF8Continuation( <span class="keyword">const</span> <span class="keywordtype">char</span> p ) {</div>
<div class="line"><a name="l00610"></a><span class="lineno">  610</span>&#160;        <span class="keywordflow">return</span> ( p &amp; 0x80 ) != 0;</div>
<div class="line"><a name="l00611"></a><span class="lineno">  611</span>&#160;    }</div>
<div class="line"><a name="l00612"></a><span class="lineno">  612</span>&#160; </div>
<div class="line"><a name="l00613"></a><span class="lineno">  613</span>&#160;    <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span>* ReadBOM( <span class="keyword">const</span> <span class="keywordtype">char</span>* p, <span class="keywordtype">bool</span>* hasBOM );</div>
<div class="line"><a name="l00614"></a><span class="lineno">  614</span>&#160;    <span class="comment">// p is the starting location,</span></div>
<div class="line"><a name="l00615"></a><span class="lineno">  615</span>&#160;    <span class="comment">// the UTF-8 value of the entity will be placed in value, and length filled in.</span></div>
<div class="line"><a name="l00616"></a><span class="lineno">  616</span>&#160;    <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span>* GetCharacterRef( <span class="keyword">const</span> <span class="keywordtype">char</span>* p, <span class="keywordtype">char</span>* value, <span class="keywordtype">int</span>* length );</div>
<div class="line"><a name="l00617"></a><span class="lineno">  617</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">void</span> ConvertUTF32ToUTF8( <span class="keywordtype">unsigned</span> <span class="keywordtype">long</span> input, <span class="keywordtype">char</span>* output, <span class="keywordtype">int</span>* length );</div>
<div class="line"><a name="l00618"></a><span class="lineno">  618</span>&#160; </div>
<div class="line"><a name="l00619"></a><span class="lineno">  619</span>&#160;    <span class="comment">// converts primitive types to strings</span></div>
<div class="line"><a name="l00620"></a><span class="lineno">  620</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">void</span> ToStr( <span class="keywordtype">int</span> v, <span class="keywordtype">char</span>* buffer, <span class="keywordtype">int</span> bufferSize );</div>
<div class="line"><a name="l00621"></a><span class="lineno">  621</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">void</span> ToStr( <span class="keywordtype">unsigned</span> v, <span class="keywordtype">char</span>* buffer, <span class="keywordtype">int</span> bufferSize );</div>
<div class="line"><a name="l00622"></a><span class="lineno">  622</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">void</span> ToStr( <span class="keywordtype">bool</span> v, <span class="keywordtype">char</span>* buffer, <span class="keywordtype">int</span> bufferSize );</div>
<div class="line"><a name="l00623"></a><span class="lineno">  623</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">void</span> ToStr( <span class="keywordtype">float</span> v, <span class="keywordtype">char</span>* buffer, <span class="keywordtype">int</span> bufferSize );</div>
<div class="line"><a name="l00624"></a><span class="lineno">  624</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">void</span> ToStr( <span class="keywordtype">double</span> v, <span class="keywordtype">char</span>* buffer, <span class="keywordtype">int</span> bufferSize );</div>
<div class="line"><a name="l00625"></a><span class="lineno">  625</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">void</span> ToStr(int64_t v, <span class="keywordtype">char</span>* buffer, <span class="keywordtype">int</span> bufferSize);</div>
<div class="line"><a name="l00626"></a><span class="lineno">  626</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">void</span> ToStr(uint64_t v, <span class="keywordtype">char</span>* buffer, <span class="keywordtype">int</span> bufferSize);</div>
<div class="line"><a name="l00627"></a><span class="lineno">  627</span>&#160; </div>
<div class="line"><a name="l00628"></a><span class="lineno">  628</span>&#160;    <span class="comment">// converts strings to primitive types</span></div>
<div class="line"><a name="l00629"></a><span class="lineno">  629</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">bool</span> ToInt( <span class="keyword">const</span> <span class="keywordtype">char</span>* str, <span class="keywordtype">int</span>* value );</div>
<div class="line"><a name="l00630"></a><span class="lineno">  630</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">bool</span> ToUnsigned( <span class="keyword">const</span> <span class="keywordtype">char</span>* str, <span class="keywordtype">unsigned</span>* value );</div>
<div class="line"><a name="l00631"></a><span class="lineno">  631</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">bool</span> ToBool( <span class="keyword">const</span> <span class="keywordtype">char</span>* str, <span class="keywordtype">bool</span>* value );</div>
<div class="line"><a name="l00632"></a><span class="lineno">  632</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">bool</span> ToFloat( <span class="keyword">const</span> <span class="keywordtype">char</span>* str, <span class="keywordtype">float</span>* value );</div>
<div class="line"><a name="l00633"></a><span class="lineno">  633</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">bool</span> ToDouble( <span class="keyword">const</span> <span class="keywordtype">char</span>* str, <span class="keywordtype">double</span>* value );</div>
<div class="line"><a name="l00634"></a><span class="lineno">  634</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">bool</span> ToInt64(<span class="keyword">const</span> <span class="keywordtype">char</span>* str, int64_t* value);</div>
<div class="line"><a name="l00635"></a><span class="lineno">  635</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">bool</span> ToUnsigned64(<span class="keyword">const</span> <span class="keywordtype">char</span>* str, uint64_t* value);</div>
<div class="line"><a name="l00636"></a><span class="lineno">  636</span>&#160;    <span class="comment">// Changes what is serialized for a boolean value.</span></div>
<div class="line"><a name="l00637"></a><span class="lineno">  637</span>&#160;    <span class="comment">// Default to &quot;true&quot; and &quot;false&quot;. Shouldn&#39;t be changed</span></div>
<div class="line"><a name="l00638"></a><span class="lineno">  638</span>&#160;    <span class="comment">// unless you have a special testing or compatibility need.</span></div>
<div class="line"><a name="l00639"></a><span class="lineno">  639</span>&#160;    <span class="comment">// Be careful: static, global, &amp; not thread safe.</span></div>
<div class="line"><a name="l00640"></a><span class="lineno">  640</span>&#160;    <span class="comment">// Be sure to set static const memory as parameters.</span></div>
<div class="line"><a name="l00641"></a><span class="lineno">  641</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">void</span> SetBoolSerialization(<span class="keyword">const</span> <span class="keywordtype">char</span>* writeTrue, <span class="keyword">const</span> <span class="keywordtype">char</span>* writeFalse);</div>
<div class="line"><a name="l00642"></a><span class="lineno">  642</span>&#160; </div>
<div class="line"><a name="l00643"></a><span class="lineno">  643</span>&#160;<span class="keyword">private</span>:</div>
<div class="line"><a name="l00644"></a><span class="lineno">  644</span>&#160;    <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span>* writeBoolTrue;</div>
<div class="line"><a name="l00645"></a><span class="lineno">  645</span>&#160;    <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span>* writeBoolFalse;</div>
<div class="line"><a name="l00646"></a><span class="lineno">  646</span>&#160;};</div>
<div class="line"><a name="l00647"></a><span class="lineno">  647</span>&#160; </div>
<div class="line"><a name="l00648"></a><span class="lineno">  648</span>&#160; </div>
<div class="line"><a name="l00674"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html">  674</a></span>&#160;<span class="keyword">class </span>TINYXML2_LIB <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a></div>
<div class="line"><a name="l00675"></a><span class="lineno">  675</span>&#160;{</div>
<div class="line"><a name="l00676"></a><span class="lineno">  676</span>&#160;    <span class="keyword">friend</span> <span class="keyword">class </span><a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>;</div>
<div class="line"><a name="l00677"></a><span class="lineno">  677</span>&#160;    <span class="keyword">friend</span> <span class="keyword">class </span><a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>;</div>
<div class="line"><a name="l00678"></a><span class="lineno">  678</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00679"></a><span class="lineno">  679</span>&#160; </div>
<div class="line"><a name="l00681"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a2de84cfa4ec3fe249bad745069d145f1">  681</a></span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a2de84cfa4ec3fe249bad745069d145f1">GetDocument</a>()<span class="keyword"> const  </span>{</div>
<div class="line"><a name="l00682"></a><span class="lineno">  682</span>&#160;        TIXMLASSERT( _document );</div>
<div class="line"><a name="l00683"></a><span class="lineno">  683</span>&#160;        <span class="keywordflow">return</span> _document;</div>
<div class="line"><a name="l00684"></a><span class="lineno">  684</span>&#160;    }</div>
<div class="line"><a name="l00686"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#af343d1ef0b45c0020e62d784d7e67a68">  686</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#af343d1ef0b45c0020e62d784d7e67a68">GetDocument</a>()              {</div>
<div class="line"><a name="l00687"></a><span class="lineno">  687</span>&#160;        TIXMLASSERT( _document );</div>
<div class="line"><a name="l00688"></a><span class="lineno">  688</span>&#160;        <span class="keywordflow">return</span> _document;</div>
<div class="line"><a name="l00689"></a><span class="lineno">  689</span>&#160;    }</div>
<div class="line"><a name="l00690"></a><span class="lineno">  690</span>&#160; </div>
<div class="line"><a name="l00692"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#aab516e699567f75cc9ab2ef2eee501e8">  692</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>*     <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#aab516e699567f75cc9ab2ef2eee501e8">ToElement</a>()     {</div>
<div class="line"><a name="l00693"></a><span class="lineno">  693</span>&#160;        <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l00694"></a><span class="lineno">  694</span>&#160;    }</div>
<div class="line"><a name="l00696"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a41c55dab9162d1eb62db2008430e376b">  696</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>*        <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a41c55dab9162d1eb62db2008430e376b">ToText</a>()        {</div>
<div class="line"><a name="l00697"></a><span class="lineno">  697</span>&#160;        <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l00698"></a><span class="lineno">  698</span>&#160;    }</div>
<div class="line"><a name="l00700"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#aff47671055aa99840a1c1ebd661e63e3">  700</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a>*     <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#aff47671055aa99840a1c1ebd661e63e3">ToComment</a>()     {</div>
<div class="line"><a name="l00701"></a><span class="lineno">  701</span>&#160;        <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l00702"></a><span class="lineno">  702</span>&#160;    }</div>
<div class="line"><a name="l00704"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a836e2966ed736fc3c94f70e12a2a3357">  704</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>*    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a836e2966ed736fc3c94f70e12a2a3357">ToDocument</a>()    {</div>
<div class="line"><a name="l00705"></a><span class="lineno">  705</span>&#160;        <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l00706"></a><span class="lineno">  706</span>&#160;    }</div>
<div class="line"><a name="l00708"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a174fd4c22c010b58138c1b84a0dfbd51">  708</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a174fd4c22c010b58138c1b84a0dfbd51">ToDeclaration</a>() {</div>
<div class="line"><a name="l00709"></a><span class="lineno">  709</span>&#160;        <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l00710"></a><span class="lineno">  710</span>&#160;    }</div>
<div class="line"><a name="l00712"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a8675a74aa0ada6eccab0c77ef3e5b9bd">  712</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>*     <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a8675a74aa0ada6eccab0c77ef3e5b9bd">ToUnknown</a>()     {</div>
<div class="line"><a name="l00713"></a><span class="lineno">  713</span>&#160;        <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l00714"></a><span class="lineno">  714</span>&#160;    }</div>
<div class="line"><a name="l00715"></a><span class="lineno">  715</span>&#160; </div>
<div class="line"><a name="l00716"></a><span class="lineno">  716</span>&#160;    <span class="keyword">virtual</span> <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>*       ToElement()<span class="keyword"> const       </span>{</div>
<div class="line"><a name="l00717"></a><span class="lineno">  717</span>&#160;        <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l00718"></a><span class="lineno">  718</span>&#160;    }</div>
<div class="line"><a name="l00719"></a><span class="lineno">  719</span>&#160;    <span class="keyword">virtual</span> <span class="keyword">const</span> XMLText*          ToText()<span class="keyword"> const          </span>{</div>
<div class="line"><a name="l00720"></a><span class="lineno">  720</span>&#160;        <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l00721"></a><span class="lineno">  721</span>&#160;    }</div>
<div class="line"><a name="l00722"></a><span class="lineno">  722</span>&#160;    <span class="keyword">virtual</span> <span class="keyword">const</span> XMLComment*       ToComment()<span class="keyword"> const       </span>{</div>
<div class="line"><a name="l00723"></a><span class="lineno">  723</span>&#160;        <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l00724"></a><span class="lineno">  724</span>&#160;    }</div>
<div class="line"><a name="l00725"></a><span class="lineno">  725</span>&#160;    <span class="keyword">virtual</span> <span class="keyword">const</span> XMLDocument*      ToDocument()<span class="keyword"> const      </span>{</div>
<div class="line"><a name="l00726"></a><span class="lineno">  726</span>&#160;        <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l00727"></a><span class="lineno">  727</span>&#160;    }</div>
<div class="line"><a name="l00728"></a><span class="lineno">  728</span>&#160;    <span class="keyword">virtual</span> <span class="keyword">const</span> XMLDeclaration*   ToDeclaration()<span class="keyword"> const   </span>{</div>
<div class="line"><a name="l00729"></a><span class="lineno">  729</span>&#160;        <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l00730"></a><span class="lineno">  730</span>&#160;    }</div>
<div class="line"><a name="l00731"></a><span class="lineno">  731</span>&#160;    <span class="keyword">virtual</span> <span class="keyword">const</span> XMLUnknown*       ToUnknown()<span class="keyword"> const       </span>{</div>
<div class="line"><a name="l00732"></a><span class="lineno">  732</span>&#160;        <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l00733"></a><span class="lineno">  733</span>&#160;    }</div>
<div class="line"><a name="l00734"></a><span class="lineno">  734</span>&#160; </div>
<div class="line"><a name="l00744"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a66344989a4b436155bcda72bd6b07b82">  744</a></span>&#160;    <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a66344989a4b436155bcda72bd6b07b82">Value</a>() <span class="keyword">const</span>;</div>
<div class="line"><a name="l00745"></a><span class="lineno">  745</span>&#160; </div>
<div class="line"><a name="l00749"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a09dd68cf9eae137579f6e50f36487513">  749</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a09dd68cf9eae137579f6e50f36487513">SetValue</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* val, <span class="keywordtype">bool</span> staticMem=<span class="keyword">false</span> );</div>
<div class="line"><a name="l00750"></a><span class="lineno">  750</span>&#160; </div>
<div class="line"><a name="l00752"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a9b5fc636646fda761d342c72e91cb286">  752</a></span>&#160;    <span class="keywordtype">int</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a9b5fc636646fda761d342c72e91cb286">GetLineNum</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> _parseLineNum; }</div>
<div class="line"><a name="l00753"></a><span class="lineno">  753</span>&#160; </div>
<div class="line"><a name="l00755"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#ae0f62bc186c56c2e0483ebd52dbfbe34">  755</a></span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*  <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#ae0f62bc186c56c2e0483ebd52dbfbe34">Parent</a>()<span class="keyword"> const          </span>{</div>
<div class="line"><a name="l00756"></a><span class="lineno">  756</span>&#160;        <span class="keywordflow">return</span> _parent;</div>
<div class="line"><a name="l00757"></a><span class="lineno">  757</span>&#160;    }</div>
<div class="line"><a name="l00758"></a><span class="lineno">  758</span>&#160; </div>
<div class="line"><a name="l00759"></a><span class="lineno">  759</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* Parent()                       {</div>
<div class="line"><a name="l00760"></a><span class="lineno">  760</span>&#160;        <span class="keywordflow">return</span> _parent;</div>
<div class="line"><a name="l00761"></a><span class="lineno">  761</span>&#160;    }</div>
<div class="line"><a name="l00762"></a><span class="lineno">  762</span>&#160; </div>
<div class="line"><a name="l00764"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#ac3ab489e6e202a3cd1762d3b332e89d4">  764</a></span>&#160;    <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#ac3ab489e6e202a3cd1762d3b332e89d4">NoChildren</a>()<span class="keyword"> const                 </span>{</div>
<div class="line"><a name="l00765"></a><span class="lineno">  765</span>&#160;        <span class="keywordflow">return</span> !_firstChild;</div>
<div class="line"><a name="l00766"></a><span class="lineno">  766</span>&#160;    }</div>
<div class="line"><a name="l00767"></a><span class="lineno">  767</span>&#160; </div>
<div class="line"><a name="l00769"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#ae7dc225e1018cdd685f7563593a1fe08">  769</a></span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*  <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#ae7dc225e1018cdd685f7563593a1fe08">FirstChild</a>()<span class="keyword"> const      </span>{</div>
<div class="line"><a name="l00770"></a><span class="lineno">  770</span>&#160;        <span class="keywordflow">return</span> _firstChild;</div>
<div class="line"><a name="l00771"></a><span class="lineno">  771</span>&#160;    }</div>
<div class="line"><a name="l00772"></a><span class="lineno">  772</span>&#160; </div>
<div class="line"><a name="l00773"></a><span class="lineno">  773</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*        FirstChild()            {</div>
<div class="line"><a name="l00774"></a><span class="lineno">  774</span>&#160;        <span class="keywordflow">return</span> _firstChild;</div>
<div class="line"><a name="l00775"></a><span class="lineno">  775</span>&#160;    }</div>
<div class="line"><a name="l00776"></a><span class="lineno">  776</span>&#160; </div>
<div class="line"><a name="l00780"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a1795a35852dc8aae877cc8ded986e59b">  780</a></span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a1795a35852dc8aae877cc8ded986e59b">FirstChildElement</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name = 0 ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l00781"></a><span class="lineno">  781</span>&#160; </div>
<div class="line"><a name="l00782"></a><span class="lineno">  782</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* FirstChildElement( <span class="keyword">const</span> <span class="keywordtype">char</span>* name = 0 )   {</div>
<div class="line"><a name="l00783"></a><span class="lineno">  783</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">const_cast&lt;</span><a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>*<span class="keyword">&gt;</span>(<span class="keyword">const_cast&lt;</span><span class="keyword">const </span><a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*<span class="keyword">&gt;</span>(<span class="keyword">this</span>)-&gt;FirstChildElement( name ));</div>
<div class="line"><a name="l00784"></a><span class="lineno">  784</span>&#160;    }</div>
<div class="line"><a name="l00785"></a><span class="lineno">  785</span>&#160; </div>
<div class="line"><a name="l00787"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a9b8583a277e8e26f4cbbb5492786778e">  787</a></span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*  <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a9b8583a277e8e26f4cbbb5492786778e">LastChild</a>()<span class="keyword"> const                       </span>{</div>
<div class="line"><a name="l00788"></a><span class="lineno">  788</span>&#160;        <span class="keywordflow">return</span> _lastChild;</div>
<div class="line"><a name="l00789"></a><span class="lineno">  789</span>&#160;    }</div>
<div class="line"><a name="l00790"></a><span class="lineno">  790</span>&#160; </div>
<div class="line"><a name="l00791"></a><span class="lineno">  791</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*        LastChild()                             {</div>
<div class="line"><a name="l00792"></a><span class="lineno">  792</span>&#160;        <span class="keywordflow">return</span> _lastChild;</div>
<div class="line"><a name="l00793"></a><span class="lineno">  793</span>&#160;    }</div>
<div class="line"><a name="l00794"></a><span class="lineno">  794</span>&#160; </div>
<div class="line"><a name="l00798"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a173e9d1341bc56992e2d320a35936551">  798</a></span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a173e9d1341bc56992e2d320a35936551">LastChildElement</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name = 0 ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l00799"></a><span class="lineno">  799</span>&#160; </div>
<div class="line"><a name="l00800"></a><span class="lineno">  800</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* LastChildElement( <span class="keyword">const</span> <span class="keywordtype">char</span>* name = 0 )    {</div>
<div class="line"><a name="l00801"></a><span class="lineno">  801</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">const_cast&lt;</span><a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>*<span class="keyword">&gt;</span>(<span class="keyword">const_cast&lt;</span><span class="keyword">const </span><a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*<span class="keyword">&gt;</span>(<span class="keyword">this</span>)-&gt;LastChildElement(name) );</div>
<div class="line"><a name="l00802"></a><span class="lineno">  802</span>&#160;    }</div>
<div class="line"><a name="l00803"></a><span class="lineno">  803</span>&#160; </div>
<div class="line"><a name="l00805"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#aac667c513d445f8b783e1e15ef9d3551">  805</a></span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*  <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#aac667c513d445f8b783e1e15ef9d3551">PreviousSibling</a>()<span class="keyword"> const                 </span>{</div>
<div class="line"><a name="l00806"></a><span class="lineno">  806</span>&#160;        <span class="keywordflow">return</span> _prev;</div>
<div class="line"><a name="l00807"></a><span class="lineno">  807</span>&#160;    }</div>
<div class="line"><a name="l00808"></a><span class="lineno">  808</span>&#160; </div>
<div class="line"><a name="l00809"></a><span class="lineno">  809</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*    PreviousSibling()                           {</div>
<div class="line"><a name="l00810"></a><span class="lineno">  810</span>&#160;        <span class="keywordflow">return</span> _prev;</div>
<div class="line"><a name="l00811"></a><span class="lineno">  811</span>&#160;    }</div>
<div class="line"><a name="l00812"></a><span class="lineno">  812</span>&#160; </div>
<div class="line"><a name="l00814"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a872936cae46fb473eb47fec99129fc70">  814</a></span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>*   <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a872936cae46fb473eb47fec99129fc70">PreviousSiblingElement</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name = 0 ) <span class="keyword">const</span> ;</div>
<div class="line"><a name="l00815"></a><span class="lineno">  815</span>&#160; </div>
<div class="line"><a name="l00816"></a><span class="lineno">  816</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* PreviousSiblingElement( <span class="keyword">const</span> <span class="keywordtype">char</span>* name = 0 ) {</div>
<div class="line"><a name="l00817"></a><span class="lineno">  817</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">const_cast&lt;</span><a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>*<span class="keyword">&gt;</span>(<span class="keyword">const_cast&lt;</span><span class="keyword">const </span><a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*<span class="keyword">&gt;</span>(<span class="keyword">this</span>)-&gt;PreviousSiblingElement( name ) );</div>
<div class="line"><a name="l00818"></a><span class="lineno">  818</span>&#160;    }</div>
<div class="line"><a name="l00819"></a><span class="lineno">  819</span>&#160; </div>
<div class="line"><a name="l00821"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a79db9ef0fe014d27790f2218b87bcbb5">  821</a></span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*  <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a79db9ef0fe014d27790f2218b87bcbb5">NextSibling</a>()<span class="keyword"> const                     </span>{</div>
<div class="line"><a name="l00822"></a><span class="lineno">  822</span>&#160;        <span class="keywordflow">return</span> _next;</div>
<div class="line"><a name="l00823"></a><span class="lineno">  823</span>&#160;    }</div>
<div class="line"><a name="l00824"></a><span class="lineno">  824</span>&#160; </div>
<div class="line"><a name="l00825"></a><span class="lineno">  825</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*    NextSibling()                               {</div>
<div class="line"><a name="l00826"></a><span class="lineno">  826</span>&#160;        <span class="keywordflow">return</span> _next;</div>
<div class="line"><a name="l00827"></a><span class="lineno">  827</span>&#160;    }</div>
<div class="line"><a name="l00828"></a><span class="lineno">  828</span>&#160; </div>
<div class="line"><a name="l00830"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a1264c86233328f0cd36297552d982f80">  830</a></span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>*   <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a1264c86233328f0cd36297552d982f80">NextSiblingElement</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name = 0 ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l00831"></a><span class="lineno">  831</span>&#160; </div>
<div class="line"><a name="l00832"></a><span class="lineno">  832</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* NextSiblingElement( <span class="keyword">const</span> <span class="keywordtype">char</span>* name = 0 )  {</div>
<div class="line"><a name="l00833"></a><span class="lineno">  833</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">const_cast&lt;</span><a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>*<span class="keyword">&gt;</span>(<span class="keyword">const_cast&lt;</span><span class="keyword">const </span><a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*<span class="keyword">&gt;</span>(<span class="keyword">this</span>)-&gt;NextSiblingElement( name ) );</div>
<div class="line"><a name="l00834"></a><span class="lineno">  834</span>&#160;    }</div>
<div class="line"><a name="l00835"></a><span class="lineno">  835</span>&#160; </div>
<div class="line"><a name="l00843"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#aeb249ed60f4e8bfad3709151c3ee4286">  843</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#aeb249ed60f4e8bfad3709151c3ee4286">InsertEndChild</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* addThis );</div>
<div class="line"><a name="l00844"></a><span class="lineno">  844</span>&#160; </div>
<div class="line"><a name="l00845"></a><span class="lineno">  845</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* LinkEndChild( <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* addThis )   {</div>
<div class="line"><a name="l00846"></a><span class="lineno">  846</span>&#160;        <span class="keywordflow">return</span> InsertEndChild( addThis );</div>
<div class="line"><a name="l00847"></a><span class="lineno">  847</span>&#160;    }</div>
<div class="line"><a name="l00855"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a8ff7dc071f3a1a6ae2ac25a37492865d">  855</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a8ff7dc071f3a1a6ae2ac25a37492865d">InsertFirstChild</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* addThis );</div>
<div class="line"><a name="l00864"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a85adb8f0b7477eec30f9a41d420b09c2">  864</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a85adb8f0b7477eec30f9a41d420b09c2">InsertAfterChild</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* afterThis, <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* addThis );</div>
<div class="line"><a name="l00865"></a><span class="lineno">  865</span>&#160; </div>
<div class="line"><a name="l00869"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a0360085cc54df5bff85d5c5da13afdce">  869</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a0360085cc54df5bff85d5c5da13afdce">DeleteChildren</a>();</div>
<div class="line"><a name="l00870"></a><span class="lineno">  870</span>&#160; </div>
<div class="line"><a name="l00874"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a363b6edbd6ebd55f8387d2b89f2b0921">  874</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a363b6edbd6ebd55f8387d2b89f2b0921">DeleteChild</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* node );</div>
<div class="line"><a name="l00875"></a><span class="lineno">  875</span>&#160; </div>
<div class="line"><a name="l00885"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a8402cbd3129d20e9e6024bbcc0531283">  885</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a8402cbd3129d20e9e6024bbcc0531283">ShallowClone</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* document ) <span class="keyword">const</span> = 0;</div>
<div class="line"><a name="l00886"></a><span class="lineno">  886</span>&#160; </div>
<div class="line"><a name="l00900"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a62c71b6bf8734b5424063b8d9a61c266">  900</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a62c71b6bf8734b5424063b8d9a61c266">DeepClone</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* target ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l00901"></a><span class="lineno">  901</span>&#160; </div>
<div class="line"><a name="l00908"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a7ce18b751c3ea09eac292dca264f9226">  908</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a7ce18b751c3ea09eac292dca264f9226">ShallowEqual</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* compare ) <span class="keyword">const</span> = 0;</div>
<div class="line"><a name="l00909"></a><span class="lineno">  909</span>&#160; </div>
<div class="line"><a name="l00932"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a81e66df0a44c67a7af17f3b77a152785">  932</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a81e66df0a44c67a7af17f3b77a152785">Accept</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a>* visitor ) <span class="keyword">const</span> = 0;</div>
<div class="line"><a name="l00933"></a><span class="lineno">  933</span>&#160; </div>
<div class="line"><a name="l00939"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a002978fc889cc011d143185f2377eca2">  939</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a002978fc889cc011d143185f2377eca2">SetUserData</a>(<span class="keywordtype">void</span>* userData)    { _userData = userData; }</div>
<div class="line"><a name="l00940"></a><span class="lineno">  940</span>&#160; </div>
<div class="line"><a name="l00946"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_node.html#a7f0687574afa03bc479dc44f29db0afe">  946</a></span>&#160;    <span class="keywordtype">void</span>* <a class="code" href="classtinyxml2_1_1_x_m_l_node.html#a7f0687574afa03bc479dc44f29db0afe">GetUserData</a>()<span class="keyword"> const           </span>{ <span class="keywordflow">return</span> _userData; }</div>
<div class="line"><a name="l00947"></a><span class="lineno">  947</span>&#160; </div>
<div class="line"><a name="l00948"></a><span class="lineno">  948</span>&#160;<span class="keyword">protected</span>:</div>
<div class="line"><a name="l00949"></a><span class="lineno">  949</span>&#160;    <span class="keyword">explicit</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* );</div>
<div class="line"><a name="l00950"></a><span class="lineno">  950</span>&#160;    <span class="keyword">virtual</span> ~<a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>();</div>
<div class="line"><a name="l00951"></a><span class="lineno">  951</span>&#160; </div>
<div class="line"><a name="l00952"></a><span class="lineno">  952</span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">char</span>* ParseDeep( <span class="keywordtype">char</span>* p, StrPair* parentEndTag, <span class="keywordtype">int</span>* curLineNumPtr);</div>
<div class="line"><a name="l00953"></a><span class="lineno">  953</span>&#160; </div>
<div class="line"><a name="l00954"></a><span class="lineno">  954</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>*    _document;</div>
<div class="line"><a name="l00955"></a><span class="lineno">  955</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*        _parent;</div>
<div class="line"><a name="l00956"></a><span class="lineno">  956</span>&#160;    <span class="keyword">mutable</span> StrPair _value;</div>
<div class="line"><a name="l00957"></a><span class="lineno">  957</span>&#160;    <span class="keywordtype">int</span>             _parseLineNum;</div>
<div class="line"><a name="l00958"></a><span class="lineno">  958</span>&#160; </div>
<div class="line"><a name="l00959"></a><span class="lineno">  959</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*        _firstChild;</div>
<div class="line"><a name="l00960"></a><span class="lineno">  960</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*        _lastChild;</div>
<div class="line"><a name="l00961"></a><span class="lineno">  961</span>&#160; </div>
<div class="line"><a name="l00962"></a><span class="lineno">  962</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*        _prev;</div>
<div class="line"><a name="l00963"></a><span class="lineno">  963</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>*        _next;</div>
<div class="line"><a name="l00964"></a><span class="lineno">  964</span>&#160; </div>
<div class="line"><a name="l00965"></a><span class="lineno">  965</span>&#160;    <span class="keywordtype">void</span>*           _userData;</div>
<div class="line"><a name="l00966"></a><span class="lineno">  966</span>&#160; </div>
<div class="line"><a name="l00967"></a><span class="lineno">  967</span>&#160;<span class="keyword">private</span>:</div>
<div class="line"><a name="l00968"></a><span class="lineno">  968</span>&#160;    MemPool*        _memPool;</div>
<div class="line"><a name="l00969"></a><span class="lineno">  969</span>&#160;    <span class="keywordtype">void</span> Unlink( <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* child );</div>
<div class="line"><a name="l00970"></a><span class="lineno">  970</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">void</span> DeleteNode( <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* node );</div>
<div class="line"><a name="l00971"></a><span class="lineno">  971</span>&#160;    <span class="keywordtype">void</span> InsertChildPreamble( <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* insertThis ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l00972"></a><span class="lineno">  972</span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* ToElementWithName( <span class="keyword">const</span> <span class="keywordtype">char</span>* name ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l00973"></a><span class="lineno">  973</span>&#160; </div>
<div class="line"><a name="l00974"></a><span class="lineno">  974</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>&amp; );  <span class="comment">// not supported</span></div>
<div class="line"><a name="l00975"></a><span class="lineno">  975</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>&amp; operator=( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>&amp; );   <span class="comment">// not supported</span></div>
<div class="line"><a name="l00976"></a><span class="lineno">  976</span>&#160;};</div>
<div class="line"><a name="l00977"></a><span class="lineno">  977</span>&#160; </div>
<div class="line"><a name="l00978"></a><span class="lineno">  978</span>&#160; </div>
<div class="line"><a name="l00991"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_text.html">  991</a></span>&#160;<span class="keyword">class </span>TINYXML2_LIB <a class="code" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a> : <span class="keyword">public</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a></div>
<div class="line"><a name="l00992"></a><span class="lineno">  992</span>&#160;{</div>
<div class="line"><a name="l00993"></a><span class="lineno">  993</span>&#160;    <span class="keyword">friend</span> <span class="keyword">class </span><a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>;</div>
<div class="line"><a name="l00994"></a><span class="lineno">  994</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00995"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_text.html#a1b2c1448f1a21299d0a7913f18b55206">  995</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_text.html#a1b2c1448f1a21299d0a7913f18b55206">Accept</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a>* visitor ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l00996"></a><span class="lineno">  996</span>&#160; </div>
<div class="line"><a name="l00997"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_text.html#ab1213b4ddebe9b17ec7e7040e9f1caf7">  997</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_text.html#ab1213b4ddebe9b17ec7e7040e9f1caf7">ToText</a>()           {</div>
<div class="line"><a name="l00998"></a><span class="lineno">  998</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">this</span>;</div>
<div class="line"><a name="l00999"></a><span class="lineno">  999</span>&#160;    }</div>
<div class="line"><a name="l01000"></a><span class="lineno"> 1000</span>&#160;    <span class="keyword">virtual</span> <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>* ToText()<span class="keyword"> const   </span>{</div>
<div class="line"><a name="l01001"></a><span class="lineno"> 1001</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">this</span>;</div>
<div class="line"><a name="l01002"></a><span class="lineno"> 1002</span>&#160;    }</div>
<div class="line"><a name="l01003"></a><span class="lineno"> 1003</span>&#160; </div>
<div class="line"><a name="l01005"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_text.html#ad080357d76ab7cc59d7651249949329d"> 1005</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_text.html#ad080357d76ab7cc59d7651249949329d">SetCData</a>( <span class="keywordtype">bool</span> isCData )           {</div>
<div class="line"><a name="l01006"></a><span class="lineno"> 1006</span>&#160;        _isCData = isCData;</div>
<div class="line"><a name="l01007"></a><span class="lineno"> 1007</span>&#160;    }</div>
<div class="line"><a name="l01009"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_text.html#ac1bb5ea4166c320882d9e0ad16fd385b"> 1009</a></span>&#160;    <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_text.html#ac1bb5ea4166c320882d9e0ad16fd385b">CData</a>()<span class="keyword"> const                      </span>{</div>
<div class="line"><a name="l01010"></a><span class="lineno"> 1010</span>&#160;        <span class="keywordflow">return</span> _isCData;</div>
<div class="line"><a name="l01011"></a><span class="lineno"> 1011</span>&#160;    }</div>
<div class="line"><a name="l01012"></a><span class="lineno"> 1012</span>&#160; </div>
<div class="line"><a name="l01013"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_text.html#af3a81ed4dd49d5151c477b3f265a3011"> 1013</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_text.html#af3a81ed4dd49d5151c477b3f265a3011">ShallowClone</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* document ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01014"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_text.html#ae0fff8a24e2de7eb073fd192e9db0331"> 1014</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_text.html#ae0fff8a24e2de7eb073fd192e9db0331">ShallowEqual</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* compare ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01015"></a><span class="lineno"> 1015</span>&#160; </div>
<div class="line"><a name="l01016"></a><span class="lineno"> 1016</span>&#160;<span class="keyword">protected</span>:</div>
<div class="line"><a name="l01017"></a><span class="lineno"> 1017</span>&#160;    <span class="keyword">explicit</span> <a class="code" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* doc )    : <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>( doc ), _isCData( false ) {}</div>
<div class="line"><a name="l01018"></a><span class="lineno"> 1018</span>&#160;    <span class="keyword">virtual</span> ~<a class="code" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>()                                              {}</div>
<div class="line"><a name="l01019"></a><span class="lineno"> 1019</span>&#160; </div>
<div class="line"><a name="l01020"></a><span class="lineno"> 1020</span>&#160;    <span class="keywordtype">char</span>* ParseDeep( <span class="keywordtype">char</span>* p, StrPair* parentEndTag, <span class="keywordtype">int</span>* curLineNumPtr );</div>
<div class="line"><a name="l01021"></a><span class="lineno"> 1021</span>&#160; </div>
<div class="line"><a name="l01022"></a><span class="lineno"> 1022</span>&#160;<span class="keyword">private</span>:</div>
<div class="line"><a name="l01023"></a><span class="lineno"> 1023</span>&#160;    <span class="keywordtype">bool</span> _isCData;</div>
<div class="line"><a name="l01024"></a><span class="lineno"> 1024</span>&#160; </div>
<div class="line"><a name="l01025"></a><span class="lineno"> 1025</span>&#160;    XMLText( <span class="keyword">const</span> XMLText&amp; );  <span class="comment">// not supported</span></div>
<div class="line"><a name="l01026"></a><span class="lineno"> 1026</span>&#160;    XMLText&amp; operator=( <span class="keyword">const</span> XMLText&amp; );   <span class="comment">// not supported</span></div>
<div class="line"><a name="l01027"></a><span class="lineno"> 1027</span>&#160;};</div>
<div class="line"><a name="l01028"></a><span class="lineno"> 1028</span>&#160; </div>
<div class="line"><a name="l01029"></a><span class="lineno"> 1029</span>&#160; </div>
<div class="line"><a name="l01031"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_comment.html"> 1031</a></span>&#160;<span class="keyword">class </span>TINYXML2_LIB <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a> : <span class="keyword">public</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a></div>
<div class="line"><a name="l01032"></a><span class="lineno"> 1032</span>&#160;{</div>
<div class="line"><a name="l01033"></a><span class="lineno"> 1033</span>&#160;    <span class="keyword">friend</span> <span class="keyword">class </span><a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>;</div>
<div class="line"><a name="l01034"></a><span class="lineno"> 1034</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l01035"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_comment.html#a8093e1dc8a34fa446d9dc3fde0e6c0ee"> 1035</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html#a8093e1dc8a34fa446d9dc3fde0e6c0ee">ToComment</a>()                 {</div>
<div class="line"><a name="l01036"></a><span class="lineno"> 1036</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">this</span>;</div>
<div class="line"><a name="l01037"></a><span class="lineno"> 1037</span>&#160;    }</div>
<div class="line"><a name="l01038"></a><span class="lineno"> 1038</span>&#160;    <span class="keyword">virtual</span> <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a>* ToComment()<span class="keyword"> const     </span>{</div>
<div class="line"><a name="l01039"></a><span class="lineno"> 1039</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">this</span>;</div>
<div class="line"><a name="l01040"></a><span class="lineno"> 1040</span>&#160;    }</div>
<div class="line"><a name="l01041"></a><span class="lineno"> 1041</span>&#160; </div>
<div class="line"><a name="l01042"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_comment.html#a4a33dc32fae0285b03f9cfcb3e43e122"> 1042</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html#a4a33dc32fae0285b03f9cfcb3e43e122">Accept</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a>* visitor ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01043"></a><span class="lineno"> 1043</span>&#160; </div>
<div class="line"><a name="l01044"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_comment.html#a08991cc63fadf7e95078ac4f9ea1b073"> 1044</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html#a08991cc63fadf7e95078ac4f9ea1b073">ShallowClone</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* document ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01045"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_comment.html#a6f7d227b25afa8cc3c763b7cc8833739"> 1045</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html#a6f7d227b25afa8cc3c763b7cc8833739">ShallowEqual</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* compare ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01046"></a><span class="lineno"> 1046</span>&#160; </div>
<div class="line"><a name="l01047"></a><span class="lineno"> 1047</span>&#160;<span class="keyword">protected</span>:</div>
<div class="line"><a name="l01048"></a><span class="lineno"> 1048</span>&#160;    <span class="keyword">explicit</span> <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* doc );</div>
<div class="line"><a name="l01049"></a><span class="lineno"> 1049</span>&#160;    <span class="keyword">virtual</span> ~<a class="code" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a>();</div>
<div class="line"><a name="l01050"></a><span class="lineno"> 1050</span>&#160; </div>
<div class="line"><a name="l01051"></a><span class="lineno"> 1051</span>&#160;    <span class="keywordtype">char</span>* ParseDeep( <span class="keywordtype">char</span>* p, StrPair* parentEndTag, <span class="keywordtype">int</span>* curLineNumPtr);</div>
<div class="line"><a name="l01052"></a><span class="lineno"> 1052</span>&#160; </div>
<div class="line"><a name="l01053"></a><span class="lineno"> 1053</span>&#160;<span class="keyword">private</span>:</div>
<div class="line"><a name="l01054"></a><span class="lineno"> 1054</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a>&amp; );    <span class="comment">// not supported</span></div>
<div class="line"><a name="l01055"></a><span class="lineno"> 1055</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a>&amp; operator=( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a>&amp; ); <span class="comment">// not supported</span></div>
<div class="line"><a name="l01056"></a><span class="lineno"> 1056</span>&#160;};</div>
<div class="line"><a name="l01057"></a><span class="lineno"> 1057</span>&#160; </div>
<div class="line"><a name="l01058"></a><span class="lineno"> 1058</span>&#160; </div>
<div class="line"><a name="l01070"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_declaration.html"> 1070</a></span>&#160;<span class="keyword">class </span>TINYXML2_LIB <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a> : <span class="keyword">public</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a></div>
<div class="line"><a name="l01071"></a><span class="lineno"> 1071</span>&#160;{</div>
<div class="line"><a name="l01072"></a><span class="lineno"> 1072</span>&#160;    <span class="keyword">friend</span> <span class="keyword">class </span><a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>;</div>
<div class="line"><a name="l01073"></a><span class="lineno"> 1073</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l01074"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_declaration.html#a159d8ac45865215e88059ea1e5b52fc5"> 1074</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html#a159d8ac45865215e88059ea1e5b52fc5">ToDeclaration</a>()                 {</div>
<div class="line"><a name="l01075"></a><span class="lineno"> 1075</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">this</span>;</div>
<div class="line"><a name="l01076"></a><span class="lineno"> 1076</span>&#160;    }</div>
<div class="line"><a name="l01077"></a><span class="lineno"> 1077</span>&#160;    <span class="keyword">virtual</span> <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>* ToDeclaration()<span class="keyword"> const     </span>{</div>
<div class="line"><a name="l01078"></a><span class="lineno"> 1078</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">this</span>;</div>
<div class="line"><a name="l01079"></a><span class="lineno"> 1079</span>&#160;    }</div>
<div class="line"><a name="l01080"></a><span class="lineno"> 1080</span>&#160; </div>
<div class="line"><a name="l01081"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_declaration.html#a5f376019fb34752eb248548f42f32045"> 1081</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html#a5f376019fb34752eb248548f42f32045">Accept</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a>* visitor ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01082"></a><span class="lineno"> 1082</span>&#160; </div>
<div class="line"><a name="l01083"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_declaration.html#a118d47518dd9e522644e42efa259aed7"> 1083</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html#a118d47518dd9e522644e42efa259aed7">ShallowClone</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* document ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01084"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_declaration.html#aa26b70011694e9b9e9480b929e9b78d6"> 1084</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html#aa26b70011694e9b9e9480b929e9b78d6">ShallowEqual</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* compare ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01085"></a><span class="lineno"> 1085</span>&#160; </div>
<div class="line"><a name="l01086"></a><span class="lineno"> 1086</span>&#160;<span class="keyword">protected</span>:</div>
<div class="line"><a name="l01087"></a><span class="lineno"> 1087</span>&#160;    <span class="keyword">explicit</span> <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* doc );</div>
<div class="line"><a name="l01088"></a><span class="lineno"> 1088</span>&#160;    <span class="keyword">virtual</span> ~<a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>();</div>
<div class="line"><a name="l01089"></a><span class="lineno"> 1089</span>&#160; </div>
<div class="line"><a name="l01090"></a><span class="lineno"> 1090</span>&#160;    <span class="keywordtype">char</span>* ParseDeep( <span class="keywordtype">char</span>* p, StrPair* parentEndTag, <span class="keywordtype">int</span>* curLineNumPtr );</div>
<div class="line"><a name="l01091"></a><span class="lineno"> 1091</span>&#160; </div>
<div class="line"><a name="l01092"></a><span class="lineno"> 1092</span>&#160;<span class="keyword">private</span>:</div>
<div class="line"><a name="l01093"></a><span class="lineno"> 1093</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>&amp; );    <span class="comment">// not supported</span></div>
<div class="line"><a name="l01094"></a><span class="lineno"> 1094</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>&amp; operator=( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>&amp; ); <span class="comment">// not supported</span></div>
<div class="line"><a name="l01095"></a><span class="lineno"> 1095</span>&#160;};</div>
<div class="line"><a name="l01096"></a><span class="lineno"> 1096</span>&#160; </div>
<div class="line"><a name="l01097"></a><span class="lineno"> 1097</span>&#160; </div>
<div class="line"><a name="l01105"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_unknown.html"> 1105</a></span>&#160;<span class="keyword">class </span>TINYXML2_LIB <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a> : <span class="keyword">public</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a></div>
<div class="line"><a name="l01106"></a><span class="lineno"> 1106</span>&#160;{</div>
<div class="line"><a name="l01107"></a><span class="lineno"> 1107</span>&#160;    <span class="keyword">friend</span> <span class="keyword">class </span><a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>;</div>
<div class="line"><a name="l01108"></a><span class="lineno"> 1108</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l01109"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_unknown.html#af4374856421921cad578c8affae872b6"> 1109</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html#af4374856421921cad578c8affae872b6">ToUnknown</a>()                 {</div>
<div class="line"><a name="l01110"></a><span class="lineno"> 1110</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">this</span>;</div>
<div class="line"><a name="l01111"></a><span class="lineno"> 1111</span>&#160;    }</div>
<div class="line"><a name="l01112"></a><span class="lineno"> 1112</span>&#160;    <span class="keyword">virtual</span> <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>* ToUnknown()<span class="keyword"> const     </span>{</div>
<div class="line"><a name="l01113"></a><span class="lineno"> 1113</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">this</span>;</div>
<div class="line"><a name="l01114"></a><span class="lineno"> 1114</span>&#160;    }</div>
<div class="line"><a name="l01115"></a><span class="lineno"> 1115</span>&#160; </div>
<div class="line"><a name="l01116"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_unknown.html#a70983aa1b1cff3d3aa6d4d0a80e5ee48"> 1116</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html#a70983aa1b1cff3d3aa6d4d0a80e5ee48">Accept</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a>* visitor ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01117"></a><span class="lineno"> 1117</span>&#160; </div>
<div class="line"><a name="l01118"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_unknown.html#a0125f41c89763dea06619b5fd5246b4c"> 1118</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html#a0125f41c89763dea06619b5fd5246b4c">ShallowClone</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* document ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01119"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_unknown.html#a0715ab2c05d7f74845c188122213b116"> 1119</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html#a0715ab2c05d7f74845c188122213b116">ShallowEqual</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* compare ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01120"></a><span class="lineno"> 1120</span>&#160; </div>
<div class="line"><a name="l01121"></a><span class="lineno"> 1121</span>&#160;<span class="keyword">protected</span>:</div>
<div class="line"><a name="l01122"></a><span class="lineno"> 1122</span>&#160;    <span class="keyword">explicit</span> <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* doc );</div>
<div class="line"><a name="l01123"></a><span class="lineno"> 1123</span>&#160;    <span class="keyword">virtual</span> ~<a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>();</div>
<div class="line"><a name="l01124"></a><span class="lineno"> 1124</span>&#160; </div>
<div class="line"><a name="l01125"></a><span class="lineno"> 1125</span>&#160;    <span class="keywordtype">char</span>* ParseDeep( <span class="keywordtype">char</span>* p, StrPair* parentEndTag, <span class="keywordtype">int</span>* curLineNumPtr );</div>
<div class="line"><a name="l01126"></a><span class="lineno"> 1126</span>&#160; </div>
<div class="line"><a name="l01127"></a><span class="lineno"> 1127</span>&#160;<span class="keyword">private</span>:</div>
<div class="line"><a name="l01128"></a><span class="lineno"> 1128</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>&amp; );    <span class="comment">// not supported</span></div>
<div class="line"><a name="l01129"></a><span class="lineno"> 1129</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>&amp; operator=( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>&amp; ); <span class="comment">// not supported</span></div>
<div class="line"><a name="l01130"></a><span class="lineno"> 1130</span>&#160;};</div>
<div class="line"><a name="l01131"></a><span class="lineno"> 1131</span>&#160; </div>
<div class="line"><a name="l01132"></a><span class="lineno"> 1132</span>&#160; </div>
<div class="line"><a name="l01133"></a><span class="lineno"> 1133</span>&#160; </div>
<div class="line"><a name="l01140"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html"> 1140</a></span>&#160;<span class="keyword">class </span>TINYXML2_LIB <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a></div>
<div class="line"><a name="l01141"></a><span class="lineno"> 1141</span>&#160;{</div>
<div class="line"><a name="l01142"></a><span class="lineno"> 1142</span>&#160;    <span class="keyword">friend</span> <span class="keyword">class </span><a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>;</div>
<div class="line"><a name="l01143"></a><span class="lineno"> 1143</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l01145"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#ab886c486ec19f02ed826f8dc129e5ad8"> 1145</a></span>&#160;    <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#ab886c486ec19f02ed826f8dc129e5ad8">Name</a>() <span class="keyword">const</span>;</div>
<div class="line"><a name="l01146"></a><span class="lineno"> 1146</span>&#160; </div>
<div class="line"><a name="l01148"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#a1aab1dd0e43ecbcfa306adbcf3a3d853"> 1148</a></span>&#160;    <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a1aab1dd0e43ecbcfa306adbcf3a3d853">Value</a>() <span class="keyword">const</span>;</div>
<div class="line"><a name="l01149"></a><span class="lineno"> 1149</span>&#160; </div>
<div class="line"><a name="l01151"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#a02d5ea924586e35f9c13857d1671b765"> 1151</a></span>&#160;    <span class="keywordtype">int</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a02d5ea924586e35f9c13857d1671b765">GetLineNum</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> _parseLineNum; }</div>
<div class="line"><a name="l01152"></a><span class="lineno"> 1152</span>&#160; </div>
<div class="line"><a name="l01154"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#aee53571b21e7ce5421eb929523a8bbe6"> 1154</a></span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#aee53571b21e7ce5421eb929523a8bbe6">Next</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01155"></a><span class="lineno"> 1155</span>&#160;        <span class="keywordflow">return</span> _next;</div>
<div class="line"><a name="l01156"></a><span class="lineno"> 1156</span>&#160;    }</div>
<div class="line"><a name="l01157"></a><span class="lineno"> 1157</span>&#160; </div>
<div class="line"><a name="l01162"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#adfa2433f0fdafd5c3880936de9affa80"> 1162</a></span>&#160;    <span class="keywordtype">int</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#adfa2433f0fdafd5c3880936de9affa80">IntValue</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01163"></a><span class="lineno"> 1163</span>&#160;        <span class="keywordtype">int</span> i = 0;</div>
<div class="line"><a name="l01164"></a><span class="lineno"> 1164</span>&#160;        QueryIntValue(&amp;i);</div>
<div class="line"><a name="l01165"></a><span class="lineno"> 1165</span>&#160;        <span class="keywordflow">return</span> i;</div>
<div class="line"><a name="l01166"></a><span class="lineno"> 1166</span>&#160;    }</div>
<div class="line"><a name="l01167"></a><span class="lineno"> 1167</span>&#160; </div>
<div class="line"><a name="l01168"></a><span class="lineno"> 1168</span>&#160;    int64_t Int64Value()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01169"></a><span class="lineno"> 1169</span>&#160;        int64_t i = 0;</div>
<div class="line"><a name="l01170"></a><span class="lineno"> 1170</span>&#160;        QueryInt64Value(&amp;i);</div>
<div class="line"><a name="l01171"></a><span class="lineno"> 1171</span>&#160;        <span class="keywordflow">return</span> i;</div>
<div class="line"><a name="l01172"></a><span class="lineno"> 1172</span>&#160;    }</div>
<div class="line"><a name="l01173"></a><span class="lineno"> 1173</span>&#160; </div>
<div class="line"><a name="l01174"></a><span class="lineno"> 1174</span>&#160;    uint64_t Unsigned64Value()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01175"></a><span class="lineno"> 1175</span>&#160;        uint64_t i = 0;</div>
<div class="line"><a name="l01176"></a><span class="lineno"> 1176</span>&#160;        QueryUnsigned64Value(&amp;i);</div>
<div class="line"><a name="l01177"></a><span class="lineno"> 1177</span>&#160;        <span class="keywordflow">return</span> i;</div>
<div class="line"><a name="l01178"></a><span class="lineno"> 1178</span>&#160;    }</div>
<div class="line"><a name="l01179"></a><span class="lineno"> 1179</span>&#160; </div>
<div class="line"><a name="l01181"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#a0be5343b08a957c42c02c5d32c35d338"> 1181</a></span>&#160;    <span class="keywordtype">unsigned</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a0be5343b08a957c42c02c5d32c35d338">UnsignedValue</a>()<span class="keyword"> const          </span>{</div>
<div class="line"><a name="l01182"></a><span class="lineno"> 1182</span>&#160;        <span class="keywordtype">unsigned</span> i=0;</div>
<div class="line"><a name="l01183"></a><span class="lineno"> 1183</span>&#160;        QueryUnsignedValue( &amp;i );</div>
<div class="line"><a name="l01184"></a><span class="lineno"> 1184</span>&#160;        <span class="keywordflow">return</span> i;</div>
<div class="line"><a name="l01185"></a><span class="lineno"> 1185</span>&#160;    }</div>
<div class="line"><a name="l01187"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#a98ce5207344ad33a265b0422addae1ff"> 1187</a></span>&#160;    <span class="keywordtype">bool</span>     <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a98ce5207344ad33a265b0422addae1ff">BoolValue</a>()<span class="keyword"> const              </span>{</div>
<div class="line"><a name="l01188"></a><span class="lineno"> 1188</span>&#160;        <span class="keywordtype">bool</span> b=<span class="keyword">false</span>;</div>
<div class="line"><a name="l01189"></a><span class="lineno"> 1189</span>&#160;        QueryBoolValue( &amp;b );</div>
<div class="line"><a name="l01190"></a><span class="lineno"> 1190</span>&#160;        <span class="keywordflow">return</span> b;</div>
<div class="line"><a name="l01191"></a><span class="lineno"> 1191</span>&#160;    }</div>
<div class="line"><a name="l01193"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#a4aa73513f54ff0087d3e804f0f54e30f"> 1193</a></span>&#160;    <span class="keywordtype">double</span>   <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a4aa73513f54ff0087d3e804f0f54e30f">DoubleValue</a>()<span class="keyword"> const            </span>{</div>
<div class="line"><a name="l01194"></a><span class="lineno"> 1194</span>&#160;        <span class="keywordtype">double</span> d=0;</div>
<div class="line"><a name="l01195"></a><span class="lineno"> 1195</span>&#160;        QueryDoubleValue( &amp;d );</div>
<div class="line"><a name="l01196"></a><span class="lineno"> 1196</span>&#160;        <span class="keywordflow">return</span> d;</div>
<div class="line"><a name="l01197"></a><span class="lineno"> 1197</span>&#160;    }</div>
<div class="line"><a name="l01199"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#a27797b45d21c981257720db94f5f8801"> 1199</a></span>&#160;    <span class="keywordtype">float</span>    <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a27797b45d21c981257720db94f5f8801">FloatValue</a>()<span class="keyword"> const             </span>{</div>
<div class="line"><a name="l01200"></a><span class="lineno"> 1200</span>&#160;        <span class="keywordtype">float</span> f=0;</div>
<div class="line"><a name="l01201"></a><span class="lineno"> 1201</span>&#160;        QueryFloatValue( &amp;f );</div>
<div class="line"><a name="l01202"></a><span class="lineno"> 1202</span>&#160;        <span class="keywordflow">return</span> f;</div>
<div class="line"><a name="l01203"></a><span class="lineno"> 1203</span>&#160;    }</div>
<div class="line"><a name="l01204"></a><span class="lineno"> 1204</span>&#160; </div>
<div class="line"><a name="l01209"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#a6d5176260db00ea301c01af8457cd993"> 1209</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a6d5176260db00ea301c01af8457cd993">QueryIntValue</a>( <span class="keywordtype">int</span>* value ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01211"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#a48a7f3496f1415832e451bd8d09c9cb9"> 1211</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a48a7f3496f1415832e451bd8d09c9cb9">QueryUnsignedValue</a>( <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span>* value ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01213"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#a4e25344d6e4159026be34dbddf1dcac2"> 1213</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a4e25344d6e4159026be34dbddf1dcac2">QueryInt64Value</a>(int64_t* value) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01215"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#af793c695e7ee65cf20b8010d38b1d157"> 1215</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#af793c695e7ee65cf20b8010d38b1d157">QueryUnsigned64Value</a>(uint64_t* value) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01217"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#a5f32e038954256f61c21ff20fd13a09c"> 1217</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a5f32e038954256f61c21ff20fd13a09c">QueryBoolValue</a>( <span class="keywordtype">bool</span>* value ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01219"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#a2aa6e55e8ea03af0609cf6690bff79b9"> 1219</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a2aa6e55e8ea03af0609cf6690bff79b9">QueryDoubleValue</a>( <span class="keywordtype">double</span>* value ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01221"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#a049dea6449a6259b6cfed44a9427b607"> 1221</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a049dea6449a6259b6cfed44a9427b607">QueryFloatValue</a>( <span class="keywordtype">float</span>* value ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01222"></a><span class="lineno"> 1222</span>&#160; </div>
<div class="line"><a name="l01224"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#a406d2c4a13c7af99a65edb59dd9f7581"> 1224</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a406d2c4a13c7af99a65edb59dd9f7581">SetAttribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* value );</div>
<div class="line"><a name="l01226"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#ad86d7d7058d76761c3a80662566a57e5"> 1226</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#ad86d7d7058d76761c3a80662566a57e5">SetAttribute</a>( <span class="keywordtype">int</span> value );</div>
<div class="line"><a name="l01228"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#ae70468c0f6df2748ba3529c716999fae"> 1228</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#ae70468c0f6df2748ba3529c716999fae">SetAttribute</a>( <span class="keywordtype">unsigned</span> value );</div>
<div class="line"><a name="l01230"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#a7c1240f479722b9aa29b6c030aa116c2"> 1230</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a7c1240f479722b9aa29b6c030aa116c2">SetAttribute</a>(int64_t value);</div>
<div class="line"><a name="l01232"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#a10964060a5c0d92486ecf8705bdf37da"> 1232</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a10964060a5c0d92486ecf8705bdf37da">SetAttribute</a>(uint64_t value);</div>
<div class="line"><a name="l01234"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#ab3516def4fe058fe328f2b89fc2d77da"> 1234</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#ab3516def4fe058fe328f2b89fc2d77da">SetAttribute</a>( <span class="keywordtype">bool</span> value );</div>
<div class="line"><a name="l01236"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#a9a65ab3147abe8ccbbd373ce8791e818"> 1236</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a9a65ab3147abe8ccbbd373ce8791e818">SetAttribute</a>( <span class="keywordtype">double</span> value );</div>
<div class="line"><a name="l01238"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_attribute.html#ae95e843313aaf5d56c32530b6456df02"> 1238</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#ae95e843313aaf5d56c32530b6456df02">SetAttribute</a>( <span class="keywordtype">float</span> value );</div>
<div class="line"><a name="l01239"></a><span class="lineno"> 1239</span>&#160; </div>
<div class="line"><a name="l01240"></a><span class="lineno"> 1240</span>&#160;<span class="keyword">private</span>:</div>
<div class="line"><a name="l01241"></a><span class="lineno"> 1241</span>&#160;    <span class="keyword">enum</span> { BUF_SIZE = 200 };</div>
<div class="line"><a name="l01242"></a><span class="lineno"> 1242</span>&#160; </div>
<div class="line"><a name="l01243"></a><span class="lineno"> 1243</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>() : _name(), _value(),_parseLineNum( 0 ), _next( 0 ), _memPool( 0 ) {}</div>
<div class="line"><a name="l01244"></a><span class="lineno"> 1244</span>&#160;    <span class="keyword">virtual</span> ~XMLAttribute() {}</div>
<div class="line"><a name="l01245"></a><span class="lineno"> 1245</span>&#160; </div>
<div class="line"><a name="l01246"></a><span class="lineno"> 1246</span>&#160;    XMLAttribute( <span class="keyword">const</span> XMLAttribute&amp; );    <span class="comment">// not supported</span></div>
<div class="line"><a name="l01247"></a><span class="lineno"> 1247</span>&#160;    <span class="keywordtype">void</span> operator=( <span class="keyword">const</span> XMLAttribute&amp; );  <span class="comment">// not supported</span></div>
<div class="line"><a name="l01248"></a><span class="lineno"> 1248</span>&#160;    <span class="keywordtype">void</span> SetName( <span class="keyword">const</span> <span class="keywordtype">char</span>* name );</div>
<div class="line"><a name="l01249"></a><span class="lineno"> 1249</span>&#160; </div>
<div class="line"><a name="l01250"></a><span class="lineno"> 1250</span>&#160;    <span class="keywordtype">char</span>* ParseDeep( <span class="keywordtype">char</span>* p, <span class="keywordtype">bool</span> processEntities, <span class="keywordtype">int</span>* curLineNumPtr );</div>
<div class="line"><a name="l01251"></a><span class="lineno"> 1251</span>&#160; </div>
<div class="line"><a name="l01252"></a><span class="lineno"> 1252</span>&#160;    <span class="keyword">mutable</span> StrPair _name;</div>
<div class="line"><a name="l01253"></a><span class="lineno"> 1253</span>&#160;    <span class="keyword">mutable</span> StrPair _value;</div>
<div class="line"><a name="l01254"></a><span class="lineno"> 1254</span>&#160;    <span class="keywordtype">int</span>             _parseLineNum;</div>
<div class="line"><a name="l01255"></a><span class="lineno"> 1255</span>&#160;    XMLAttribute*   _next;</div>
<div class="line"><a name="l01256"></a><span class="lineno"> 1256</span>&#160;    MemPool*        _memPool;</div>
<div class="line"><a name="l01257"></a><span class="lineno"> 1257</span>&#160;};</div>
<div class="line"><a name="l01258"></a><span class="lineno"> 1258</span>&#160; </div>
<div class="line"><a name="l01259"></a><span class="lineno"> 1259</span>&#160; </div>
<div class="line"><a name="l01264"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html"> 1264</a></span>&#160;<span class="keyword">class </span>TINYXML2_LIB <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> : <span class="keyword">public</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a></div>
<div class="line"><a name="l01265"></a><span class="lineno"> 1265</span>&#160;{</div>
<div class="line"><a name="l01266"></a><span class="lineno"> 1266</span>&#160;    <span class="keyword">friend</span> <span class="keyword">class </span><a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>;</div>
<div class="line"><a name="l01267"></a><span class="lineno"> 1267</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l01269"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a63e057fb5baee1dd29f323cb85907b35"> 1269</a></span>&#160;    <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a63e057fb5baee1dd29f323cb85907b35">Name</a>()<span class="keyword"> const        </span>{</div>
<div class="line"><a name="l01270"></a><span class="lineno"> 1270</span>&#160;        <span class="keywordflow">return</span> Value();</div>
<div class="line"><a name="l01271"></a><span class="lineno"> 1271</span>&#160;    }</div>
<div class="line"><a name="l01273"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a97712009a530d8cb8a63bf705f02b4f1"> 1273</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a97712009a530d8cb8a63bf705f02b4f1">SetName</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* str, <span class="keywordtype">bool</span> staticMem=<span class="keyword">false</span> )   {</div>
<div class="line"><a name="l01274"></a><span class="lineno"> 1274</span>&#160;        SetValue( str, staticMem );</div>
<div class="line"><a name="l01275"></a><span class="lineno"> 1275</span>&#160;    }</div>
<div class="line"><a name="l01276"></a><span class="lineno"> 1276</span>&#160; </div>
<div class="line"><a name="l01277"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#ad9ff5c2dbc15df36cf664ce1b0ea0a5d"> 1277</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#ad9ff5c2dbc15df36cf664ce1b0ea0a5d">ToElement</a>()             {</div>
<div class="line"><a name="l01278"></a><span class="lineno"> 1278</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">this</span>;</div>
<div class="line"><a name="l01279"></a><span class="lineno"> 1279</span>&#160;    }</div>
<div class="line"><a name="l01280"></a><span class="lineno"> 1280</span>&#160;    <span class="keyword">virtual</span> <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* ToElement()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01281"></a><span class="lineno"> 1281</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">this</span>;</div>
<div class="line"><a name="l01282"></a><span class="lineno"> 1282</span>&#160;    }</div>
<div class="line"><a name="l01283"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a3ea8a40e788fb9ad876c28a32932c6d5"> 1283</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a3ea8a40e788fb9ad876c28a32932c6d5">Accept</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a>* visitor ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01284"></a><span class="lineno"> 1284</span>&#160; </div>
<div class="line"><a name="l01308"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a70e49ed60b11212ae35f7e354cfe1de9"> 1308</a></span>&#160;    <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a70e49ed60b11212ae35f7e354cfe1de9">Attribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keyword">const</span> <span class="keywordtype">char</span>* value=0 ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01309"></a><span class="lineno"> 1309</span>&#160; </div>
<div class="line"><a name="l01316"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a95a89b13bb14a2d4655e2b5b406c00d4"> 1316</a></span>&#160;    <span class="keywordtype">int</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a95a89b13bb14a2d4655e2b5b406c00d4">IntAttribute</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">int</span> defaultValue = 0) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01318"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#afea43a1d4aa33e3703ddee5fc9adc26c"> 1318</a></span>&#160;    <span class="keywordtype">unsigned</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#afea43a1d4aa33e3703ddee5fc9adc26c">UnsignedAttribute</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">unsigned</span> defaultValue = 0) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01320"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a66d96972adecd816194191f13cc4a0a0"> 1320</a></span>&#160;    int64_t <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a66d96972adecd816194191f13cc4a0a0">Int64Attribute</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* name, int64_t defaultValue = 0) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01322"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a226502bab8f1be7ede1fdd255398eb85"> 1322</a></span>&#160;    uint64_t <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a226502bab8f1be7ede1fdd255398eb85">Unsigned64Attribute</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* name, uint64_t defaultValue = 0) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01324"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a53eda26131e1ad1031ef8ec8adb51bd8"> 1324</a></span>&#160;    <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a53eda26131e1ad1031ef8ec8adb51bd8">BoolAttribute</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">bool</span> defaultValue = <span class="keyword">false</span>) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01326"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a10a90c505aea716bf073eea1c97f33b5"> 1326</a></span>&#160;    <span class="keywordtype">double</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a10a90c505aea716bf073eea1c97f33b5">DoubleAttribute</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">double</span> defaultValue = 0) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01328"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#ab1f4be2332e27dc640e9b6abd01d64dd"> 1328</a></span>&#160;    <span class="keywordtype">float</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#ab1f4be2332e27dc640e9b6abd01d64dd">FloatAttribute</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">float</span> defaultValue = 0) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01329"></a><span class="lineno"> 1329</span>&#160; </div>
<div class="line"><a name="l01343"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a8a78bc1187c1c45ad89f2690eab567b1"> 1343</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a8a78bc1187c1c45ad89f2690eab567b1">QueryIntAttribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">int</span>* value )<span class="keyword"> const                </span>{</div>
<div class="line"><a name="l01344"></a><span class="lineno"> 1344</span>&#160;        <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* a = FindAttribute( name );</div>
<div class="line"><a name="l01345"></a><span class="lineno"> 1345</span>&#160;        <span class="keywordflow">if</span> ( !a ) {</div>
<div class="line"><a name="l01346"></a><span class="lineno"> 1346</span>&#160;            <span class="keywordflow">return</span> XML_NO_ATTRIBUTE;</div>
<div class="line"><a name="l01347"></a><span class="lineno"> 1347</span>&#160;        }</div>
<div class="line"><a name="l01348"></a><span class="lineno"> 1348</span>&#160;        <span class="keywordflow">return</span> a-&gt;<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a6d5176260db00ea301c01af8457cd993">QueryIntValue</a>( value );</div>
<div class="line"><a name="l01349"></a><span class="lineno"> 1349</span>&#160;    }</div>
<div class="line"><a name="l01350"></a><span class="lineno"> 1350</span>&#160; </div>
<div class="line"><a name="l01352"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a26fc84cbfba6769dafcfbf256c05e22f"> 1352</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a26fc84cbfba6769dafcfbf256c05e22f">QueryUnsignedAttribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span>* value )<span class="keyword"> const  </span>{</div>
<div class="line"><a name="l01353"></a><span class="lineno"> 1353</span>&#160;        <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* a = FindAttribute( name );</div>
<div class="line"><a name="l01354"></a><span class="lineno"> 1354</span>&#160;        <span class="keywordflow">if</span> ( !a ) {</div>
<div class="line"><a name="l01355"></a><span class="lineno"> 1355</span>&#160;            <span class="keywordflow">return</span> XML_NO_ATTRIBUTE;</div>
<div class="line"><a name="l01356"></a><span class="lineno"> 1356</span>&#160;        }</div>
<div class="line"><a name="l01357"></a><span class="lineno"> 1357</span>&#160;        <span class="keywordflow">return</span> a-&gt;<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a48a7f3496f1415832e451bd8d09c9cb9">QueryUnsignedValue</a>( value );</div>
<div class="line"><a name="l01358"></a><span class="lineno"> 1358</span>&#160;    }</div>
<div class="line"><a name="l01359"></a><span class="lineno"> 1359</span>&#160; </div>
<div class="line"><a name="l01361"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a7c0955d80b6f8d196744eacb0f6e90a8"> 1361</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a7c0955d80b6f8d196744eacb0f6e90a8">QueryInt64Attribute</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* name, int64_t* value)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01362"></a><span class="lineno"> 1362</span>&#160;        <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* a = FindAttribute(name);</div>
<div class="line"><a name="l01363"></a><span class="lineno"> 1363</span>&#160;        <span class="keywordflow">if</span> (!a) {</div>
<div class="line"><a name="l01364"></a><span class="lineno"> 1364</span>&#160;            <span class="keywordflow">return</span> XML_NO_ATTRIBUTE;</div>
<div class="line"><a name="l01365"></a><span class="lineno"> 1365</span>&#160;        }</div>
<div class="line"><a name="l01366"></a><span class="lineno"> 1366</span>&#160;        <span class="keywordflow">return</span> a-&gt;<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a4e25344d6e4159026be34dbddf1dcac2">QueryInt64Value</a>(value);</div>
<div class="line"><a name="l01367"></a><span class="lineno"> 1367</span>&#160;    }</div>
<div class="line"><a name="l01368"></a><span class="lineno"> 1368</span>&#160; </div>
<div class="line"><a name="l01370"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a13dd590b5d3958ce2ed79844aacd9405"> 1370</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a13dd590b5d3958ce2ed79844aacd9405">QueryUnsigned64Attribute</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* name, uint64_t* value)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01371"></a><span class="lineno"> 1371</span>&#160;        <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* a = FindAttribute(name);</div>
<div class="line"><a name="l01372"></a><span class="lineno"> 1372</span>&#160;        <span class="keywordflow">if</span>(!a) {</div>
<div class="line"><a name="l01373"></a><span class="lineno"> 1373</span>&#160;            <span class="keywordflow">return</span> XML_NO_ATTRIBUTE;</div>
<div class="line"><a name="l01374"></a><span class="lineno"> 1374</span>&#160;        }</div>
<div class="line"><a name="l01375"></a><span class="lineno"> 1375</span>&#160;        <span class="keywordflow">return</span> a-&gt;<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#af793c695e7ee65cf20b8010d38b1d157">QueryUnsigned64Value</a>(value);</div>
<div class="line"><a name="l01376"></a><span class="lineno"> 1376</span>&#160;    }</div>
<div class="line"><a name="l01377"></a><span class="lineno"> 1377</span>&#160; </div>
<div class="line"><a name="l01379"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a14c1bb77c39689838be01838d86ca872"> 1379</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a14c1bb77c39689838be01838d86ca872">QueryBoolAttribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">bool</span>* value )<span class="keyword"> const              </span>{</div>
<div class="line"><a name="l01380"></a><span class="lineno"> 1380</span>&#160;        <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* a = FindAttribute( name );</div>
<div class="line"><a name="l01381"></a><span class="lineno"> 1381</span>&#160;        <span class="keywordflow">if</span> ( !a ) {</div>
<div class="line"><a name="l01382"></a><span class="lineno"> 1382</span>&#160;            <span class="keywordflow">return</span> XML_NO_ATTRIBUTE;</div>
<div class="line"><a name="l01383"></a><span class="lineno"> 1383</span>&#160;        }</div>
<div class="line"><a name="l01384"></a><span class="lineno"> 1384</span>&#160;        <span class="keywordflow">return</span> a-&gt;<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a5f32e038954256f61c21ff20fd13a09c">QueryBoolValue</a>( value );</div>
<div class="line"><a name="l01385"></a><span class="lineno"> 1385</span>&#160;    }</div>
<div class="line"><a name="l01387"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a5f0964e2dbd8e2ee7fce9beab689443c"> 1387</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a5f0964e2dbd8e2ee7fce9beab689443c">QueryDoubleAttribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">double</span>* value )<span class="keyword"> const          </span>{</div>
<div class="line"><a name="l01388"></a><span class="lineno"> 1388</span>&#160;        <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* a = FindAttribute( name );</div>
<div class="line"><a name="l01389"></a><span class="lineno"> 1389</span>&#160;        <span class="keywordflow">if</span> ( !a ) {</div>
<div class="line"><a name="l01390"></a><span class="lineno"> 1390</span>&#160;            <span class="keywordflow">return</span> XML_NO_ATTRIBUTE;</div>
<div class="line"><a name="l01391"></a><span class="lineno"> 1391</span>&#160;        }</div>
<div class="line"><a name="l01392"></a><span class="lineno"> 1392</span>&#160;        <span class="keywordflow">return</span> a-&gt;<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a2aa6e55e8ea03af0609cf6690bff79b9">QueryDoubleValue</a>( value );</div>
<div class="line"><a name="l01393"></a><span class="lineno"> 1393</span>&#160;    }</div>
<div class="line"><a name="l01395"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#acd5eeddf6002ef90806af794b9d9a5a5"> 1395</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#acd5eeddf6002ef90806af794b9d9a5a5">QueryFloatAttribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">float</span>* value )<span class="keyword"> const            </span>{</div>
<div class="line"><a name="l01396"></a><span class="lineno"> 1396</span>&#160;        <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* a = FindAttribute( name );</div>
<div class="line"><a name="l01397"></a><span class="lineno"> 1397</span>&#160;        <span class="keywordflow">if</span> ( !a ) {</div>
<div class="line"><a name="l01398"></a><span class="lineno"> 1398</span>&#160;            <span class="keywordflow">return</span> XML_NO_ATTRIBUTE;</div>
<div class="line"><a name="l01399"></a><span class="lineno"> 1399</span>&#160;        }</div>
<div class="line"><a name="l01400"></a><span class="lineno"> 1400</span>&#160;        <span class="keywordflow">return</span> a-&gt;<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a049dea6449a6259b6cfed44a9427b607">QueryFloatValue</a>( value );</div>
<div class="line"><a name="l01401"></a><span class="lineno"> 1401</span>&#160;    }</div>
<div class="line"><a name="l01402"></a><span class="lineno"> 1402</span>&#160; </div>
<div class="line"><a name="l01404"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#adb8ae765f98d0c5037faec48deea78bc"> 1404</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#adb8ae765f98d0c5037faec48deea78bc">QueryStringAttribute</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keyword">const</span> <span class="keywordtype">char</span>** value)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01405"></a><span class="lineno"> 1405</span>&#160;        <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* a = FindAttribute(name);</div>
<div class="line"><a name="l01406"></a><span class="lineno"> 1406</span>&#160;        <span class="keywordflow">if</span> (!a) {</div>
<div class="line"><a name="l01407"></a><span class="lineno"> 1407</span>&#160;            <span class="keywordflow">return</span> XML_NO_ATTRIBUTE;</div>
<div class="line"><a name="l01408"></a><span class="lineno"> 1408</span>&#160;        }</div>
<div class="line"><a name="l01409"></a><span class="lineno"> 1409</span>&#160;        *value = a-&gt;<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a1aab1dd0e43ecbcfa306adbcf3a3d853">Value</a>();</div>
<div class="line"><a name="l01410"></a><span class="lineno"> 1410</span>&#160;        <span class="keywordflow">return</span> XML_SUCCESS;</div>
<div class="line"><a name="l01411"></a><span class="lineno"> 1411</span>&#160;    }</div>
<div class="line"><a name="l01412"></a><span class="lineno"> 1412</span>&#160; </div>
<div class="line"><a name="l01413"></a><span class="lineno"> 1413</span>&#160; </div>
<div class="line"><a name="l01414"></a><span class="lineno"> 1414</span>&#160; </div>
<div class="line"><a name="l01432"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a5b7df3bed2b8954eabf227fa204522eb"> 1432</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a5b7df3bed2b8954eabf227fa204522eb">QueryAttribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">int</span>* value )<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01433"></a><span class="lineno"> 1433</span>&#160;        <span class="keywordflow">return</span> QueryIntAttribute( name, value );</div>
<div class="line"><a name="l01434"></a><span class="lineno"> 1434</span>&#160;    }</div>
<div class="line"><a name="l01435"></a><span class="lineno"> 1435</span>&#160; </div>
<div class="line"><a name="l01436"></a><span class="lineno"> 1436</span>&#160;    XMLError QueryAttribute( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span>* value )<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01437"></a><span class="lineno"> 1437</span>&#160;        <span class="keywordflow">return</span> QueryUnsignedAttribute( name, value );</div>
<div class="line"><a name="l01438"></a><span class="lineno"> 1438</span>&#160;    }</div>
<div class="line"><a name="l01439"></a><span class="lineno"> 1439</span>&#160; </div>
<div class="line"><a name="l01440"></a><span class="lineno"> 1440</span>&#160;    XMLError QueryAttribute(<span class="keyword">const</span> <span class="keywordtype">char</span>* name, int64_t* value)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01441"></a><span class="lineno"> 1441</span>&#160;        <span class="keywordflow">return</span> QueryInt64Attribute(name, value);</div>
<div class="line"><a name="l01442"></a><span class="lineno"> 1442</span>&#160;    }</div>
<div class="line"><a name="l01443"></a><span class="lineno"> 1443</span>&#160; </div>
<div class="line"><a name="l01444"></a><span class="lineno"> 1444</span>&#160;    XMLError QueryAttribute(<span class="keyword">const</span> <span class="keywordtype">char</span>* name, uint64_t* value)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01445"></a><span class="lineno"> 1445</span>&#160;        <span class="keywordflow">return</span> QueryUnsigned64Attribute(name, value);</div>
<div class="line"><a name="l01446"></a><span class="lineno"> 1446</span>&#160;    }</div>
<div class="line"><a name="l01447"></a><span class="lineno"> 1447</span>&#160; </div>
<div class="line"><a name="l01448"></a><span class="lineno"> 1448</span>&#160;    XMLError QueryAttribute( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">bool</span>* value )<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01449"></a><span class="lineno"> 1449</span>&#160;        <span class="keywordflow">return</span> QueryBoolAttribute( name, value );</div>
<div class="line"><a name="l01450"></a><span class="lineno"> 1450</span>&#160;    }</div>
<div class="line"><a name="l01451"></a><span class="lineno"> 1451</span>&#160; </div>
<div class="line"><a name="l01452"></a><span class="lineno"> 1452</span>&#160;    XMLError QueryAttribute( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">double</span>* value )<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01453"></a><span class="lineno"> 1453</span>&#160;        <span class="keywordflow">return</span> QueryDoubleAttribute( name, value );</div>
<div class="line"><a name="l01454"></a><span class="lineno"> 1454</span>&#160;    }</div>
<div class="line"><a name="l01455"></a><span class="lineno"> 1455</span>&#160; </div>
<div class="line"><a name="l01456"></a><span class="lineno"> 1456</span>&#160;    XMLError QueryAttribute( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">float</span>* value )<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01457"></a><span class="lineno"> 1457</span>&#160;        <span class="keywordflow">return</span> QueryFloatAttribute( name, value );</div>
<div class="line"><a name="l01458"></a><span class="lineno"> 1458</span>&#160;    }</div>
<div class="line"><a name="l01459"></a><span class="lineno"> 1459</span>&#160; </div>
<div class="line"><a name="l01460"></a><span class="lineno"> 1460</span>&#160;    XMLError QueryAttribute(<span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keyword">const</span> <span class="keywordtype">char</span>** value)<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01461"></a><span class="lineno"> 1461</span>&#160;        <span class="keywordflow">return</span> QueryStringAttribute(name, value);</div>
<div class="line"><a name="l01462"></a><span class="lineno"> 1462</span>&#160;    }</div>
<div class="line"><a name="l01463"></a><span class="lineno"> 1463</span>&#160; </div>
<div class="line"><a name="l01465"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a11943abf2d0831548c3790dd5d9f119c"> 1465</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a11943abf2d0831548c3790dd5d9f119c">SetAttribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keyword">const</span> <span class="keywordtype">char</span>* value )    {</div>
<div class="line"><a name="l01466"></a><span class="lineno"> 1466</span>&#160;        <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* a = FindOrCreateAttribute( name );</div>
<div class="line"><a name="l01467"></a><span class="lineno"> 1467</span>&#160;        a-&gt;<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a406d2c4a13c7af99a65edb59dd9f7581">SetAttribute</a>( value );</div>
<div class="line"><a name="l01468"></a><span class="lineno"> 1468</span>&#160;    }</div>
<div class="line"><a name="l01470"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#aae6568c64c7f1cc88be8461ba41a79cf"> 1470</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#aae6568c64c7f1cc88be8461ba41a79cf">SetAttribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">int</span> value )            {</div>
<div class="line"><a name="l01471"></a><span class="lineno"> 1471</span>&#160;        <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* a = FindOrCreateAttribute( name );</div>
<div class="line"><a name="l01472"></a><span class="lineno"> 1472</span>&#160;        a-&gt;<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a406d2c4a13c7af99a65edb59dd9f7581">SetAttribute</a>( value );</div>
<div class="line"><a name="l01473"></a><span class="lineno"> 1473</span>&#160;    }</div>
<div class="line"><a name="l01475"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#ae143997e90064ba82326b29a9930ea8f"> 1475</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#ae143997e90064ba82326b29a9930ea8f">SetAttribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">unsigned</span> value )       {</div>
<div class="line"><a name="l01476"></a><span class="lineno"> 1476</span>&#160;        <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* a = FindOrCreateAttribute( name );</div>
<div class="line"><a name="l01477"></a><span class="lineno"> 1477</span>&#160;        a-&gt;<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a406d2c4a13c7af99a65edb59dd9f7581">SetAttribute</a>( value );</div>
<div class="line"><a name="l01478"></a><span class="lineno"> 1478</span>&#160;    }</div>
<div class="line"><a name="l01479"></a><span class="lineno"> 1479</span>&#160; </div>
<div class="line"><a name="l01481"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#aaeefdf9171fec91b13a776b42299b0dd"> 1481</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#aaeefdf9171fec91b13a776b42299b0dd">SetAttribute</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* name, int64_t value) {</div>
<div class="line"><a name="l01482"></a><span class="lineno"> 1482</span>&#160;        <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* a = FindOrCreateAttribute(name);</div>
<div class="line"><a name="l01483"></a><span class="lineno"> 1483</span>&#160;        a-&gt;<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a406d2c4a13c7af99a65edb59dd9f7581">SetAttribute</a>(value);</div>
<div class="line"><a name="l01484"></a><span class="lineno"> 1484</span>&#160;    }</div>
<div class="line"><a name="l01485"></a><span class="lineno"> 1485</span>&#160; </div>
<div class="line"><a name="l01487"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#ad598868c0599ddc4695dab18552c308d"> 1487</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#ad598868c0599ddc4695dab18552c308d">SetAttribute</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* name, uint64_t value) {</div>
<div class="line"><a name="l01488"></a><span class="lineno"> 1488</span>&#160;        <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* a = FindOrCreateAttribute(name);</div>
<div class="line"><a name="l01489"></a><span class="lineno"> 1489</span>&#160;        a-&gt;<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a406d2c4a13c7af99a65edb59dd9f7581">SetAttribute</a>(value);</div>
<div class="line"><a name="l01490"></a><span class="lineno"> 1490</span>&#160;    }</div>
<div class="line"><a name="l01491"></a><span class="lineno"> 1491</span>&#160; </div>
<div class="line"><a name="l01493"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#aa848b696e6a75e4e545c6da9893b11e1"> 1493</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#aa848b696e6a75e4e545c6da9893b11e1">SetAttribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">bool</span> value )           {</div>
<div class="line"><a name="l01494"></a><span class="lineno"> 1494</span>&#160;        <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* a = FindOrCreateAttribute( name );</div>
<div class="line"><a name="l01495"></a><span class="lineno"> 1495</span>&#160;        a-&gt;<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a406d2c4a13c7af99a65edb59dd9f7581">SetAttribute</a>( value );</div>
<div class="line"><a name="l01496"></a><span class="lineno"> 1496</span>&#160;    }</div>
<div class="line"><a name="l01498"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a233397ee81e70eb5d4b814c5f8698533"> 1498</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a233397ee81e70eb5d4b814c5f8698533">SetAttribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">double</span> value )     {</div>
<div class="line"><a name="l01499"></a><span class="lineno"> 1499</span>&#160;        <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* a = FindOrCreateAttribute( name );</div>
<div class="line"><a name="l01500"></a><span class="lineno"> 1500</span>&#160;        a-&gt;<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a406d2c4a13c7af99a65edb59dd9f7581">SetAttribute</a>( value );</div>
<div class="line"><a name="l01501"></a><span class="lineno"> 1501</span>&#160;    }</div>
<div class="line"><a name="l01503"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a554b70d882e65b28fc084b23df9b9759"> 1503</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a554b70d882e65b28fc084b23df9b9759">SetAttribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">float</span> value )      {</div>
<div class="line"><a name="l01504"></a><span class="lineno"> 1504</span>&#160;        <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* a = FindOrCreateAttribute( name );</div>
<div class="line"><a name="l01505"></a><span class="lineno"> 1505</span>&#160;        a-&gt;<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html#a406d2c4a13c7af99a65edb59dd9f7581">SetAttribute</a>( value );</div>
<div class="line"><a name="l01506"></a><span class="lineno"> 1506</span>&#160;    }</div>
<div class="line"><a name="l01507"></a><span class="lineno"> 1507</span>&#160; </div>
<div class="line"><a name="l01511"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#aebd45aa7118964c30b32fe12e944628a"> 1511</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#aebd45aa7118964c30b32fe12e944628a">DeleteAttribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name );</div>
<div class="line"><a name="l01512"></a><span class="lineno"> 1512</span>&#160; </div>
<div class="line"><a name="l01514"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a3e191704c8d499906ec11fe2f60c6686"> 1514</a></span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a3e191704c8d499906ec11fe2f60c6686">FirstAttribute</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01515"></a><span class="lineno"> 1515</span>&#160;        <span class="keywordflow">return</span> _rootAttribute;</div>
<div class="line"><a name="l01516"></a><span class="lineno"> 1516</span>&#160;    }</div>
<div class="line"><a name="l01518"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a2dcd4d5d6fb63396cd2f257c318b42c4"> 1518</a></span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a2dcd4d5d6fb63396cd2f257c318b42c4">FindAttribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01519"></a><span class="lineno"> 1519</span>&#160; </div>
<div class="line"><a name="l01548"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a6d5c8d115561ade4e4456b71d91b6f51"> 1548</a></span>&#160;    <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a6d5c8d115561ade4e4456b71d91b6f51">GetText</a>() <span class="keyword">const</span>;</div>
<div class="line"><a name="l01549"></a><span class="lineno"> 1549</span>&#160; </div>
<div class="line"><a name="l01584"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a1f9c2cd61b72af5ae708d37b7ad283ce"> 1584</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a1f9c2cd61b72af5ae708d37b7ad283ce">SetText</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* inText );</div>
<div class="line"><a name="l01586"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#aeae8917b5ea6060b3c08d4e3d8d632d7"> 1586</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#aeae8917b5ea6060b3c08d4e3d8d632d7">SetText</a>( <span class="keywordtype">int</span> value );</div>
<div class="line"><a name="l01588"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a7bbfcc11d516598bc924a8fba4d08597"> 1588</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a7bbfcc11d516598bc924a8fba4d08597">SetText</a>( <span class="keywordtype">unsigned</span> value );</div>
<div class="line"><a name="l01590"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a7b62cd33acdfeff7ea2b1b330d4368e4"> 1590</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a7b62cd33acdfeff7ea2b1b330d4368e4">SetText</a>(int64_t value);</div>
<div class="line"><a name="l01592"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a6e615bc745afd1ca8ded56d7aac02657"> 1592</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a6e615bc745afd1ca8ded56d7aac02657">SetText</a>(uint64_t value);</div>
<div class="line"><a name="l01594"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#ae4b543d6770de76fb6ab68e541c192a4"> 1594</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#ae4b543d6770de76fb6ab68e541c192a4">SetText</a>( <span class="keywordtype">bool</span> value );</div>
<div class="line"><a name="l01596"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a67bd77ac9aaeff58ff20b4275a65ba4e"> 1596</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a67bd77ac9aaeff58ff20b4275a65ba4e">SetText</a>( <span class="keywordtype">double</span> value );</div>
<div class="line"><a name="l01598"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a51d560da5ae3ad6b75e0ab9ffb2ae42a"> 1598</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a51d560da5ae3ad6b75e0ab9ffb2ae42a">SetText</a>( <span class="keywordtype">float</span> value );</div>
<div class="line"><a name="l01599"></a><span class="lineno"> 1599</span>&#160; </div>
<div class="line"><a name="l01626"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632"> 1626</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">QueryIntText</a>( <span class="keywordtype">int</span>* ival ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01628"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a14d38aa4b5e18a46274a27425188a6a1"> 1628</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a14d38aa4b5e18a46274a27425188a6a1">QueryUnsignedText</a>( <span class="keywordtype">unsigned</span>* uval ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01630"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a120c538c8eead169e635dbc70fb226d8"> 1630</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a120c538c8eead169e635dbc70fb226d8">QueryInt64Text</a>(int64_t* uval) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01632"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#ac2239b3bd172ad8f5b78d04d4236144b"> 1632</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#ac2239b3bd172ad8f5b78d04d4236144b">QueryUnsigned64Text</a>(uint64_t* uval) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01634"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a3fe5417d59eb8f5c4afe924b7d332736"> 1634</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a3fe5417d59eb8f5c4afe924b7d332736">QueryBoolText</a>( <span class="keywordtype">bool</span>* bval ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01636"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a684679c99bb036a25652744cec6c4d96"> 1636</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a684679c99bb036a25652744cec6c4d96">QueryDoubleText</a>( <span class="keywordtype">double</span>* dval ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01638"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#afa332afedd93210daa6d44b88eb11e29"> 1638</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#afa332afedd93210daa6d44b88eb11e29">QueryFloatText</a>( <span class="keywordtype">float</span>* fval ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01639"></a><span class="lineno"> 1639</span>&#160; </div>
<div class="line"><a name="l01640"></a><span class="lineno"> 1640</span>&#160;    <span class="keywordtype">int</span> IntText(<span class="keywordtype">int</span> defaultValue = 0) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01641"></a><span class="lineno"> 1641</span>&#160; </div>
<div class="line"><a name="l01643"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a49bad014ffcc17b0b6119d5b2c97dfb5"> 1643</a></span>&#160;    <span class="keywordtype">unsigned</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a49bad014ffcc17b0b6119d5b2c97dfb5">UnsignedText</a>(<span class="keywordtype">unsigned</span> defaultValue = 0) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01645"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#aab6151f7e3b4c2c0a8234e262d7b6b8a"> 1645</a></span>&#160;    int64_t <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#aab6151f7e3b4c2c0a8234e262d7b6b8a">Int64Text</a>(int64_t defaultValue = 0) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01647"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#af48c1023abbac1acdf4927c51c3a5f0c"> 1647</a></span>&#160;    uint64_t <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#af48c1023abbac1acdf4927c51c3a5f0c">Unsigned64Text</a>(uint64_t defaultValue = 0) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01649"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a68569f59f6382bcea7f5013ec59736d2"> 1649</a></span>&#160;    <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a68569f59f6382bcea7f5013ec59736d2">BoolText</a>(<span class="keywordtype">bool</span> defaultValue = <span class="keyword">false</span>) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01651"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a81b1ff0cf2f2cd09be8badc08b39a2b7"> 1651</a></span>&#160;    <span class="keywordtype">double</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a81b1ff0cf2f2cd09be8badc08b39a2b7">DoubleText</a>(<span class="keywordtype">double</span> defaultValue = 0) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01653"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a45444eb21f99ca46101545992dc2e927"> 1653</a></span>&#160;    <span class="keywordtype">float</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a45444eb21f99ca46101545992dc2e927">FloatText</a>(<span class="keywordtype">float</span> defaultValue = 0) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01654"></a><span class="lineno"> 1654</span>&#160; </div>
<div class="line"><a name="l01659"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#abc9506eff9780f666f49dc3d5e5cae13"> 1659</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#abc9506eff9780f666f49dc3d5e5cae13">InsertNewChildElement</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* name);</div>
<div class="line"><a name="l01661"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#ae4f2c2e781b8dc030411d84cd20fa46d"> 1661</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#ae4f2c2e781b8dc030411d84cd20fa46d">InsertNewComment</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* comment);</div>
<div class="line"><a name="l01663"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#a189e155810fc9fdd4da1409cbadee187"> 1663</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#a189e155810fc9fdd4da1409cbadee187">InsertNewText</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* text);</div>
<div class="line"><a name="l01665"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#adec237e788b50c4ed73c918a166adde6"> 1665</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#adec237e788b50c4ed73c918a166adde6">InsertNewDeclaration</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* text);</div>
<div class="line"><a name="l01667"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#acaa5fe3957760e68185006965e2c11c2"> 1667</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#acaa5fe3957760e68185006965e2c11c2">InsertNewUnknown</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* text);</div>
<div class="line"><a name="l01668"></a><span class="lineno"> 1668</span>&#160; </div>
<div class="line"><a name="l01669"></a><span class="lineno"> 1669</span>&#160; </div>
<div class="line"><a name="l01670"></a><span class="lineno"> 1670</span>&#160;    <span class="comment">// internal:</span></div>
<div class="line"><a name="l01671"></a><span class="lineno"> 1671</span>&#160;    <span class="keyword">enum</span> ElementClosingType {</div>
<div class="line"><a name="l01672"></a><span class="lineno"> 1672</span>&#160;        OPEN,       <span class="comment">// &lt;foo&gt;</span></div>
<div class="line"><a name="l01673"></a><span class="lineno"> 1673</span>&#160;        CLOSED,     <span class="comment">// &lt;foo/&gt;</span></div>
<div class="line"><a name="l01674"></a><span class="lineno"> 1674</span>&#160;        CLOSING     <span class="comment">// &lt;/foo&gt;</span></div>
<div class="line"><a name="l01675"></a><span class="lineno"> 1675</span>&#160;    };</div>
<div class="line"><a name="l01676"></a><span class="lineno"> 1676</span>&#160;    ElementClosingType ClosingType()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01677"></a><span class="lineno"> 1677</span>&#160;        <span class="keywordflow">return</span> _closingType;</div>
<div class="line"><a name="l01678"></a><span class="lineno"> 1678</span>&#160;    }</div>
<div class="line"><a name="l01679"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#ac035742d68b0c50c3f676374e59fe750"> 1679</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#ac035742d68b0c50c3f676374e59fe750">ShallowClone</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* document ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01680"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_element.html#ad9ea913a460b48979bd83cf9871c99f6"> 1680</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html#ad9ea913a460b48979bd83cf9871c99f6">ShallowEqual</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* compare ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01681"></a><span class="lineno"> 1681</span>&#160; </div>
<div class="line"><a name="l01682"></a><span class="lineno"> 1682</span>&#160;<span class="keyword">protected</span>:</div>
<div class="line"><a name="l01683"></a><span class="lineno"> 1683</span>&#160;    <span class="keywordtype">char</span>* ParseDeep( <span class="keywordtype">char</span>* p, StrPair* parentEndTag, <span class="keywordtype">int</span>* curLineNumPtr );</div>
<div class="line"><a name="l01684"></a><span class="lineno"> 1684</span>&#160; </div>
<div class="line"><a name="l01685"></a><span class="lineno"> 1685</span>&#160;<span class="keyword">private</span>:</div>
<div class="line"><a name="l01686"></a><span class="lineno"> 1686</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* doc );</div>
<div class="line"><a name="l01687"></a><span class="lineno"> 1687</span>&#160;    <span class="keyword">virtual</span> ~<a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>();</div>
<div class="line"><a name="l01688"></a><span class="lineno"> 1688</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>&amp; );    <span class="comment">// not supported</span></div>
<div class="line"><a name="l01689"></a><span class="lineno"> 1689</span>&#160;    <span class="keywordtype">void</span> operator=( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>&amp; );    <span class="comment">// not supported</span></div>
<div class="line"><a name="l01690"></a><span class="lineno"> 1690</span>&#160; </div>
<div class="line"><a name="l01691"></a><span class="lineno"> 1691</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* FindOrCreateAttribute( <span class="keyword">const</span> <span class="keywordtype">char</span>* name );</div>
<div class="line"><a name="l01692"></a><span class="lineno"> 1692</span>&#160;    <span class="keywordtype">char</span>* ParseAttributes( <span class="keywordtype">char</span>* p, <span class="keywordtype">int</span>* curLineNumPtr );</div>
<div class="line"><a name="l01693"></a><span class="lineno"> 1693</span>&#160;    <span class="keyword">static</span> <span class="keywordtype">void</span> DeleteAttribute( <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* attribute );</div>
<div class="line"><a name="l01694"></a><span class="lineno"> 1694</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* CreateAttribute();</div>
<div class="line"><a name="l01695"></a><span class="lineno"> 1695</span>&#160; </div>
<div class="line"><a name="l01696"></a><span class="lineno"> 1696</span>&#160;    <span class="keyword">enum</span> { BUF_SIZE = 200 };</div>
<div class="line"><a name="l01697"></a><span class="lineno"> 1697</span>&#160;    ElementClosingType _closingType;</div>
<div class="line"><a name="l01698"></a><span class="lineno"> 1698</span>&#160;    <span class="comment">// The attribute list is ordered; there is no &#39;lastAttribute&#39;</span></div>
<div class="line"><a name="l01699"></a><span class="lineno"> 1699</span>&#160;    <span class="comment">// because the list needs to be scanned for dupes before adding</span></div>
<div class="line"><a name="l01700"></a><span class="lineno"> 1700</span>&#160;    <span class="comment">// a new attribute.</span></div>
<div class="line"><a name="l01701"></a><span class="lineno"> 1701</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* _rootAttribute;</div>
<div class="line"><a name="l01702"></a><span class="lineno"> 1702</span>&#160;};</div>
<div class="line"><a name="l01703"></a><span class="lineno"> 1703</span>&#160; </div>
<div class="line"><a name="l01704"></a><span class="lineno"> 1704</span>&#160; </div>
<div class="line"><a name="l01705"></a><span class="lineno"> 1705</span>&#160;<span class="keyword">enum</span> Whitespace {</div>
<div class="line"><a name="l01706"></a><span class="lineno"> 1706</span>&#160;    PRESERVE_WHITESPACE,</div>
<div class="line"><a name="l01707"></a><span class="lineno"> 1707</span>&#160;    COLLAPSE_WHITESPACE</div>
<div class="line"><a name="l01708"></a><span class="lineno"> 1708</span>&#160;};</div>
<div class="line"><a name="l01709"></a><span class="lineno"> 1709</span>&#160; </div>
<div class="line"><a name="l01710"></a><span class="lineno"> 1710</span>&#160; </div>
<div class="line"><a name="l01716"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html"> 1716</a></span>&#160;<span class="keyword">class </span>TINYXML2_LIB <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> : <span class="keyword">public</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a></div>
<div class="line"><a name="l01717"></a><span class="lineno"> 1717</span>&#160;{</div>
<div class="line"><a name="l01718"></a><span class="lineno"> 1718</span>&#160;    <span class="keyword">friend</span> <span class="keyword">class </span><a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>;</div>
<div class="line"><a name="l01719"></a><span class="lineno"> 1719</span>&#160;    <span class="comment">// Gives access to SetError and Push/PopDepth, but over-access for everything else.</span></div>
<div class="line"><a name="l01720"></a><span class="lineno"> 1720</span>&#160;    <span class="comment">// Wishing C++ had &quot;internal&quot; scope.</span></div>
<div class="line"><a name="l01721"></a><span class="lineno"> 1721</span>&#160;    <span class="keyword">friend</span> <span class="keyword">class </span><a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>;</div>
<div class="line"><a name="l01722"></a><span class="lineno"> 1722</span>&#160;    <span class="keyword">friend</span> <span class="keyword">class </span><a class="code" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>;</div>
<div class="line"><a name="l01723"></a><span class="lineno"> 1723</span>&#160;    <span class="keyword">friend</span> <span class="keyword">class </span><a class="code" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a>;</div>
<div class="line"><a name="l01724"></a><span class="lineno"> 1724</span>&#160;    <span class="keyword">friend</span> <span class="keyword">class </span><a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>;</div>
<div class="line"><a name="l01725"></a><span class="lineno"> 1725</span>&#160;    <span class="keyword">friend</span> <span class="keyword">class </span><a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>;</div>
<div class="line"><a name="l01726"></a><span class="lineno"> 1726</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l01728"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a57ddf17b6e054dda10af98991b1b8f70"> 1728</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a57ddf17b6e054dda10af98991b1b8f70">XMLDocument</a>( <span class="keywordtype">bool</span> processEntities = <span class="keyword">true</span>, Whitespace whitespaceMode = PRESERVE_WHITESPACE );</div>
<div class="line"><a name="l01729"></a><span class="lineno"> 1729</span>&#160;    ~<a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>();</div>
<div class="line"><a name="l01730"></a><span class="lineno"> 1730</span>&#160; </div>
<div class="line"><a name="l01731"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a3e185f880882bd978367bb55937735ec"> 1731</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a3e185f880882bd978367bb55937735ec">ToDocument</a>()               {</div>
<div class="line"><a name="l01732"></a><span class="lineno"> 1732</span>&#160;        TIXMLASSERT( <span class="keyword">this</span> == _document );</div>
<div class="line"><a name="l01733"></a><span class="lineno"> 1733</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">this</span>;</div>
<div class="line"><a name="l01734"></a><span class="lineno"> 1734</span>&#160;    }</div>
<div class="line"><a name="l01735"></a><span class="lineno"> 1735</span>&#160;    <span class="keyword">virtual</span> <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* ToDocument()<span class="keyword"> const   </span>{</div>
<div class="line"><a name="l01736"></a><span class="lineno"> 1736</span>&#160;        TIXMLASSERT( <span class="keyword">this</span> == _document );</div>
<div class="line"><a name="l01737"></a><span class="lineno"> 1737</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">this</span>;</div>
<div class="line"><a name="l01738"></a><span class="lineno"> 1738</span>&#160;    }</div>
<div class="line"><a name="l01739"></a><span class="lineno"> 1739</span>&#160; </div>
<div class="line"><a name="l01750"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#af2b616169e6517182f6725f2498e9a01"> 1750</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#af2b616169e6517182f6725f2498e9a01">Parse</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* xml, <span class="keywordtype">size_t</span> nBytes=<span class="keyword">static_cast&lt;</span><span class="keywordtype">size_t</span><span class="keyword">&gt;</span>(-1) );</div>
<div class="line"><a name="l01751"></a><span class="lineno"> 1751</span>&#160; </div>
<div class="line"><a name="l01757"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a2ebd4647a8af5fc6831b294ac26a150a"> 1757</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a2ebd4647a8af5fc6831b294ac26a150a">LoadFile</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* filename );</div>
<div class="line"><a name="l01758"></a><span class="lineno"> 1758</span>&#160; </div>
<div class="line"><a name="l01770"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a5f1d330fad44c52f3d265338dd2a6dc2"> 1770</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a5f1d330fad44c52f3d265338dd2a6dc2">LoadFile</a>( FILE* );</div>
<div class="line"><a name="l01771"></a><span class="lineno"> 1771</span>&#160; </div>
<div class="line"><a name="l01777"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a73ac416b4a2aa0952e841220eb3da18f"> 1777</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a73ac416b4a2aa0952e841220eb3da18f">SaveFile</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* filename, <span class="keywordtype">bool</span> compact = <span class="keyword">false</span> );</div>
<div class="line"><a name="l01778"></a><span class="lineno"> 1778</span>&#160; </div>
<div class="line"><a name="l01786"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a8b95779479a0035acc67b3a61dfe1b74"> 1786</a></span>&#160;    XMLError <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a8b95779479a0035acc67b3a61dfe1b74">SaveFile</a>( FILE* fp, <span class="keywordtype">bool</span> compact = <span class="keyword">false</span> );</div>
<div class="line"><a name="l01787"></a><span class="lineno"> 1787</span>&#160; </div>
<div class="line"><a name="l01788"></a><span class="lineno"> 1788</span>&#160;    <span class="keywordtype">bool</span> ProcessEntities()<span class="keyword"> const        </span>{</div>
<div class="line"><a name="l01789"></a><span class="lineno"> 1789</span>&#160;        <span class="keywordflow">return</span> _processEntities;</div>
<div class="line"><a name="l01790"></a><span class="lineno"> 1790</span>&#160;    }</div>
<div class="line"><a name="l01791"></a><span class="lineno"> 1791</span>&#160;    Whitespace WhitespaceMode()<span class="keyword"> const   </span>{</div>
<div class="line"><a name="l01792"></a><span class="lineno"> 1792</span>&#160;        <span class="keywordflow">return</span> _whitespaceMode;</div>
<div class="line"><a name="l01793"></a><span class="lineno"> 1793</span>&#160;    }</div>
<div class="line"><a name="l01794"></a><span class="lineno"> 1794</span>&#160; </div>
<div class="line"><a name="l01798"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a33fc5d159db873a179fa26338adb05bd"> 1798</a></span>&#160;    <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a33fc5d159db873a179fa26338adb05bd">HasBOM</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01799"></a><span class="lineno"> 1799</span>&#160;        <span class="keywordflow">return</span> _writeBOM;</div>
<div class="line"><a name="l01800"></a><span class="lineno"> 1800</span>&#160;    }</div>
<div class="line"><a name="l01803"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a14419b698f7c4b140df4e80f3f0c93b0"> 1803</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a14419b698f7c4b140df4e80f3f0c93b0">SetBOM</a>( <span class="keywordtype">bool</span> useBOM ) {</div>
<div class="line"><a name="l01804"></a><span class="lineno"> 1804</span>&#160;        _writeBOM = useBOM;</div>
<div class="line"><a name="l01805"></a><span class="lineno"> 1805</span>&#160;    }</div>
<div class="line"><a name="l01806"></a><span class="lineno"> 1806</span>&#160; </div>
<div class="line"><a name="l01810"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#ad2b70320d3c2a071c2f36928edff3e1c"> 1810</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#ad2b70320d3c2a071c2f36928edff3e1c">RootElement</a>()               {</div>
<div class="line"><a name="l01811"></a><span class="lineno"> 1811</span>&#160;        <span class="keywordflow">return</span> FirstChildElement();</div>
<div class="line"><a name="l01812"></a><span class="lineno"> 1812</span>&#160;    }</div>
<div class="line"><a name="l01813"></a><span class="lineno"> 1813</span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* RootElement()<span class="keyword"> const   </span>{</div>
<div class="line"><a name="l01814"></a><span class="lineno"> 1814</span>&#160;        <span class="keywordflow">return</span> FirstChildElement();</div>
<div class="line"><a name="l01815"></a><span class="lineno"> 1815</span>&#160;    }</div>
<div class="line"><a name="l01816"></a><span class="lineno"> 1816</span>&#160; </div>
<div class="line"><a name="l01831"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a867cf5fa3e3ff6ae4847a8b7ee8ec083"> 1831</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a867cf5fa3e3ff6ae4847a8b7ee8ec083">Print</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html">XMLPrinter</a>* streamer=0 ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01832"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a9efa54f7ecb37c17ab1fa2b3078ccca1"> 1832</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a9efa54f7ecb37c17ab1fa2b3078ccca1">Accept</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a>* visitor ) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01833"></a><span class="lineno"> 1833</span>&#160; </div>
<div class="line"><a name="l01839"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a8aa7817d4a1001364b06373763ab99d6"> 1839</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a8aa7817d4a1001364b06373763ab99d6">NewElement</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name );</div>
<div class="line"><a name="l01845"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#ade4874bcb439954972ef2b3723ff3259"> 1845</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#ade4874bcb439954972ef2b3723ff3259">NewComment</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* comment );</div>
<div class="line"><a name="l01851"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#ab7e8b29ae4099092a8bb947da6361296"> 1851</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#ab7e8b29ae4099092a8bb947da6361296">NewText</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* text );</div>
<div class="line"><a name="l01863"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#aee2eb3435923f5494dcc70ac225b60a2"> 1863</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#aee2eb3435923f5494dcc70ac225b60a2">NewDeclaration</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* text=0 );</div>
<div class="line"><a name="l01869"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a5385c937734ff6db9226ab707d2c7147"> 1869</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a5385c937734ff6db9226ab707d2c7147">NewUnknown</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* text );</div>
<div class="line"><a name="l01870"></a><span class="lineno"> 1870</span>&#160; </div>
<div class="line"><a name="l01875"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#ac1d6e2c7fcc1a660624ac4f68e96380d"> 1875</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#ac1d6e2c7fcc1a660624ac4f68e96380d">DeleteNode</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* node );</div>
<div class="line"><a name="l01876"></a><span class="lineno"> 1876</span>&#160; </div>
<div class="line"><a name="l01878"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a4085d9c52f1d93214311459d6d1fcf17"> 1878</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a4085d9c52f1d93214311459d6d1fcf17">ClearError</a>();</div>
<div class="line"><a name="l01879"></a><span class="lineno"> 1879</span>&#160; </div>
<div class="line"><a name="l01881"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a34e6318e182e40e3cc4f4ba5d59ed9ed"> 1881</a></span>&#160;    <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a34e6318e182e40e3cc4f4ba5d59ed9ed">Error</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01882"></a><span class="lineno"> 1882</span>&#160;        <span class="keywordflow">return</span> _errorID != XML_SUCCESS;</div>
<div class="line"><a name="l01883"></a><span class="lineno"> 1883</span>&#160;    }</div>
<div class="line"><a name="l01885"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#afa3ed33b3107f920ec2b301f805ac17d"> 1885</a></span>&#160;    XMLError  <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#afa3ed33b3107f920ec2b301f805ac17d">ErrorID</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l01886"></a><span class="lineno"> 1886</span>&#160;        <span class="keywordflow">return</span> _errorID;</div>
<div class="line"><a name="l01887"></a><span class="lineno"> 1887</span>&#160;    }</div>
<div class="line"><a name="l01888"></a><span class="lineno"> 1888</span>&#160;    <span class="keyword">const</span> <span class="keywordtype">char</span>* ErrorName() <span class="keyword">const</span>;</div>
<div class="line"><a name="l01889"></a><span class="lineno"> 1889</span>&#160;    <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span>* ErrorIDToName(XMLError errorID);</div>
<div class="line"><a name="l01890"></a><span class="lineno"> 1890</span>&#160; </div>
<div class="line"><a name="l01894"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#ad75aa9d32c4e8b300655186808aa9abf"> 1894</a></span>&#160;    <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#ad75aa9d32c4e8b300655186808aa9abf">ErrorStr</a>() <span class="keyword">const</span>;</div>
<div class="line"><a name="l01895"></a><span class="lineno"> 1895</span>&#160; </div>
<div class="line"><a name="l01897"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a1d033945b42e125d933d6231e4571552"> 1897</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a1d033945b42e125d933d6231e4571552">PrintError</a>() <span class="keyword">const</span>;</div>
<div class="line"><a name="l01898"></a><span class="lineno"> 1898</span>&#160; </div>
<div class="line"><a name="l01900"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a57400f816dbe7799ece33615ead9ab76"> 1900</a></span>&#160;    <span class="keywordtype">int</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a57400f816dbe7799ece33615ead9ab76">ErrorLineNum</a>()<span class="keyword"> const</span></div>
<div class="line"><a name="l01901"></a><span class="lineno"> 1901</span>&#160;<span class="keyword">    </span>{</div>
<div class="line"><a name="l01902"></a><span class="lineno"> 1902</span>&#160;        <span class="keywordflow">return</span> _errorLineNum;</div>
<div class="line"><a name="l01903"></a><span class="lineno"> 1903</span>&#160;    }</div>
<div class="line"><a name="l01904"></a><span class="lineno"> 1904</span>&#160; </div>
<div class="line"><a name="l01906"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a65656b0b2cbc822708eb351504178aaf"> 1906</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a65656b0b2cbc822708eb351504178aaf">Clear</a>();</div>
<div class="line"><a name="l01907"></a><span class="lineno"> 1907</span>&#160; </div>
<div class="line"><a name="l01915"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#af592ffc91514e25a39664521ac83db45"> 1915</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#af592ffc91514e25a39664521ac83db45">DeepCopy</a>(<a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* target) <span class="keyword">const</span>;</div>
<div class="line"><a name="l01916"></a><span class="lineno"> 1916</span>&#160; </div>
<div class="line"><a name="l01917"></a><span class="lineno"> 1917</span>&#160;    <span class="comment">// internal</span></div>
<div class="line"><a name="l01918"></a><span class="lineno"> 1918</span>&#160;    <span class="keywordtype">char</span>* Identify( <span class="keywordtype">char</span>* p, <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>** node );</div>
<div class="line"><a name="l01919"></a><span class="lineno"> 1919</span>&#160; </div>
<div class="line"><a name="l01920"></a><span class="lineno"> 1920</span>&#160;    <span class="comment">// internal</span></div>
<div class="line"><a name="l01921"></a><span class="lineno"> 1921</span>&#160;    <span class="keywordtype">void</span> MarkInUse(<span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* <span class="keyword">const</span>);</div>
<div class="line"><a name="l01922"></a><span class="lineno"> 1922</span>&#160; </div>
<div class="line"><a name="l01923"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#aa37cc1709d7e1e988bc17dcfb24a69b8"> 1923</a></span>&#160;    <span class="keyword">virtual</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#aa37cc1709d7e1e988bc17dcfb24a69b8">ShallowClone</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>* <span class="comment">/*document*/</span> )<span class="keyword"> const    </span>{</div>
<div class="line"><a name="l01924"></a><span class="lineno"> 1924</span>&#160;        <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l01925"></a><span class="lineno"> 1925</span>&#160;    }</div>
<div class="line"><a name="l01926"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_document.html#a6fe5ef18699091844fcf64b56ffa5bf9"> 1926</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html#a6fe5ef18699091844fcf64b56ffa5bf9">ShallowEqual</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* <span class="comment">/*compare*/</span> )<span class="keyword"> const   </span>{</div>
<div class="line"><a name="l01927"></a><span class="lineno"> 1927</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">false</span>;</div>
<div class="line"><a name="l01928"></a><span class="lineno"> 1928</span>&#160;    }</div>
<div class="line"><a name="l01929"></a><span class="lineno"> 1929</span>&#160; </div>
<div class="line"><a name="l01930"></a><span class="lineno"> 1930</span>&#160;<span class="keyword">private</span>:</div>
<div class="line"><a name="l01931"></a><span class="lineno"> 1931</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>&amp; );  <span class="comment">// not supported</span></div>
<div class="line"><a name="l01932"></a><span class="lineno"> 1932</span>&#160;    <span class="keywordtype">void</span> operator=( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>&amp; );   <span class="comment">// not supported</span></div>
<div class="line"><a name="l01933"></a><span class="lineno"> 1933</span>&#160; </div>
<div class="line"><a name="l01934"></a><span class="lineno"> 1934</span>&#160;    <span class="keywordtype">bool</span>            _writeBOM;</div>
<div class="line"><a name="l01935"></a><span class="lineno"> 1935</span>&#160;    <span class="keywordtype">bool</span>            _processEntities;</div>
<div class="line"><a name="l01936"></a><span class="lineno"> 1936</span>&#160;    XMLError        _errorID;</div>
<div class="line"><a name="l01937"></a><span class="lineno"> 1937</span>&#160;    Whitespace      _whitespaceMode;</div>
<div class="line"><a name="l01938"></a><span class="lineno"> 1938</span>&#160;    <span class="keyword">mutable</span> StrPair _errorStr;</div>
<div class="line"><a name="l01939"></a><span class="lineno"> 1939</span>&#160;    <span class="keywordtype">int</span>             _errorLineNum;</div>
<div class="line"><a name="l01940"></a><span class="lineno"> 1940</span>&#160;    <span class="keywordtype">char</span>*           _charBuffer;</div>
<div class="line"><a name="l01941"></a><span class="lineno"> 1941</span>&#160;    <span class="keywordtype">int</span>             _parseCurLineNum;</div>
<div class="line"><a name="l01942"></a><span class="lineno"> 1942</span>&#160;    <span class="keywordtype">int</span>             _parsingDepth;</div>
<div class="line"><a name="l01943"></a><span class="lineno"> 1943</span>&#160;    <span class="comment">// Memory tracking does add some overhead.</span></div>
<div class="line"><a name="l01944"></a><span class="lineno"> 1944</span>&#160;    <span class="comment">// However, the code assumes that you don&#39;t</span></div>
<div class="line"><a name="l01945"></a><span class="lineno"> 1945</span>&#160;    <span class="comment">// have a bunch of unlinked nodes around.</span></div>
<div class="line"><a name="l01946"></a><span class="lineno"> 1946</span>&#160;    <span class="comment">// Therefore it takes less memory to track</span></div>
<div class="line"><a name="l01947"></a><span class="lineno"> 1947</span>&#160;    <span class="comment">// in the document vs. a linked list in the XMLNode,</span></div>
<div class="line"><a name="l01948"></a><span class="lineno"> 1948</span>&#160;    <span class="comment">// and the performance is the same.</span></div>
<div class="line"><a name="l01949"></a><span class="lineno"> 1949</span>&#160;    DynArray&lt;XMLNode*, 10&gt; _unlinked;</div>
<div class="line"><a name="l01950"></a><span class="lineno"> 1950</span>&#160; </div>
<div class="line"><a name="l01951"></a><span class="lineno"> 1951</span>&#160;    MemPoolT&lt; <span class="keyword">sizeof</span>(<a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>) &gt;   _elementPool;</div>
<div class="line"><a name="l01952"></a><span class="lineno"> 1952</span>&#160;    MemPoolT&lt; <span class="keyword">sizeof</span>(<a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>) &gt; _attributePool;</div>
<div class="line"><a name="l01953"></a><span class="lineno"> 1953</span>&#160;    MemPoolT&lt; <span class="keyword">sizeof</span>(<a class="code" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>) &gt;      _textPool;</div>
<div class="line"><a name="l01954"></a><span class="lineno"> 1954</span>&#160;    MemPoolT&lt; <span class="keyword">sizeof</span>(<a class="code" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a>) &gt;   _commentPool;</div>
<div class="line"><a name="l01955"></a><span class="lineno"> 1955</span>&#160; </div>
<div class="line"><a name="l01956"></a><span class="lineno"> 1956</span>&#160;    <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span>* _errorNames[XML_ERROR_COUNT];</div>
<div class="line"><a name="l01957"></a><span class="lineno"> 1957</span>&#160; </div>
<div class="line"><a name="l01958"></a><span class="lineno"> 1958</span>&#160;    <span class="keywordtype">void</span> Parse();</div>
<div class="line"><a name="l01959"></a><span class="lineno"> 1959</span>&#160; </div>
<div class="line"><a name="l01960"></a><span class="lineno"> 1960</span>&#160;    <span class="keywordtype">void</span> SetError( XMLError error, <span class="keywordtype">int</span> lineNum, <span class="keyword">const</span> <span class="keywordtype">char</span>* format, ... );</div>
<div class="line"><a name="l01961"></a><span class="lineno"> 1961</span>&#160; </div>
<div class="line"><a name="l01962"></a><span class="lineno"> 1962</span>&#160;    <span class="comment">// Something of an obvious security hole, once it was discovered.</span></div>
<div class="line"><a name="l01963"></a><span class="lineno"> 1963</span>&#160;    <span class="comment">// Either an ill-formed XML or an excessively deep one can overflow</span></div>
<div class="line"><a name="l01964"></a><span class="lineno"> 1964</span>&#160;    <span class="comment">// the stack. Track stack depth, and error out if needed.</span></div>
<div class="line"><a name="l01965"></a><span class="lineno"> 1965</span>&#160;    <span class="keyword">class </span>DepthTracker {</div>
<div class="line"><a name="l01966"></a><span class="lineno"> 1966</span>&#160;    <span class="keyword">public</span>:</div>
<div class="line"><a name="l01967"></a><span class="lineno"> 1967</span>&#160;        <span class="keyword">explicit</span> DepthTracker(<a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> * document) {</div>
<div class="line"><a name="l01968"></a><span class="lineno"> 1968</span>&#160;            this-&gt;_document = document;</div>
<div class="line"><a name="l01969"></a><span class="lineno"> 1969</span>&#160;            document-&gt;PushDepth();</div>
<div class="line"><a name="l01970"></a><span class="lineno"> 1970</span>&#160;        }</div>
<div class="line"><a name="l01971"></a><span class="lineno"> 1971</span>&#160;        ~DepthTracker() {</div>
<div class="line"><a name="l01972"></a><span class="lineno"> 1972</span>&#160;            _document-&gt;PopDepth();</div>
<div class="line"><a name="l01973"></a><span class="lineno"> 1973</span>&#160;        }</div>
<div class="line"><a name="l01974"></a><span class="lineno"> 1974</span>&#160;    <span class="keyword">private</span>:</div>
<div class="line"><a name="l01975"></a><span class="lineno"> 1975</span>&#160;        XMLDocument * _document;</div>
<div class="line"><a name="l01976"></a><span class="lineno"> 1976</span>&#160;    };</div>
<div class="line"><a name="l01977"></a><span class="lineno"> 1977</span>&#160;    <span class="keywordtype">void</span> PushDepth();</div>
<div class="line"><a name="l01978"></a><span class="lineno"> 1978</span>&#160;    <span class="keywordtype">void</span> PopDepth();</div>
<div class="line"><a name="l01979"></a><span class="lineno"> 1979</span>&#160; </div>
<div class="line"><a name="l01980"></a><span class="lineno"> 1980</span>&#160;    <span class="keyword">template</span>&lt;<span class="keyword">class</span> NodeType, <span class="keywordtype">int</span> PoolElementSize&gt;</div>
<div class="line"><a name="l01981"></a><span class="lineno"> 1981</span>&#160;    NodeType* CreateUnlinkedNode( MemPoolT&lt;PoolElementSize&gt;&amp; pool );</div>
<div class="line"><a name="l01982"></a><span class="lineno"> 1982</span>&#160;};</div>
<div class="line"><a name="l01983"></a><span class="lineno"> 1983</span>&#160; </div>
<div class="line"><a name="l01984"></a><span class="lineno"> 1984</span>&#160;<span class="keyword">template</span>&lt;<span class="keyword">class</span> NodeType, <span class="keywordtype">int</span> PoolElementSize&gt;</div>
<div class="line"><a name="l01985"></a><span class="lineno"> 1985</span>&#160;<span class="keyword">inline</span> NodeType* XMLDocument::CreateUnlinkedNode( MemPoolT&lt;PoolElementSize&gt;&amp; pool )</div>
<div class="line"><a name="l01986"></a><span class="lineno"> 1986</span>&#160;{</div>
<div class="line"><a name="l01987"></a><span class="lineno"> 1987</span>&#160;    TIXMLASSERT( <span class="keyword">sizeof</span>( NodeType ) == PoolElementSize );</div>
<div class="line"><a name="l01988"></a><span class="lineno"> 1988</span>&#160;    TIXMLASSERT( <span class="keyword">sizeof</span>( NodeType ) == pool.ItemSize() );</div>
<div class="line"><a name="l01989"></a><span class="lineno"> 1989</span>&#160;    NodeType* returnNode = <span class="keyword">new</span> (pool.Alloc()) NodeType( <span class="keyword">this</span> );</div>
<div class="line"><a name="l01990"></a><span class="lineno"> 1990</span>&#160;    TIXMLASSERT( returnNode );</div>
<div class="line"><a name="l01991"></a><span class="lineno"> 1991</span>&#160;    returnNode-&gt;_memPool = &amp;pool;</div>
<div class="line"><a name="l01992"></a><span class="lineno"> 1992</span>&#160; </div>
<div class="line"><a name="l01993"></a><span class="lineno"> 1993</span>&#160;    _unlinked.Push(returnNode);</div>
<div class="line"><a name="l01994"></a><span class="lineno"> 1994</span>&#160;    <span class="keywordflow">return</span> returnNode;</div>
<div class="line"><a name="l01995"></a><span class="lineno"> 1995</span>&#160;}</div>
<div class="line"><a name="l01996"></a><span class="lineno"> 1996</span>&#160; </div>
<div class="line"><a name="l02052"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html"> 2052</a></span>&#160;<span class="keyword">class </span>TINYXML2_LIB <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a></div>
<div class="line"><a name="l02053"></a><span class="lineno"> 2053</span>&#160;{</div>
<div class="line"><a name="l02054"></a><span class="lineno"> 2054</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l02056"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#a9c240a35c18f053509b4b97ddccd9793"> 2056</a></span>&#160;    <span class="keyword">explicit</span> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#a9c240a35c18f053509b4b97ddccd9793">XMLHandle</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* node ) : _node( node ) {</div>
<div class="line"><a name="l02057"></a><span class="lineno"> 2057</span>&#160;    }</div>
<div class="line"><a name="l02059"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#aa2edbc1c0d3e3e8259bd98de7f1cf500"> 2059</a></span>&#160;    <span class="keyword">explicit</span> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#aa2edbc1c0d3e3e8259bd98de7f1cf500">XMLHandle</a>( <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>&amp; node ) : _node( &amp;node ) {</div>
<div class="line"><a name="l02060"></a><span class="lineno"> 2060</span>&#160;    }</div>
<div class="line"><a name="l02062"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#afd8e01e6018c07347b8e6d80272466aa"> 2062</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#afd8e01e6018c07347b8e6d80272466aa">XMLHandle</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>&amp; ref ) : _node( ref._node ) {</div>
<div class="line"><a name="l02063"></a><span class="lineno"> 2063</span>&#160;    }</div>
<div class="line"><a name="l02065"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#a75b908322bb4b83be3281b6845252b20"> 2065</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>&amp; <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#a75b908322bb4b83be3281b6845252b20">operator=</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>&amp; ref )                            {</div>
<div class="line"><a name="l02066"></a><span class="lineno"> 2066</span>&#160;        _node = ref._node;</div>
<div class="line"><a name="l02067"></a><span class="lineno"> 2067</span>&#160;        <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div>
<div class="line"><a name="l02068"></a><span class="lineno"> 2068</span>&#160;    }</div>
<div class="line"><a name="l02069"></a><span class="lineno"> 2069</span>&#160; </div>
<div class="line"><a name="l02071"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#a536447dc7f54c0cd11e031dad94795ae"> 2071</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#a536447dc7f54c0cd11e031dad94795ae">FirstChild</a>()                                                  {</div>
<div class="line"><a name="l02072"></a><span class="lineno"> 2072</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>( _node ? _node-&gt;FirstChild() : 0 );</div>
<div class="line"><a name="l02073"></a><span class="lineno"> 2073</span>&#160;    }</div>
<div class="line"><a name="l02075"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#a74b04dd0f15e0bf01860e282b840b6a3"> 2075</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#a74b04dd0f15e0bf01860e282b840b6a3">FirstChildElement</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name = 0 )                     {</div>
<div class="line"><a name="l02076"></a><span class="lineno"> 2076</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>( _node ? _node-&gt;FirstChildElement( name ) : 0 );</div>
<div class="line"><a name="l02077"></a><span class="lineno"> 2077</span>&#160;    }</div>
<div class="line"><a name="l02079"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#a9d09f04435f0f2f7d0816b0198d0517b"> 2079</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#a9d09f04435f0f2f7d0816b0198d0517b">LastChild</a>()                                                   {</div>
<div class="line"><a name="l02080"></a><span class="lineno"> 2080</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>( _node ? _node-&gt;LastChild() : 0 );</div>
<div class="line"><a name="l02081"></a><span class="lineno"> 2081</span>&#160;    }</div>
<div class="line"><a name="l02083"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#a42cccd0ce8b1ce704f431025e9f19e0c"> 2083</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#a42cccd0ce8b1ce704f431025e9f19e0c">LastChildElement</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name = 0 )                      {</div>
<div class="line"><a name="l02084"></a><span class="lineno"> 2084</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>( _node ? _node-&gt;LastChildElement( name ) : 0 );</div>
<div class="line"><a name="l02085"></a><span class="lineno"> 2085</span>&#160;    }</div>
<div class="line"><a name="l02087"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#a428374e756f4db4cbc287fec64eae02c"> 2087</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#a428374e756f4db4cbc287fec64eae02c">PreviousSibling</a>()                                             {</div>
<div class="line"><a name="l02088"></a><span class="lineno"> 2088</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>( _node ? _node-&gt;PreviousSibling() : 0 );</div>
<div class="line"><a name="l02089"></a><span class="lineno"> 2089</span>&#160;    }</div>
<div class="line"><a name="l02091"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#a786957e498039554ed334cdc36612a7e"> 2091</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#a786957e498039554ed334cdc36612a7e">PreviousSiblingElement</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name = 0 )                {</div>
<div class="line"><a name="l02092"></a><span class="lineno"> 2092</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>( _node ? _node-&gt;PreviousSiblingElement( name ) : 0 );</div>
<div class="line"><a name="l02093"></a><span class="lineno"> 2093</span>&#160;    }</div>
<div class="line"><a name="l02095"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#aad2eccc7c7c7b18145877c978c3850b5"> 2095</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#aad2eccc7c7c7b18145877c978c3850b5">NextSibling</a>()                                                 {</div>
<div class="line"><a name="l02096"></a><span class="lineno"> 2096</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>( _node ? _node-&gt;NextSibling() : 0 );</div>
<div class="line"><a name="l02097"></a><span class="lineno"> 2097</span>&#160;    }</div>
<div class="line"><a name="l02099"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#ae41d88ee061f3c49a081630ff753b2c5"> 2099</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#ae41d88ee061f3c49a081630ff753b2c5">NextSiblingElement</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name = 0 )                    {</div>
<div class="line"><a name="l02100"></a><span class="lineno"> 2100</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>( _node ? _node-&gt;NextSiblingElement( name ) : 0 );</div>
<div class="line"><a name="l02101"></a><span class="lineno"> 2101</span>&#160;    }</div>
<div class="line"><a name="l02102"></a><span class="lineno"> 2102</span>&#160; </div>
<div class="line"><a name="l02104"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#a03ea6ec970a021b71bf1219a0f6717df"> 2104</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#a03ea6ec970a021b71bf1219a0f6717df">ToNode</a>()                           {</div>
<div class="line"><a name="l02105"></a><span class="lineno"> 2105</span>&#160;        <span class="keywordflow">return</span> _node;</div>
<div class="line"><a name="l02106"></a><span class="lineno"> 2106</span>&#160;    }</div>
<div class="line"><a name="l02108"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#a5e73ed8f3f6f9619d5a8bb1862c47d99"> 2108</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#a5e73ed8f3f6f9619d5a8bb1862c47d99">ToElement</a>()                     {</div>
<div class="line"><a name="l02109"></a><span class="lineno"> 2109</span>&#160;        <span class="keywordflow">return</span> ( _node ? _node-&gt;ToElement() : 0 );</div>
<div class="line"><a name="l02110"></a><span class="lineno"> 2110</span>&#160;    }</div>
<div class="line"><a name="l02112"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#a6ab9e8cbfb41417246e5657e3842c62a"> 2112</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#a6ab9e8cbfb41417246e5657e3842c62a">ToText</a>()                           {</div>
<div class="line"><a name="l02113"></a><span class="lineno"> 2113</span>&#160;        <span class="keywordflow">return</span> ( _node ? _node-&gt;ToText() : 0 );</div>
<div class="line"><a name="l02114"></a><span class="lineno"> 2114</span>&#160;    }</div>
<div class="line"><a name="l02116"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#aa387368a1ad8d843a9f12df863d298de"> 2116</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#aa387368a1ad8d843a9f12df863d298de">ToUnknown</a>()                     {</div>
<div class="line"><a name="l02117"></a><span class="lineno"> 2117</span>&#160;        <span class="keywordflow">return</span> ( _node ? _node-&gt;ToUnknown() : 0 );</div>
<div class="line"><a name="l02118"></a><span class="lineno"> 2118</span>&#160;    }</div>
<div class="line"><a name="l02120"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_handle.html#a108858be7ee3eb53f73b5194c1aa8ff0"> 2120</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>* <a class="code" href="classtinyxml2_1_1_x_m_l_handle.html#a108858be7ee3eb53f73b5194c1aa8ff0">ToDeclaration</a>()             {</div>
<div class="line"><a name="l02121"></a><span class="lineno"> 2121</span>&#160;        <span class="keywordflow">return</span> ( _node ? _node-&gt;ToDeclaration() : 0 );</div>
<div class="line"><a name="l02122"></a><span class="lineno"> 2122</span>&#160;    }</div>
<div class="line"><a name="l02123"></a><span class="lineno"> 2123</span>&#160; </div>
<div class="line"><a name="l02124"></a><span class="lineno"> 2124</span>&#160;<span class="keyword">private</span>:</div>
<div class="line"><a name="l02125"></a><span class="lineno"> 2125</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* _node;</div>
<div class="line"><a name="l02126"></a><span class="lineno"> 2126</span>&#160;};</div>
<div class="line"><a name="l02127"></a><span class="lineno"> 2127</span>&#160; </div>
<div class="line"><a name="l02128"></a><span class="lineno"> 2128</span>&#160; </div>
<div class="line"><a name="l02133"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_const_handle.html"> 2133</a></span>&#160;<span class="keyword">class </span>TINYXML2_LIB <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a></div>
<div class="line"><a name="l02134"></a><span class="lineno"> 2134</span>&#160;{</div>
<div class="line"><a name="l02135"></a><span class="lineno"> 2135</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l02136"></a><span class="lineno"> 2136</span>&#160;    <span class="keyword">explicit</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* node ) : _node( node ) {</div>
<div class="line"><a name="l02137"></a><span class="lineno"> 2137</span>&#160;    }</div>
<div class="line"><a name="l02138"></a><span class="lineno"> 2138</span>&#160;    <span class="keyword">explicit</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>&amp; node ) : _node( &amp;node ) {</div>
<div class="line"><a name="l02139"></a><span class="lineno"> 2139</span>&#160;    }</div>
<div class="line"><a name="l02140"></a><span class="lineno"> 2140</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a>&amp; ref ) : _node( ref._node ) {</div>
<div class="line"><a name="l02141"></a><span class="lineno"> 2141</span>&#160;    }</div>
<div class="line"><a name="l02142"></a><span class="lineno"> 2142</span>&#160; </div>
<div class="line"><a name="l02143"></a><span class="lineno"> 2143</span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a>&amp; operator=( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a>&amp; ref )                          {</div>
<div class="line"><a name="l02144"></a><span class="lineno"> 2144</span>&#160;        _node = ref._node;</div>
<div class="line"><a name="l02145"></a><span class="lineno"> 2145</span>&#160;        <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div>
<div class="line"><a name="l02146"></a><span class="lineno"> 2146</span>&#160;    }</div>
<div class="line"><a name="l02147"></a><span class="lineno"> 2147</span>&#160; </div>
<div class="line"><a name="l02148"></a><span class="lineno"> 2148</span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a> FirstChild()<span class="keyword"> const                                         </span>{</div>
<div class="line"><a name="l02149"></a><span class="lineno"> 2149</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a>( _node ? _node-&gt;FirstChild() : 0 );</div>
<div class="line"><a name="l02150"></a><span class="lineno"> 2150</span>&#160;    }</div>
<div class="line"><a name="l02151"></a><span class="lineno"> 2151</span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a> FirstChildElement( <span class="keyword">const</span> <span class="keywordtype">char</span>* name = 0 )<span class="keyword"> const                </span>{</div>
<div class="line"><a name="l02152"></a><span class="lineno"> 2152</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a>( _node ? _node-&gt;FirstChildElement( name ) : 0 );</div>
<div class="line"><a name="l02153"></a><span class="lineno"> 2153</span>&#160;    }</div>
<div class="line"><a name="l02154"></a><span class="lineno"> 2154</span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a> LastChild()<span class="keyword">    const                                       </span>{</div>
<div class="line"><a name="l02155"></a><span class="lineno"> 2155</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a>( _node ? _node-&gt;LastChild() : 0 );</div>
<div class="line"><a name="l02156"></a><span class="lineno"> 2156</span>&#160;    }</div>
<div class="line"><a name="l02157"></a><span class="lineno"> 2157</span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a> LastChildElement( <span class="keyword">const</span> <span class="keywordtype">char</span>* name = 0 )<span class="keyword"> const             </span>{</div>
<div class="line"><a name="l02158"></a><span class="lineno"> 2158</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a>( _node ? _node-&gt;LastChildElement( name ) : 0 );</div>
<div class="line"><a name="l02159"></a><span class="lineno"> 2159</span>&#160;    }</div>
<div class="line"><a name="l02160"></a><span class="lineno"> 2160</span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a> PreviousSibling()<span class="keyword"> const                                    </span>{</div>
<div class="line"><a name="l02161"></a><span class="lineno"> 2161</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a>( _node ? _node-&gt;PreviousSibling() : 0 );</div>
<div class="line"><a name="l02162"></a><span class="lineno"> 2162</span>&#160;    }</div>
<div class="line"><a name="l02163"></a><span class="lineno"> 2163</span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a> PreviousSiblingElement( <span class="keyword">const</span> <span class="keywordtype">char</span>* name = 0 )<span class="keyword"> const       </span>{</div>
<div class="line"><a name="l02164"></a><span class="lineno"> 2164</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a>( _node ? _node-&gt;PreviousSiblingElement( name ) : 0 );</div>
<div class="line"><a name="l02165"></a><span class="lineno"> 2165</span>&#160;    }</div>
<div class="line"><a name="l02166"></a><span class="lineno"> 2166</span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a> NextSibling()<span class="keyword"> const                                        </span>{</div>
<div class="line"><a name="l02167"></a><span class="lineno"> 2167</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a>( _node ? _node-&gt;NextSibling() : 0 );</div>
<div class="line"><a name="l02168"></a><span class="lineno"> 2168</span>&#160;    }</div>
<div class="line"><a name="l02169"></a><span class="lineno"> 2169</span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a> NextSiblingElement( <span class="keyword">const</span> <span class="keywordtype">char</span>* name = 0 )<span class="keyword"> const           </span>{</div>
<div class="line"><a name="l02170"></a><span class="lineno"> 2170</span>&#160;        <span class="keywordflow">return</span> <a class="code" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a>( _node ? _node-&gt;NextSiblingElement( name ) : 0 );</div>
<div class="line"><a name="l02171"></a><span class="lineno"> 2171</span>&#160;    }</div>
<div class="line"><a name="l02172"></a><span class="lineno"> 2172</span>&#160; </div>
<div class="line"><a name="l02173"></a><span class="lineno"> 2173</span>&#160; </div>
<div class="line"><a name="l02174"></a><span class="lineno"> 2174</span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* ToNode()<span class="keyword"> const               </span>{</div>
<div class="line"><a name="l02175"></a><span class="lineno"> 2175</span>&#160;        <span class="keywordflow">return</span> _node;</div>
<div class="line"><a name="l02176"></a><span class="lineno"> 2176</span>&#160;    }</div>
<div class="line"><a name="l02177"></a><span class="lineno"> 2177</span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* ToElement()<span class="keyword"> const         </span>{</div>
<div class="line"><a name="l02178"></a><span class="lineno"> 2178</span>&#160;        <span class="keywordflow">return</span> ( _node ? _node-&gt;ToElement() : 0 );</div>
<div class="line"><a name="l02179"></a><span class="lineno"> 2179</span>&#160;    }</div>
<div class="line"><a name="l02180"></a><span class="lineno"> 2180</span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>* ToText()<span class="keyword"> const               </span>{</div>
<div class="line"><a name="l02181"></a><span class="lineno"> 2181</span>&#160;        <span class="keywordflow">return</span> ( _node ? _node-&gt;ToText() : 0 );</div>
<div class="line"><a name="l02182"></a><span class="lineno"> 2182</span>&#160;    }</div>
<div class="line"><a name="l02183"></a><span class="lineno"> 2183</span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>* ToUnknown()<span class="keyword"> const         </span>{</div>
<div class="line"><a name="l02184"></a><span class="lineno"> 2184</span>&#160;        <span class="keywordflow">return</span> ( _node ? _node-&gt;ToUnknown() : 0 );</div>
<div class="line"><a name="l02185"></a><span class="lineno"> 2185</span>&#160;    }</div>
<div class="line"><a name="l02186"></a><span class="lineno"> 2186</span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>* ToDeclaration()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l02187"></a><span class="lineno"> 2187</span>&#160;        <span class="keywordflow">return</span> ( _node ? _node-&gt;ToDeclaration() : 0 );</div>
<div class="line"><a name="l02188"></a><span class="lineno"> 2188</span>&#160;    }</div>
<div class="line"><a name="l02189"></a><span class="lineno"> 2189</span>&#160; </div>
<div class="line"><a name="l02190"></a><span class="lineno"> 2190</span>&#160;<span class="keyword">private</span>:</div>
<div class="line"><a name="l02191"></a><span class="lineno"> 2191</span>&#160;    <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* _node;</div>
<div class="line"><a name="l02192"></a><span class="lineno"> 2192</span>&#160;};</div>
<div class="line"><a name="l02193"></a><span class="lineno"> 2193</span>&#160; </div>
<div class="line"><a name="l02194"></a><span class="lineno"> 2194</span>&#160; </div>
<div class="line"><a name="l02237"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html"> 2237</a></span>&#160;<span class="keyword">class </span>TINYXML2_LIB <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html">XMLPrinter</a> : <span class="keyword">public</span> <a class="code" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a></div>
<div class="line"><a name="l02238"></a><span class="lineno"> 2238</span>&#160;{</div>
<div class="line"><a name="l02239"></a><span class="lineno"> 2239</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l02246"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#aa6d3841c069085f5b8a27bc7103c04f7"> 2246</a></span>&#160;    <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#aa6d3841c069085f5b8a27bc7103c04f7">XMLPrinter</a>( FILE* file=0, <span class="keywordtype">bool</span> compact = <span class="keyword">false</span>, <span class="keywordtype">int</span> depth = 0 );</div>
<div class="line"><a name="l02247"></a><span class="lineno"> 2247</span>&#160;    <span class="keyword">virtual</span> ~<a class="code" href="classtinyxml2_1_1_x_m_l_printer.html">XMLPrinter</a>()   {}</div>
<div class="line"><a name="l02248"></a><span class="lineno"> 2248</span>&#160; </div>
<div class="line"><a name="l02250"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a178c608ce8476043d5d6513819cde903"> 2250</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a178c608ce8476043d5d6513819cde903">PushHeader</a>( <span class="keywordtype">bool</span> writeBOM, <span class="keywordtype">bool</span> writeDeclaration );</div>
<div class="line"><a name="l02254"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a20fb06c83bd13e5140d7dd13af06c010"> 2254</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a20fb06c83bd13e5140d7dd13af06c010">OpenElement</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">bool</span> compactMode=<span class="keyword">false</span> );</div>
<div class="line"><a name="l02256"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a9a4e2c9348b42e147629d5a99f4af3f0"> 2256</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a9a4e2c9348b42e147629d5a99f4af3f0">PushAttribute</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keyword">const</span> <span class="keywordtype">char</span>* value );</div>
<div class="line"><a name="l02257"></a><span class="lineno"> 2257</span>&#160;    <span class="keywordtype">void</span> PushAttribute( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">int</span> value );</div>
<div class="line"><a name="l02258"></a><span class="lineno"> 2258</span>&#160;    <span class="keywordtype">void</span> PushAttribute( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">unsigned</span> value );</div>
<div class="line"><a name="l02259"></a><span class="lineno"> 2259</span>&#160;    <span class="keywordtype">void</span> PushAttribute( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, int64_t value );</div>
<div class="line"><a name="l02260"></a><span class="lineno"> 2260</span>&#160;    <span class="keywordtype">void</span> PushAttribute( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, uint64_t value );</div>
<div class="line"><a name="l02261"></a><span class="lineno"> 2261</span>&#160;    <span class="keywordtype">void</span> PushAttribute( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">bool</span> value );</div>
<div class="line"><a name="l02262"></a><span class="lineno"> 2262</span>&#160;    <span class="keywordtype">void</span> PushAttribute( <span class="keyword">const</span> <span class="keywordtype">char</span>* name, <span class="keywordtype">double</span> value );</div>
<div class="line"><a name="l02264"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#ad04d29562b46fcdb23ab320f8b664240"> 2264</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#ad04d29562b46fcdb23ab320f8b664240">CloseElement</a>( <span class="keywordtype">bool</span> compactMode=<span class="keyword">false</span> );</div>
<div class="line"><a name="l02265"></a><span class="lineno"> 2265</span>&#160; </div>
<div class="line"><a name="l02267"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a1cc16a9362df4332012cb13cff6441b3"> 2267</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a1cc16a9362df4332012cb13cff6441b3">PushText</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* text, <span class="keywordtype">bool</span> cdata=<span class="keyword">false</span> );</div>
<div class="line"><a name="l02269"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a3e0d4d78de25d4cf081009e1431cea7e"> 2269</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a3e0d4d78de25d4cf081009e1431cea7e">PushText</a>( <span class="keywordtype">int</span> value );</div>
<div class="line"><a name="l02271"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a661fb50e7e0a4918d2d259cb0fae647e"> 2271</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a661fb50e7e0a4918d2d259cb0fae647e">PushText</a>( <span class="keywordtype">unsigned</span> value );</div>
<div class="line"><a name="l02273"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a96b0a0bfe105154a0a6c37d725258f0a"> 2273</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a96b0a0bfe105154a0a6c37d725258f0a">PushText</a>( int64_t value );</div>
<div class="line"><a name="l02275"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a60b0a4cf57371ff8679c2c7556ccb708"> 2275</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a60b0a4cf57371ff8679c2c7556ccb708">PushText</a>( uint64_t value );</div>
<div class="line"><a name="l02277"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a4390e5fa1ed05189a8686647345ab29f"> 2277</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a4390e5fa1ed05189a8686647345ab29f">PushText</a>( <span class="keywordtype">bool</span> value );</div>
<div class="line"><a name="l02279"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a1dbb1390e829d0673af66b9cd1928bd7"> 2279</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a1dbb1390e829d0673af66b9cd1928bd7">PushText</a>( <span class="keywordtype">float</span> value );</div>
<div class="line"><a name="l02281"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#aa715302dfc09473c77c853cbd5431965"> 2281</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#aa715302dfc09473c77c853cbd5431965">PushText</a>( <span class="keywordtype">double</span> value );</div>
<div class="line"><a name="l02282"></a><span class="lineno"> 2282</span>&#160; </div>
<div class="line"><a name="l02284"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#afc8416814219591c2fd5656e0c233140"> 2284</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#afc8416814219591c2fd5656e0c233140">PushComment</a>( <span class="keyword">const</span> <span class="keywordtype">char</span>* comment );</div>
<div class="line"><a name="l02285"></a><span class="lineno"> 2285</span>&#160; </div>
<div class="line"><a name="l02286"></a><span class="lineno"> 2286</span>&#160;    <span class="keywordtype">void</span> PushDeclaration( <span class="keyword">const</span> <span class="keywordtype">char</span>* value );</div>
<div class="line"><a name="l02287"></a><span class="lineno"> 2287</span>&#160;    <span class="keywordtype">void</span> PushUnknown( <span class="keyword">const</span> <span class="keywordtype">char</span>* value );</div>
<div class="line"><a name="l02288"></a><span class="lineno"> 2288</span>&#160; </div>
<div class="line"><a name="l02289"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#ae966b988a7a28c41e91c5ca17fb2054b"> 2289</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#ae966b988a7a28c41e91c5ca17fb2054b">VisitEnter</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>&amp; <span class="comment">/*doc*/</span> );</div>
<div class="line"><a name="l02290"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a15fc1f2b922f540917dcf52808737b29"> 2290</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a15fc1f2b922f540917dcf52808737b29">VisitExit</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>&amp; <span class="comment">/*doc*/</span> )            {</div>
<div class="line"><a name="l02291"></a><span class="lineno"> 2291</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">true</span>;</div>
<div class="line"><a name="l02292"></a><span class="lineno"> 2292</span>&#160;    }</div>
<div class="line"><a name="l02293"></a><span class="lineno"> 2293</span>&#160; </div>
<div class="line"><a name="l02294"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a2ce2aa508c21ac91615093ddb9c282c5"> 2294</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a2ce2aa508c21ac91615093ddb9c282c5">VisitEnter</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>&amp; element, <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a>* attribute );</div>
<div class="line"><a name="l02295"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#ae99e0a7086543591edfb565f24689098"> 2295</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#ae99e0a7086543591edfb565f24689098">VisitExit</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>&amp; element );</div>
<div class="line"><a name="l02296"></a><span class="lineno"> 2296</span>&#160; </div>
<div class="line"><a name="l02297"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a275ae25544a12199ae40b6994ca6e4de"> 2297</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a275ae25544a12199ae40b6994ca6e4de">Visit</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>&amp; text );</div>
<div class="line"><a name="l02298"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a3f16a30be1537ac141d9bd2db824ba9e"> 2298</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a3f16a30be1537ac141d9bd2db824ba9e">Visit</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a>&amp; comment );</div>
<div class="line"><a name="l02299"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a9ceff5cd85e5db65838962174fcdcc46"> 2299</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a9ceff5cd85e5db65838962174fcdcc46">Visit</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>&amp; declaration );</div>
<div class="line"><a name="l02300"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#aa15e1da81e17dea5da6499ac5b08d9d8"> 2300</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#aa15e1da81e17dea5da6499ac5b08d9d8">Visit</a>( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>&amp; unknown );</div>
<div class="line"><a name="l02301"></a><span class="lineno"> 2301</span>&#160; </div>
<div class="line"><a name="l02306"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a180671d73844f159f2d4aafbc11d106e"> 2306</a></span>&#160;    <span class="keyword">const</span> <span class="keywordtype">char</span>* <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a180671d73844f159f2d4aafbc11d106e">CStr</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l02307"></a><span class="lineno"> 2307</span>&#160;        <span class="keywordflow">return</span> _buffer.Mem();</div>
<div class="line"><a name="l02308"></a><span class="lineno"> 2308</span>&#160;    }</div>
<div class="line"><a name="l02314"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a3256cf3523d4898b91abb18b924be04c"> 2314</a></span>&#160;    <span class="keywordtype">int</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a3256cf3523d4898b91abb18b924be04c">CStrSize</a>()<span class="keyword"> const </span>{</div>
<div class="line"><a name="l02315"></a><span class="lineno"> 2315</span>&#160;        <span class="keywordflow">return</span> _buffer.Size();</div>
<div class="line"><a name="l02316"></a><span class="lineno"> 2316</span>&#160;    }</div>
<div class="line"><a name="l02321"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a690cb140ba98b7339734ff865f56b0b3"> 2321</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a690cb140ba98b7339734ff865f56b0b3">ClearBuffer</a>( <span class="keywordtype">bool</span> resetToFirstElement = <span class="keyword">true</span> ) {</div>
<div class="line"><a name="l02322"></a><span class="lineno"> 2322</span>&#160;        _buffer.Clear();</div>
<div class="line"><a name="l02323"></a><span class="lineno"> 2323</span>&#160;        _buffer.Push(0);</div>
<div class="line"><a name="l02324"></a><span class="lineno"> 2324</span>&#160;        _firstElement = resetToFirstElement;</div>
<div class="line"><a name="l02325"></a><span class="lineno"> 2325</span>&#160;    }</div>
<div class="line"><a name="l02326"></a><span class="lineno"> 2326</span>&#160; </div>
<div class="line"><a name="l02327"></a><span class="lineno"> 2327</span>&#160;<span class="keyword">protected</span>:</div>
<div class="line"><a name="l02328"></a><span class="lineno"> 2328</span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> CompactMode( <span class="keyword">const</span> <a class="code" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>&amp; )   { <span class="keywordflow">return</span> _compactMode; }</div>
<div class="line"><a name="l02329"></a><span class="lineno"> 2329</span>&#160; </div>
<div class="line"><a name="l02333"></a><span class="lineno"><a class="line" href="classtinyxml2_1_1_x_m_l_printer.html#a01148e2ebe6776e38c5a3e41bc5feb74"> 2333</a></span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classtinyxml2_1_1_x_m_l_printer.html#a01148e2ebe6776e38c5a3e41bc5feb74">PrintSpace</a>( <span class="keywordtype">int</span> depth );</div>
<div class="line"><a name="l02334"></a><span class="lineno"> 2334</span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">void</span> Print( <span class="keyword">const</span> <span class="keywordtype">char</span>* format, ... );</div>
<div class="line"><a name="l02335"></a><span class="lineno"> 2335</span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">void</span> Write( <span class="keyword">const</span> <span class="keywordtype">char</span>* data, <span class="keywordtype">size_t</span> size );</div>
<div class="line"><a name="l02336"></a><span class="lineno"> 2336</span>&#160;    <span class="keyword">virtual</span> <span class="keywordtype">void</span> Putc( <span class="keywordtype">char</span> ch );</div>
<div class="line"><a name="l02337"></a><span class="lineno"> 2337</span>&#160; </div>
<div class="line"><a name="l02338"></a><span class="lineno"> 2338</span>&#160;    <span class="keyword">inline</span> <span class="keywordtype">void</span> Write(<span class="keyword">const</span> <span class="keywordtype">char</span>* data) { Write(data, strlen(data)); }</div>
<div class="line"><a name="l02339"></a><span class="lineno"> 2339</span>&#160; </div>
<div class="line"><a name="l02340"></a><span class="lineno"> 2340</span>&#160;    <span class="keywordtype">void</span> SealElementIfJustOpened();</div>
<div class="line"><a name="l02341"></a><span class="lineno"> 2341</span>&#160;    <span class="keywordtype">bool</span> _elementJustOpened;</div>
<div class="line"><a name="l02342"></a><span class="lineno"> 2342</span>&#160;    DynArray&lt; const char*, 10 &gt; _stack;</div>
<div class="line"><a name="l02343"></a><span class="lineno"> 2343</span>&#160; </div>
<div class="line"><a name="l02344"></a><span class="lineno"> 2344</span>&#160;<span class="keyword">private</span>:</div>
<div class="line"><a name="l02349"></a><span class="lineno"> 2349</span>&#160;    <span class="keywordtype">void</span> PrepareForNewNode( <span class="keywordtype">bool</span> compactMode );</div>
<div class="line"><a name="l02350"></a><span class="lineno"> 2350</span>&#160;    <span class="keywordtype">void</span> PrintString( <span class="keyword">const</span> <span class="keywordtype">char</span>*, <span class="keywordtype">bool</span> restrictedEntitySet );  <span class="comment">// prints out, after detecting entities.</span></div>
<div class="line"><a name="l02351"></a><span class="lineno"> 2351</span>&#160; </div>
<div class="line"><a name="l02352"></a><span class="lineno"> 2352</span>&#160;    <span class="keywordtype">bool</span> _firstElement;</div>
<div class="line"><a name="l02353"></a><span class="lineno"> 2353</span>&#160;    FILE* _fp;</div>
<div class="line"><a name="l02354"></a><span class="lineno"> 2354</span>&#160;    <span class="keywordtype">int</span> _depth;</div>
<div class="line"><a name="l02355"></a><span class="lineno"> 2355</span>&#160;    <span class="keywordtype">int</span> _textDepth;</div>
<div class="line"><a name="l02356"></a><span class="lineno"> 2356</span>&#160;    <span class="keywordtype">bool</span> _processEntities;</div>
<div class="line"><a name="l02357"></a><span class="lineno"> 2357</span>&#160;    <span class="keywordtype">bool</span> _compactMode;</div>
<div class="line"><a name="l02358"></a><span class="lineno"> 2358</span>&#160; </div>
<div class="line"><a name="l02359"></a><span class="lineno"> 2359</span>&#160;    <span class="keyword">enum</span> {</div>
<div class="line"><a name="l02360"></a><span class="lineno"> 2360</span>&#160;        ENTITY_RANGE = 64,</div>
<div class="line"><a name="l02361"></a><span class="lineno"> 2361</span>&#160;        BUF_SIZE = 200</div>
<div class="line"><a name="l02362"></a><span class="lineno"> 2362</span>&#160;    };</div>
<div class="line"><a name="l02363"></a><span class="lineno"> 2363</span>&#160;    <span class="keywordtype">bool</span> _entityFlag[ENTITY_RANGE];</div>
<div class="line"><a name="l02364"></a><span class="lineno"> 2364</span>&#160;    <span class="keywordtype">bool</span> _restrictedEntityFlag[ENTITY_RANGE];</div>
<div class="line"><a name="l02365"></a><span class="lineno"> 2365</span>&#160; </div>
<div class="line"><a name="l02366"></a><span class="lineno"> 2366</span>&#160;    DynArray&lt; char, 20 &gt; _buffer;</div>
<div class="line"><a name="l02367"></a><span class="lineno"> 2367</span>&#160; </div>
<div class="line"><a name="l02368"></a><span class="lineno"> 2368</span>&#160;    <span class="comment">// Prohibit cloning, intentionally not implemented</span></div>
<div class="line"><a name="l02369"></a><span class="lineno"> 2369</span>&#160;    XMLPrinter( <span class="keyword">const</span> XMLPrinter&amp; );</div>
<div class="line"><a name="l02370"></a><span class="lineno"> 2370</span>&#160;    XMLPrinter&amp; operator=( <span class="keyword">const</span> XMLPrinter&amp; );</div>
<div class="line"><a name="l02371"></a><span class="lineno"> 2371</span>&#160;};</div>
<div class="line"><a name="l02372"></a><span class="lineno"> 2372</span>&#160; </div>
<div class="line"><a name="l02373"></a><span class="lineno"> 2373</span>&#160; </div>
<div class="line"><a name="l02374"></a><span class="lineno"> 2374</span>&#160;}   <span class="comment">// tinyxml2</span></div>
<div class="line"><a name="l02375"></a><span class="lineno"> 2375</span>&#160; </div>
<div class="line"><a name="l02376"></a><span class="lineno"> 2376</span>&#160;<span class="preprocessor">#if defined(_MSC_VER)</span></div>
<div class="line"><a name="l02377"></a><span class="lineno"> 2377</span>&#160;<span class="preprocessor">#   pragma warning(pop)</span></div>
<div class="line"><a name="l02378"></a><span class="lineno"> 2378</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l02379"></a><span class="lineno"> 2379</span>&#160; </div>
<div class="line"><a name="l02380"></a><span class="lineno"> 2380</span>&#160;<span class="preprocessor">#endif </span><span class="comment">// TINYXML2_INCLUDED</span></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1141</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_a02d5ea924586e35f9c13857d1671b765"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#a02d5ea924586e35f9c13857d1671b765">tinyxml2::XMLAttribute::GetLineNum</a></div><div class="ttdeci">int GetLineNum() const</div><div class="ttdoc">Gets the line number the attribute is in, if the document was parsed from a file.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1151</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_a049dea6449a6259b6cfed44a9427b607"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#a049dea6449a6259b6cfed44a9427b607">tinyxml2::XMLAttribute::QueryFloatValue</a></div><div class="ttdeci">XMLError QueryFloatValue(float *value) const</div><div class="ttdoc">See QueryIntValue.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_a0be5343b08a957c42c02c5d32c35d338"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#a0be5343b08a957c42c02c5d32c35d338">tinyxml2::XMLAttribute::UnsignedValue</a></div><div class="ttdeci">unsigned UnsignedValue() const</div><div class="ttdoc">Query as an unsigned integer. See IntValue()</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1181</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_a10964060a5c0d92486ecf8705bdf37da"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#a10964060a5c0d92486ecf8705bdf37da">tinyxml2::XMLAttribute::SetAttribute</a></div><div class="ttdeci">void SetAttribute(uint64_t value)</div><div class="ttdoc">Set the attribute to value.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_a1aab1dd0e43ecbcfa306adbcf3a3d853"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#a1aab1dd0e43ecbcfa306adbcf3a3d853">tinyxml2::XMLAttribute::Value</a></div><div class="ttdeci">const char * Value() const</div><div class="ttdoc">The value of the attribute.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_a27797b45d21c981257720db94f5f8801"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#a27797b45d21c981257720db94f5f8801">tinyxml2::XMLAttribute::FloatValue</a></div><div class="ttdeci">float FloatValue() const</div><div class="ttdoc">Query as a float. See IntValue()</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1199</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_a2aa6e55e8ea03af0609cf6690bff79b9"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#a2aa6e55e8ea03af0609cf6690bff79b9">tinyxml2::XMLAttribute::QueryDoubleValue</a></div><div class="ttdeci">XMLError QueryDoubleValue(double *value) const</div><div class="ttdoc">See QueryIntValue.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_a406d2c4a13c7af99a65edb59dd9f7581"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#a406d2c4a13c7af99a65edb59dd9f7581">tinyxml2::XMLAttribute::SetAttribute</a></div><div class="ttdeci">void SetAttribute(const char *value)</div><div class="ttdoc">Set the attribute to a string value.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_a48a7f3496f1415832e451bd8d09c9cb9"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#a48a7f3496f1415832e451bd8d09c9cb9">tinyxml2::XMLAttribute::QueryUnsignedValue</a></div><div class="ttdeci">XMLError QueryUnsignedValue(unsigned int *value) const</div><div class="ttdoc">See QueryIntValue.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_a4aa73513f54ff0087d3e804f0f54e30f"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#a4aa73513f54ff0087d3e804f0f54e30f">tinyxml2::XMLAttribute::DoubleValue</a></div><div class="ttdeci">double DoubleValue() const</div><div class="ttdoc">Query as a double. See IntValue()</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1193</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_a4e25344d6e4159026be34dbddf1dcac2"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#a4e25344d6e4159026be34dbddf1dcac2">tinyxml2::XMLAttribute::QueryInt64Value</a></div><div class="ttdeci">XMLError QueryInt64Value(int64_t *value) const</div><div class="ttdoc">See QueryIntValue.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_a5f32e038954256f61c21ff20fd13a09c"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#a5f32e038954256f61c21ff20fd13a09c">tinyxml2::XMLAttribute::QueryBoolValue</a></div><div class="ttdeci">XMLError QueryBoolValue(bool *value) const</div><div class="ttdoc">See QueryIntValue.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_a6d5176260db00ea301c01af8457cd993"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#a6d5176260db00ea301c01af8457cd993">tinyxml2::XMLAttribute::QueryIntValue</a></div><div class="ttdeci">XMLError QueryIntValue(int *value) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_a7c1240f479722b9aa29b6c030aa116c2"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#a7c1240f479722b9aa29b6c030aa116c2">tinyxml2::XMLAttribute::SetAttribute</a></div><div class="ttdeci">void SetAttribute(int64_t value)</div><div class="ttdoc">Set the attribute to value.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_a98ce5207344ad33a265b0422addae1ff"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#a98ce5207344ad33a265b0422addae1ff">tinyxml2::XMLAttribute::BoolValue</a></div><div class="ttdeci">bool BoolValue() const</div><div class="ttdoc">Query as a boolean. See IntValue()</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1187</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_a9a65ab3147abe8ccbbd373ce8791e818"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#a9a65ab3147abe8ccbbd373ce8791e818">tinyxml2::XMLAttribute::SetAttribute</a></div><div class="ttdeci">void SetAttribute(double value)</div><div class="ttdoc">Set the attribute to value.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_ab3516def4fe058fe328f2b89fc2d77da"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#ab3516def4fe058fe328f2b89fc2d77da">tinyxml2::XMLAttribute::SetAttribute</a></div><div class="ttdeci">void SetAttribute(bool value)</div><div class="ttdoc">Set the attribute to value.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_ab886c486ec19f02ed826f8dc129e5ad8"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#ab886c486ec19f02ed826f8dc129e5ad8">tinyxml2::XMLAttribute::Name</a></div><div class="ttdeci">const char * Name() const</div><div class="ttdoc">The name of the attribute.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_ad86d7d7058d76761c3a80662566a57e5"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#ad86d7d7058d76761c3a80662566a57e5">tinyxml2::XMLAttribute::SetAttribute</a></div><div class="ttdeci">void SetAttribute(int value)</div><div class="ttdoc">Set the attribute to value.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_adfa2433f0fdafd5c3880936de9affa80"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#adfa2433f0fdafd5c3880936de9affa80">tinyxml2::XMLAttribute::IntValue</a></div><div class="ttdeci">int IntValue() const</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1162</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_ae70468c0f6df2748ba3529c716999fae"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#ae70468c0f6df2748ba3529c716999fae">tinyxml2::XMLAttribute::SetAttribute</a></div><div class="ttdeci">void SetAttribute(unsigned value)</div><div class="ttdoc">Set the attribute to value.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_ae95e843313aaf5d56c32530b6456df02"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#ae95e843313aaf5d56c32530b6456df02">tinyxml2::XMLAttribute::SetAttribute</a></div><div class="ttdeci">void SetAttribute(float value)</div><div class="ttdoc">Set the attribute to value.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_aee53571b21e7ce5421eb929523a8bbe6"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#aee53571b21e7ce5421eb929523a8bbe6">tinyxml2::XMLAttribute::Next</a></div><div class="ttdeci">const XMLAttribute * Next() const</div><div class="ttdoc">The next attribute in the list.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1154</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_attribute_html_af793c695e7ee65cf20b8010d38b1d157"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_attribute.html#af793c695e7ee65cf20b8010d38b1d157">tinyxml2::XMLAttribute::QueryUnsigned64Value</a></div><div class="ttdeci">XMLError QueryUnsigned64Value(uint64_t *value) const</div><div class="ttdoc">See QueryIntValue.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_comment_html"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_comment.html">tinyxml2::XMLComment</a></div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1032</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_comment_html_a08991cc63fadf7e95078ac4f9ea1b073"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_comment.html#a08991cc63fadf7e95078ac4f9ea1b073">tinyxml2::XMLComment::ShallowClone</a></div><div class="ttdeci">virtual XMLNode * ShallowClone(XMLDocument *document) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_comment_html_a4a33dc32fae0285b03f9cfcb3e43e122"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_comment.html#a4a33dc32fae0285b03f9cfcb3e43e122">tinyxml2::XMLComment::Accept</a></div><div class="ttdeci">virtual bool Accept(XMLVisitor *visitor) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_comment_html_a6f7d227b25afa8cc3c763b7cc8833739"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_comment.html#a6f7d227b25afa8cc3c763b7cc8833739">tinyxml2::XMLComment::ShallowEqual</a></div><div class="ttdeci">virtual bool ShallowEqual(const XMLNode *compare) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_comment_html_a8093e1dc8a34fa446d9dc3fde0e6c0ee"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_comment.html#a8093e1dc8a34fa446d9dc3fde0e6c0ee">tinyxml2::XMLComment::ToComment</a></div><div class="ttdeci">virtual XMLComment * ToComment()</div><div class="ttdoc">Safely cast to a Comment, or null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1035</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_const_handle_html"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_const_handle.html">tinyxml2::XMLConstHandle</a></div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2134</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_declaration_html"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_declaration.html">tinyxml2::XMLDeclaration</a></div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1071</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_declaration_html_a118d47518dd9e522644e42efa259aed7"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_declaration.html#a118d47518dd9e522644e42efa259aed7">tinyxml2::XMLDeclaration::ShallowClone</a></div><div class="ttdeci">virtual XMLNode * ShallowClone(XMLDocument *document) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_declaration_html_a159d8ac45865215e88059ea1e5b52fc5"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_declaration.html#a159d8ac45865215e88059ea1e5b52fc5">tinyxml2::XMLDeclaration::ToDeclaration</a></div><div class="ttdeci">virtual XMLDeclaration * ToDeclaration()</div><div class="ttdoc">Safely cast to a Declaration, or null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1074</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_declaration_html_a5f376019fb34752eb248548f42f32045"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_declaration.html#a5f376019fb34752eb248548f42f32045">tinyxml2::XMLDeclaration::Accept</a></div><div class="ttdeci">virtual bool Accept(XMLVisitor *visitor) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_declaration_html_aa26b70011694e9b9e9480b929e9b78d6"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_declaration.html#aa26b70011694e9b9e9480b929e9b78d6">tinyxml2::XMLDeclaration::ShallowEqual</a></div><div class="ttdeci">virtual bool ShallowEqual(const XMLNode *compare) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1717</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a14419b698f7c4b140df4e80f3f0c93b0"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a14419b698f7c4b140df4e80f3f0c93b0">tinyxml2::XMLDocument::SetBOM</a></div><div class="ttdeci">void SetBOM(bool useBOM)</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1803</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a1d033945b42e125d933d6231e4571552"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a1d033945b42e125d933d6231e4571552">tinyxml2::XMLDocument::PrintError</a></div><div class="ttdeci">void PrintError() const</div><div class="ttdoc">A (trivial) utility function that prints the ErrorStr() to stdout.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a2ebd4647a8af5fc6831b294ac26a150a"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a2ebd4647a8af5fc6831b294ac26a150a">tinyxml2::XMLDocument::LoadFile</a></div><div class="ttdeci">XMLError LoadFile(const char *filename)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a33fc5d159db873a179fa26338adb05bd"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a33fc5d159db873a179fa26338adb05bd">tinyxml2::XMLDocument::HasBOM</a></div><div class="ttdeci">bool HasBOM() const</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1798</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a34e6318e182e40e3cc4f4ba5d59ed9ed"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a34e6318e182e40e3cc4f4ba5d59ed9ed">tinyxml2::XMLDocument::Error</a></div><div class="ttdeci">bool Error() const</div><div class="ttdoc">Return true if there was an error parsing the document.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1881</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a3e185f880882bd978367bb55937735ec"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a3e185f880882bd978367bb55937735ec">tinyxml2::XMLDocument::ToDocument</a></div><div class="ttdeci">virtual XMLDocument * ToDocument()</div><div class="ttdoc">Safely cast to a Document, or null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1731</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a4085d9c52f1d93214311459d6d1fcf17"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a4085d9c52f1d93214311459d6d1fcf17">tinyxml2::XMLDocument::ClearError</a></div><div class="ttdeci">void ClearError()</div><div class="ttdoc">Clears the error flags.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a5385c937734ff6db9226ab707d2c7147"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a5385c937734ff6db9226ab707d2c7147">tinyxml2::XMLDocument::NewUnknown</a></div><div class="ttdeci">XMLUnknown * NewUnknown(const char *text)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a57400f816dbe7799ece33615ead9ab76"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a57400f816dbe7799ece33615ead9ab76">tinyxml2::XMLDocument::ErrorLineNum</a></div><div class="ttdeci">int ErrorLineNum() const</div><div class="ttdoc">Return the line where the error occurred, or zero if unknown.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1900</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a57ddf17b6e054dda10af98991b1b8f70"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a57ddf17b6e054dda10af98991b1b8f70">tinyxml2::XMLDocument::XMLDocument</a></div><div class="ttdeci">XMLDocument(bool processEntities=true, Whitespace whitespaceMode=PRESERVE_WHITESPACE)</div><div class="ttdoc">constructor</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a5f1d330fad44c52f3d265338dd2a6dc2"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a5f1d330fad44c52f3d265338dd2a6dc2">tinyxml2::XMLDocument::LoadFile</a></div><div class="ttdeci">XMLError LoadFile(FILE *)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a65656b0b2cbc822708eb351504178aaf"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a65656b0b2cbc822708eb351504178aaf">tinyxml2::XMLDocument::Clear</a></div><div class="ttdeci">void Clear()</div><div class="ttdoc">Clear the document, resetting it to the initial state.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a6fe5ef18699091844fcf64b56ffa5bf9"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a6fe5ef18699091844fcf64b56ffa5bf9">tinyxml2::XMLDocument::ShallowEqual</a></div><div class="ttdeci">virtual bool ShallowEqual(const XMLNode *) const</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1926</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a73ac416b4a2aa0952e841220eb3da18f"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a73ac416b4a2aa0952e841220eb3da18f">tinyxml2::XMLDocument::SaveFile</a></div><div class="ttdeci">XMLError SaveFile(const char *filename, bool compact=false)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a867cf5fa3e3ff6ae4847a8b7ee8ec083"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a867cf5fa3e3ff6ae4847a8b7ee8ec083">tinyxml2::XMLDocument::Print</a></div><div class="ttdeci">void Print(XMLPrinter *streamer=0) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a8aa7817d4a1001364b06373763ab99d6"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a8aa7817d4a1001364b06373763ab99d6">tinyxml2::XMLDocument::NewElement</a></div><div class="ttdeci">XMLElement * NewElement(const char *name)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a8b95779479a0035acc67b3a61dfe1b74"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a8b95779479a0035acc67b3a61dfe1b74">tinyxml2::XMLDocument::SaveFile</a></div><div class="ttdeci">XMLError SaveFile(FILE *fp, bool compact=false)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_a9efa54f7ecb37c17ab1fa2b3078ccca1"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#a9efa54f7ecb37c17ab1fa2b3078ccca1">tinyxml2::XMLDocument::Accept</a></div><div class="ttdeci">virtual bool Accept(XMLVisitor *visitor) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_aa37cc1709d7e1e988bc17dcfb24a69b8"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#aa37cc1709d7e1e988bc17dcfb24a69b8">tinyxml2::XMLDocument::ShallowClone</a></div><div class="ttdeci">virtual XMLNode * ShallowClone(XMLDocument *) const</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1923</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_ab7e8b29ae4099092a8bb947da6361296"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#ab7e8b29ae4099092a8bb947da6361296">tinyxml2::XMLDocument::NewText</a></div><div class="ttdeci">XMLText * NewText(const char *text)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_ac1d6e2c7fcc1a660624ac4f68e96380d"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#ac1d6e2c7fcc1a660624ac4f68e96380d">tinyxml2::XMLDocument::DeleteNode</a></div><div class="ttdeci">void DeleteNode(XMLNode *node)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_ad2b70320d3c2a071c2f36928edff3e1c"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#ad2b70320d3c2a071c2f36928edff3e1c">tinyxml2::XMLDocument::RootElement</a></div><div class="ttdeci">XMLElement * RootElement()</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1810</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_ad75aa9d32c4e8b300655186808aa9abf"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#ad75aa9d32c4e8b300655186808aa9abf">tinyxml2::XMLDocument::ErrorStr</a></div><div class="ttdeci">const char * ErrorStr() const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_ade4874bcb439954972ef2b3723ff3259"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#ade4874bcb439954972ef2b3723ff3259">tinyxml2::XMLDocument::NewComment</a></div><div class="ttdeci">XMLComment * NewComment(const char *comment)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_aee2eb3435923f5494dcc70ac225b60a2"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#aee2eb3435923f5494dcc70ac225b60a2">tinyxml2::XMLDocument::NewDeclaration</a></div><div class="ttdeci">XMLDeclaration * NewDeclaration(const char *text=0)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_af2b616169e6517182f6725f2498e9a01"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#af2b616169e6517182f6725f2498e9a01">tinyxml2::XMLDocument::Parse</a></div><div class="ttdeci">XMLError Parse(const char *xml, size_t nBytes=static_cast&lt; size_t &gt;(-1))</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_af592ffc91514e25a39664521ac83db45"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#af592ffc91514e25a39664521ac83db45">tinyxml2::XMLDocument::DeepCopy</a></div><div class="ttdeci">void DeepCopy(XMLDocument *target) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_document_html_afa3ed33b3107f920ec2b301f805ac17d"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_document.html#afa3ed33b3107f920ec2b301f805ac17d">tinyxml2::XMLDocument::ErrorID</a></div><div class="ttdeci">XMLError ErrorID() const</div><div class="ttdoc">Return the errorID.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1885</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1265</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a10a90c505aea716bf073eea1c97f33b5"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a10a90c505aea716bf073eea1c97f33b5">tinyxml2::XMLElement::DoubleAttribute</a></div><div class="ttdeci">double DoubleAttribute(const char *name, double defaultValue=0) const</div><div class="ttdoc">See IntAttribute()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a11943abf2d0831548c3790dd5d9f119c"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a11943abf2d0831548c3790dd5d9f119c">tinyxml2::XMLElement::SetAttribute</a></div><div class="ttdeci">void SetAttribute(const char *name, const char *value)</div><div class="ttdoc">Sets the named attribute to value.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1465</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a120c538c8eead169e635dbc70fb226d8"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a120c538c8eead169e635dbc70fb226d8">tinyxml2::XMLElement::QueryInt64Text</a></div><div class="ttdeci">XMLError QueryInt64Text(int64_t *uval) const</div><div class="ttdoc">See QueryIntText()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a13dd590b5d3958ce2ed79844aacd9405"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a13dd590b5d3958ce2ed79844aacd9405">tinyxml2::XMLElement::QueryUnsigned64Attribute</a></div><div class="ttdeci">XMLError QueryUnsigned64Attribute(const char *name, uint64_t *value) const</div><div class="ttdoc">See QueryIntAttribute()</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1370</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a14c1bb77c39689838be01838d86ca872"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a14c1bb77c39689838be01838d86ca872">tinyxml2::XMLElement::QueryBoolAttribute</a></div><div class="ttdeci">XMLError QueryBoolAttribute(const char *name, bool *value) const</div><div class="ttdoc">See QueryIntAttribute()</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1379</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a14d38aa4b5e18a46274a27425188a6a1"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a14d38aa4b5e18a46274a27425188a6a1">tinyxml2::XMLElement::QueryUnsignedText</a></div><div class="ttdeci">XMLError QueryUnsignedText(unsigned *uval) const</div><div class="ttdoc">See QueryIntText()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a189e155810fc9fdd4da1409cbadee187"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a189e155810fc9fdd4da1409cbadee187">tinyxml2::XMLElement::InsertNewText</a></div><div class="ttdeci">XMLText * InsertNewText(const char *text)</div><div class="ttdoc">See InsertNewChildElement()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a1f9c2cd61b72af5ae708d37b7ad283ce"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a1f9c2cd61b72af5ae708d37b7ad283ce">tinyxml2::XMLElement::SetText</a></div><div class="ttdeci">void SetText(const char *inText)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a226502bab8f1be7ede1fdd255398eb85"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a226502bab8f1be7ede1fdd255398eb85">tinyxml2::XMLElement::Unsigned64Attribute</a></div><div class="ttdeci">uint64_t Unsigned64Attribute(const char *name, uint64_t defaultValue=0) const</div><div class="ttdoc">See IntAttribute()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a233397ee81e70eb5d4b814c5f8698533"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a233397ee81e70eb5d4b814c5f8698533">tinyxml2::XMLElement::SetAttribute</a></div><div class="ttdeci">void SetAttribute(const char *name, double value)</div><div class="ttdoc">Sets the named attribute to value.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1498</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a26fc84cbfba6769dafcfbf256c05e22f"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a26fc84cbfba6769dafcfbf256c05e22f">tinyxml2::XMLElement::QueryUnsignedAttribute</a></div><div class="ttdeci">XMLError QueryUnsignedAttribute(const char *name, unsigned int *value) const</div><div class="ttdoc">See QueryIntAttribute()</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1352</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a2dcd4d5d6fb63396cd2f257c318b42c4"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a2dcd4d5d6fb63396cd2f257c318b42c4">tinyxml2::XMLElement::FindAttribute</a></div><div class="ttdeci">const XMLAttribute * FindAttribute(const char *name) const</div><div class="ttdoc">Query a specific attribute in the list.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a3e191704c8d499906ec11fe2f60c6686"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a3e191704c8d499906ec11fe2f60c6686">tinyxml2::XMLElement::FirstAttribute</a></div><div class="ttdeci">const XMLAttribute * FirstAttribute() const</div><div class="ttdoc">Return the first attribute in the list.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1514</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a3ea8a40e788fb9ad876c28a32932c6d5"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a3ea8a40e788fb9ad876c28a32932c6d5">tinyxml2::XMLElement::Accept</a></div><div class="ttdeci">virtual bool Accept(XMLVisitor *visitor) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a3fe5417d59eb8f5c4afe924b7d332736"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a3fe5417d59eb8f5c4afe924b7d332736">tinyxml2::XMLElement::QueryBoolText</a></div><div class="ttdeci">XMLError QueryBoolText(bool *bval) const</div><div class="ttdoc">See QueryIntText()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a45444eb21f99ca46101545992dc2e927"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a45444eb21f99ca46101545992dc2e927">tinyxml2::XMLElement::FloatText</a></div><div class="ttdeci">float FloatText(float defaultValue=0) const</div><div class="ttdoc">See QueryIntText()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a49bad014ffcc17b0b6119d5b2c97dfb5"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a49bad014ffcc17b0b6119d5b2c97dfb5">tinyxml2::XMLElement::UnsignedText</a></div><div class="ttdeci">unsigned UnsignedText(unsigned defaultValue=0) const</div><div class="ttdoc">See QueryIntText()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a51d560da5ae3ad6b75e0ab9ffb2ae42a"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a51d560da5ae3ad6b75e0ab9ffb2ae42a">tinyxml2::XMLElement::SetText</a></div><div class="ttdeci">void SetText(float value)</div><div class="ttdoc">Convenience method for setting text inside an element. See SetText() for important limitations.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a53eda26131e1ad1031ef8ec8adb51bd8"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a53eda26131e1ad1031ef8ec8adb51bd8">tinyxml2::XMLElement::BoolAttribute</a></div><div class="ttdeci">bool BoolAttribute(const char *name, bool defaultValue=false) const</div><div class="ttdoc">See IntAttribute()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a554b70d882e65b28fc084b23df9b9759"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a554b70d882e65b28fc084b23df9b9759">tinyxml2::XMLElement::SetAttribute</a></div><div class="ttdeci">void SetAttribute(const char *name, float value)</div><div class="ttdoc">Sets the named attribute to value.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1503</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a5b7df3bed2b8954eabf227fa204522eb"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a5b7df3bed2b8954eabf227fa204522eb">tinyxml2::XMLElement::QueryAttribute</a></div><div class="ttdeci">XMLError QueryAttribute(const char *name, int *value) const</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1432</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a5f0964e2dbd8e2ee7fce9beab689443c"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a5f0964e2dbd8e2ee7fce9beab689443c">tinyxml2::XMLElement::QueryDoubleAttribute</a></div><div class="ttdeci">XMLError QueryDoubleAttribute(const char *name, double *value) const</div><div class="ttdoc">See QueryIntAttribute()</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1387</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a63e057fb5baee1dd29f323cb85907b35"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a63e057fb5baee1dd29f323cb85907b35">tinyxml2::XMLElement::Name</a></div><div class="ttdeci">const char * Name() const</div><div class="ttdoc">Get the name of an element (which is the Value() of the node.)</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1269</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a66d96972adecd816194191f13cc4a0a0"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a66d96972adecd816194191f13cc4a0a0">tinyxml2::XMLElement::Int64Attribute</a></div><div class="ttdeci">int64_t Int64Attribute(const char *name, int64_t defaultValue=0) const</div><div class="ttdoc">See IntAttribute()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a67bd77ac9aaeff58ff20b4275a65ba4e"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a67bd77ac9aaeff58ff20b4275a65ba4e">tinyxml2::XMLElement::SetText</a></div><div class="ttdeci">void SetText(double value)</div><div class="ttdoc">Convenience method for setting text inside an element. See SetText() for important limitations.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a684679c99bb036a25652744cec6c4d96"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a684679c99bb036a25652744cec6c4d96">tinyxml2::XMLElement::QueryDoubleText</a></div><div class="ttdeci">XMLError QueryDoubleText(double *dval) const</div><div class="ttdoc">See QueryIntText()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a68569f59f6382bcea7f5013ec59736d2"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a68569f59f6382bcea7f5013ec59736d2">tinyxml2::XMLElement::BoolText</a></div><div class="ttdeci">bool BoolText(bool defaultValue=false) const</div><div class="ttdoc">See QueryIntText()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a6d5c8d115561ade4e4456b71d91b6f51"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a6d5c8d115561ade4e4456b71d91b6f51">tinyxml2::XMLElement::GetText</a></div><div class="ttdeci">const char * GetText() const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a6e615bc745afd1ca8ded56d7aac02657"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a6e615bc745afd1ca8ded56d7aac02657">tinyxml2::XMLElement::SetText</a></div><div class="ttdeci">void SetText(uint64_t value)</div><div class="ttdoc">Convenience method for setting text inside an element. See SetText() for important limitations.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a70e49ed60b11212ae35f7e354cfe1de9"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a70e49ed60b11212ae35f7e354cfe1de9">tinyxml2::XMLElement::Attribute</a></div><div class="ttdeci">const char * Attribute(const char *name, const char *value=0) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a7b62cd33acdfeff7ea2b1b330d4368e4"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a7b62cd33acdfeff7ea2b1b330d4368e4">tinyxml2::XMLElement::SetText</a></div><div class="ttdeci">void SetText(int64_t value)</div><div class="ttdoc">Convenience method for setting text inside an element. See SetText() for important limitations.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a7bbfcc11d516598bc924a8fba4d08597"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a7bbfcc11d516598bc924a8fba4d08597">tinyxml2::XMLElement::SetText</a></div><div class="ttdeci">void SetText(unsigned value)</div><div class="ttdoc">Convenience method for setting text inside an element. See SetText() for important limitations.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a7c0955d80b6f8d196744eacb0f6e90a8"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a7c0955d80b6f8d196744eacb0f6e90a8">tinyxml2::XMLElement::QueryInt64Attribute</a></div><div class="ttdeci">XMLError QueryInt64Attribute(const char *name, int64_t *value) const</div><div class="ttdoc">See QueryIntAttribute()</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1361</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a81b1ff0cf2f2cd09be8badc08b39a2b7"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a81b1ff0cf2f2cd09be8badc08b39a2b7">tinyxml2::XMLElement::DoubleText</a></div><div class="ttdeci">double DoubleText(double defaultValue=0) const</div><div class="ttdoc">See QueryIntText()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a8a78bc1187c1c45ad89f2690eab567b1"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a8a78bc1187c1c45ad89f2690eab567b1">tinyxml2::XMLElement::QueryIntAttribute</a></div><div class="ttdeci">XMLError QueryIntAttribute(const char *name, int *value) const</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1343</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a926357996bef633cb736e1a558419632"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">tinyxml2::XMLElement::QueryIntText</a></div><div class="ttdeci">XMLError QueryIntText(int *ival) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a95a89b13bb14a2d4655e2b5b406c00d4"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a95a89b13bb14a2d4655e2b5b406c00d4">tinyxml2::XMLElement::IntAttribute</a></div><div class="ttdeci">int IntAttribute(const char *name, int defaultValue=0) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_a97712009a530d8cb8a63bf705f02b4f1"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#a97712009a530d8cb8a63bf705f02b4f1">tinyxml2::XMLElement::SetName</a></div><div class="ttdeci">void SetName(const char *str, bool staticMem=false)</div><div class="ttdoc">Set the name of the element.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1273</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_aa848b696e6a75e4e545c6da9893b11e1"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#aa848b696e6a75e4e545c6da9893b11e1">tinyxml2::XMLElement::SetAttribute</a></div><div class="ttdeci">void SetAttribute(const char *name, bool value)</div><div class="ttdoc">Sets the named attribute to value.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1493</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_aab6151f7e3b4c2c0a8234e262d7b6b8a"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#aab6151f7e3b4c2c0a8234e262d7b6b8a">tinyxml2::XMLElement::Int64Text</a></div><div class="ttdeci">int64_t Int64Text(int64_t defaultValue=0) const</div><div class="ttdoc">See QueryIntText()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_aae6568c64c7f1cc88be8461ba41a79cf"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#aae6568c64c7f1cc88be8461ba41a79cf">tinyxml2::XMLElement::SetAttribute</a></div><div class="ttdeci">void SetAttribute(const char *name, int value)</div><div class="ttdoc">Sets the named attribute to value.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1470</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_aaeefdf9171fec91b13a776b42299b0dd"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#aaeefdf9171fec91b13a776b42299b0dd">tinyxml2::XMLElement::SetAttribute</a></div><div class="ttdeci">void SetAttribute(const char *name, int64_t value)</div><div class="ttdoc">Sets the named attribute to value.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1481</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_ab1f4be2332e27dc640e9b6abd01d64dd"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#ab1f4be2332e27dc640e9b6abd01d64dd">tinyxml2::XMLElement::FloatAttribute</a></div><div class="ttdeci">float FloatAttribute(const char *name, float defaultValue=0) const</div><div class="ttdoc">See IntAttribute()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_abc9506eff9780f666f49dc3d5e5cae13"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#abc9506eff9780f666f49dc3d5e5cae13">tinyxml2::XMLElement::InsertNewChildElement</a></div><div class="ttdeci">XMLElement * InsertNewChildElement(const char *name)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_ac035742d68b0c50c3f676374e59fe750"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#ac035742d68b0c50c3f676374e59fe750">tinyxml2::XMLElement::ShallowClone</a></div><div class="ttdeci">virtual XMLNode * ShallowClone(XMLDocument *document) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_ac2239b3bd172ad8f5b78d04d4236144b"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#ac2239b3bd172ad8f5b78d04d4236144b">tinyxml2::XMLElement::QueryUnsigned64Text</a></div><div class="ttdeci">XMLError QueryUnsigned64Text(uint64_t *uval) const</div><div class="ttdoc">See QueryIntText()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_acaa5fe3957760e68185006965e2c11c2"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#acaa5fe3957760e68185006965e2c11c2">tinyxml2::XMLElement::InsertNewUnknown</a></div><div class="ttdeci">XMLUnknown * InsertNewUnknown(const char *text)</div><div class="ttdoc">See InsertNewChildElement()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_acd5eeddf6002ef90806af794b9d9a5a5"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#acd5eeddf6002ef90806af794b9d9a5a5">tinyxml2::XMLElement::QueryFloatAttribute</a></div><div class="ttdeci">XMLError QueryFloatAttribute(const char *name, float *value) const</div><div class="ttdoc">See QueryIntAttribute()</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1395</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_ad598868c0599ddc4695dab18552c308d"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#ad598868c0599ddc4695dab18552c308d">tinyxml2::XMLElement::SetAttribute</a></div><div class="ttdeci">void SetAttribute(const char *name, uint64_t value)</div><div class="ttdoc">Sets the named attribute to value.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1487</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_ad9ea913a460b48979bd83cf9871c99f6"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#ad9ea913a460b48979bd83cf9871c99f6">tinyxml2::XMLElement::ShallowEqual</a></div><div class="ttdeci">virtual bool ShallowEqual(const XMLNode *compare) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_ad9ff5c2dbc15df36cf664ce1b0ea0a5d"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#ad9ff5c2dbc15df36cf664ce1b0ea0a5d">tinyxml2::XMLElement::ToElement</a></div><div class="ttdeci">virtual XMLElement * ToElement()</div><div class="ttdoc">Safely cast to an Element, or null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1277</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_adb8ae765f98d0c5037faec48deea78bc"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#adb8ae765f98d0c5037faec48deea78bc">tinyxml2::XMLElement::QueryStringAttribute</a></div><div class="ttdeci">XMLError QueryStringAttribute(const char *name, const char **value) const</div><div class="ttdoc">See QueryIntAttribute()</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1404</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_adec237e788b50c4ed73c918a166adde6"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#adec237e788b50c4ed73c918a166adde6">tinyxml2::XMLElement::InsertNewDeclaration</a></div><div class="ttdeci">XMLDeclaration * InsertNewDeclaration(const char *text)</div><div class="ttdoc">See InsertNewChildElement()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_ae143997e90064ba82326b29a9930ea8f"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#ae143997e90064ba82326b29a9930ea8f">tinyxml2::XMLElement::SetAttribute</a></div><div class="ttdeci">void SetAttribute(const char *name, unsigned value)</div><div class="ttdoc">Sets the named attribute to value.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1475</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_ae4b543d6770de76fb6ab68e541c192a4"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#ae4b543d6770de76fb6ab68e541c192a4">tinyxml2::XMLElement::SetText</a></div><div class="ttdeci">void SetText(bool value)</div><div class="ttdoc">Convenience method for setting text inside an element. See SetText() for important limitations.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_ae4f2c2e781b8dc030411d84cd20fa46d"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#ae4f2c2e781b8dc030411d84cd20fa46d">tinyxml2::XMLElement::InsertNewComment</a></div><div class="ttdeci">XMLComment * InsertNewComment(const char *comment)</div><div class="ttdoc">See InsertNewChildElement()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_aeae8917b5ea6060b3c08d4e3d8d632d7"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#aeae8917b5ea6060b3c08d4e3d8d632d7">tinyxml2::XMLElement::SetText</a></div><div class="ttdeci">void SetText(int value)</div><div class="ttdoc">Convenience method for setting text inside an element. See SetText() for important limitations.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_aebd45aa7118964c30b32fe12e944628a"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#aebd45aa7118964c30b32fe12e944628a">tinyxml2::XMLElement::DeleteAttribute</a></div><div class="ttdeci">void DeleteAttribute(const char *name)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_af48c1023abbac1acdf4927c51c3a5f0c"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#af48c1023abbac1acdf4927c51c3a5f0c">tinyxml2::XMLElement::Unsigned64Text</a></div><div class="ttdeci">uint64_t Unsigned64Text(uint64_t defaultValue=0) const</div><div class="ttdoc">See QueryIntText()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_afa332afedd93210daa6d44b88eb11e29"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#afa332afedd93210daa6d44b88eb11e29">tinyxml2::XMLElement::QueryFloatText</a></div><div class="ttdeci">XMLError QueryFloatText(float *fval) const</div><div class="ttdoc">See QueryIntText()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_element_html_afea43a1d4aa33e3703ddee5fc9adc26c"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_element.html#afea43a1d4aa33e3703ddee5fc9adc26c">tinyxml2::XMLElement::UnsignedAttribute</a></div><div class="ttdeci">unsigned UnsignedAttribute(const char *name, unsigned defaultValue=0) const</div><div class="ttdoc">See IntAttribute()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2053</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_a03ea6ec970a021b71bf1219a0f6717df"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#a03ea6ec970a021b71bf1219a0f6717df">tinyxml2::XMLHandle::ToNode</a></div><div class="ttdeci">XMLNode * ToNode()</div><div class="ttdoc">Safe cast to XMLNode. This can return null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2104</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_a108858be7ee3eb53f73b5194c1aa8ff0"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#a108858be7ee3eb53f73b5194c1aa8ff0">tinyxml2::XMLHandle::ToDeclaration</a></div><div class="ttdeci">XMLDeclaration * ToDeclaration()</div><div class="ttdoc">Safe cast to XMLDeclaration. This can return null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2120</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_a428374e756f4db4cbc287fec64eae02c"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#a428374e756f4db4cbc287fec64eae02c">tinyxml2::XMLHandle::PreviousSibling</a></div><div class="ttdeci">XMLHandle PreviousSibling()</div><div class="ttdoc">Get the previous sibling of this handle.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2087</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_a42cccd0ce8b1ce704f431025e9f19e0c"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#a42cccd0ce8b1ce704f431025e9f19e0c">tinyxml2::XMLHandle::LastChildElement</a></div><div class="ttdeci">XMLHandle LastChildElement(const char *name=0)</div><div class="ttdoc">Get the last child element of this handle.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2083</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_a536447dc7f54c0cd11e031dad94795ae"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#a536447dc7f54c0cd11e031dad94795ae">tinyxml2::XMLHandle::FirstChild</a></div><div class="ttdeci">XMLHandle FirstChild()</div><div class="ttdoc">Get the first child of this handle.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2071</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_a5e73ed8f3f6f9619d5a8bb1862c47d99"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#a5e73ed8f3f6f9619d5a8bb1862c47d99">tinyxml2::XMLHandle::ToElement</a></div><div class="ttdeci">XMLElement * ToElement()</div><div class="ttdoc">Safe cast to XMLElement. This can return null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2108</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_a6ab9e8cbfb41417246e5657e3842c62a"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#a6ab9e8cbfb41417246e5657e3842c62a">tinyxml2::XMLHandle::ToText</a></div><div class="ttdeci">XMLText * ToText()</div><div class="ttdoc">Safe cast to XMLText. This can return null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2112</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_a74b04dd0f15e0bf01860e282b840b6a3"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#a74b04dd0f15e0bf01860e282b840b6a3">tinyxml2::XMLHandle::FirstChildElement</a></div><div class="ttdeci">XMLHandle FirstChildElement(const char *name=0)</div><div class="ttdoc">Get the first child element of this handle.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2075</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_a75b908322bb4b83be3281b6845252b20"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#a75b908322bb4b83be3281b6845252b20">tinyxml2::XMLHandle::operator=</a></div><div class="ttdeci">XMLHandle &amp; operator=(const XMLHandle &amp;ref)</div><div class="ttdoc">Assignment.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2065</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_a786957e498039554ed334cdc36612a7e"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#a786957e498039554ed334cdc36612a7e">tinyxml2::XMLHandle::PreviousSiblingElement</a></div><div class="ttdeci">XMLHandle PreviousSiblingElement(const char *name=0)</div><div class="ttdoc">Get the previous sibling element of this handle.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2091</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_a9c240a35c18f053509b4b97ddccd9793"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#a9c240a35c18f053509b4b97ddccd9793">tinyxml2::XMLHandle::XMLHandle</a></div><div class="ttdeci">XMLHandle(XMLNode *node)</div><div class="ttdoc">Create a handle from any node (at any depth of the tree.) This can be a null pointer.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2056</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_a9d09f04435f0f2f7d0816b0198d0517b"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#a9d09f04435f0f2f7d0816b0198d0517b">tinyxml2::XMLHandle::LastChild</a></div><div class="ttdeci">XMLHandle LastChild()</div><div class="ttdoc">Get the last child of this handle.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2079</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_aa2edbc1c0d3e3e8259bd98de7f1cf500"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#aa2edbc1c0d3e3e8259bd98de7f1cf500">tinyxml2::XMLHandle::XMLHandle</a></div><div class="ttdeci">XMLHandle(XMLNode &amp;node)</div><div class="ttdoc">Create a handle from a node.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2059</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_aa387368a1ad8d843a9f12df863d298de"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#aa387368a1ad8d843a9f12df863d298de">tinyxml2::XMLHandle::ToUnknown</a></div><div class="ttdeci">XMLUnknown * ToUnknown()</div><div class="ttdoc">Safe cast to XMLUnknown. This can return null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2116</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_aad2eccc7c7c7b18145877c978c3850b5"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#aad2eccc7c7c7b18145877c978c3850b5">tinyxml2::XMLHandle::NextSibling</a></div><div class="ttdeci">XMLHandle NextSibling()</div><div class="ttdoc">Get the next sibling of this handle.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2095</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_ae41d88ee061f3c49a081630ff753b2c5"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#ae41d88ee061f3c49a081630ff753b2c5">tinyxml2::XMLHandle::NextSiblingElement</a></div><div class="ttdeci">XMLHandle NextSiblingElement(const char *name=0)</div><div class="ttdoc">Get the next sibling element of this handle.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2099</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_handle_html_afd8e01e6018c07347b8e6d80272466aa"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_handle.html#afd8e01e6018c07347b8e6d80272466aa">tinyxml2::XMLHandle::XMLHandle</a></div><div class="ttdeci">XMLHandle(const XMLHandle &amp;ref)</div><div class="ttdoc">Copy constructor.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2062</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></div><div class="ttdef"><b>Definition:</b> tinyxml2.h:675</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a002978fc889cc011d143185f2377eca2"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a002978fc889cc011d143185f2377eca2">tinyxml2::XMLNode::SetUserData</a></div><div class="ttdeci">void SetUserData(void *userData)</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:939</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a0360085cc54df5bff85d5c5da13afdce"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a0360085cc54df5bff85d5c5da13afdce">tinyxml2::XMLNode::DeleteChildren</a></div><div class="ttdeci">void DeleteChildren()</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a09dd68cf9eae137579f6e50f36487513"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a09dd68cf9eae137579f6e50f36487513">tinyxml2::XMLNode::SetValue</a></div><div class="ttdeci">void SetValue(const char *val, bool staticMem=false)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a1264c86233328f0cd36297552d982f80"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a1264c86233328f0cd36297552d982f80">tinyxml2::XMLNode::NextSiblingElement</a></div><div class="ttdeci">const XMLElement * NextSiblingElement(const char *name=0) const</div><div class="ttdoc">Get the next (right) sibling element of this node, with an optionally supplied name.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a173e9d1341bc56992e2d320a35936551"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a173e9d1341bc56992e2d320a35936551">tinyxml2::XMLNode::LastChildElement</a></div><div class="ttdeci">const XMLElement * LastChildElement(const char *name=0) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a174fd4c22c010b58138c1b84a0dfbd51"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a174fd4c22c010b58138c1b84a0dfbd51">tinyxml2::XMLNode::ToDeclaration</a></div><div class="ttdeci">virtual XMLDeclaration * ToDeclaration()</div><div class="ttdoc">Safely cast to a Declaration, or null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:708</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a1795a35852dc8aae877cc8ded986e59b"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a1795a35852dc8aae877cc8ded986e59b">tinyxml2::XMLNode::FirstChildElement</a></div><div class="ttdeci">const XMLElement * FirstChildElement(const char *name=0) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a2de84cfa4ec3fe249bad745069d145f1"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a2de84cfa4ec3fe249bad745069d145f1">tinyxml2::XMLNode::GetDocument</a></div><div class="ttdeci">const XMLDocument * GetDocument() const</div><div class="ttdoc">Get the XMLDocument that owns this XMLNode.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:681</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a363b6edbd6ebd55f8387d2b89f2b0921"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a363b6edbd6ebd55f8387d2b89f2b0921">tinyxml2::XMLNode::DeleteChild</a></div><div class="ttdeci">void DeleteChild(XMLNode *node)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a41c55dab9162d1eb62db2008430e376b"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a41c55dab9162d1eb62db2008430e376b">tinyxml2::XMLNode::ToText</a></div><div class="ttdeci">virtual XMLText * ToText()</div><div class="ttdoc">Safely cast to Text, or null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:696</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a62c71b6bf8734b5424063b8d9a61c266"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a62c71b6bf8734b5424063b8d9a61c266">tinyxml2::XMLNode::DeepClone</a></div><div class="ttdeci">XMLNode * DeepClone(XMLDocument *target) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a66344989a4b436155bcda72bd6b07b82"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a66344989a4b436155bcda72bd6b07b82">tinyxml2::XMLNode::Value</a></div><div class="ttdeci">const char * Value() const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a79db9ef0fe014d27790f2218b87bcbb5"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a79db9ef0fe014d27790f2218b87bcbb5">tinyxml2::XMLNode::NextSibling</a></div><div class="ttdeci">const XMLNode * NextSibling() const</div><div class="ttdoc">Get the next (right) sibling node of this node.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:821</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a7ce18b751c3ea09eac292dca264f9226"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a7ce18b751c3ea09eac292dca264f9226">tinyxml2::XMLNode::ShallowEqual</a></div><div class="ttdeci">virtual bool ShallowEqual(const XMLNode *compare) const =0</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a7f0687574afa03bc479dc44f29db0afe"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a7f0687574afa03bc479dc44f29db0afe">tinyxml2::XMLNode::GetUserData</a></div><div class="ttdeci">void * GetUserData() const</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:946</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a81e66df0a44c67a7af17f3b77a152785"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a81e66df0a44c67a7af17f3b77a152785">tinyxml2::XMLNode::Accept</a></div><div class="ttdeci">virtual bool Accept(XMLVisitor *visitor) const =0</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a836e2966ed736fc3c94f70e12a2a3357"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a836e2966ed736fc3c94f70e12a2a3357">tinyxml2::XMLNode::ToDocument</a></div><div class="ttdeci">virtual XMLDocument * ToDocument()</div><div class="ttdoc">Safely cast to a Document, or null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:704</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a8402cbd3129d20e9e6024bbcc0531283"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a8402cbd3129d20e9e6024bbcc0531283">tinyxml2::XMLNode::ShallowClone</a></div><div class="ttdeci">virtual XMLNode * ShallowClone(XMLDocument *document) const =0</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a85adb8f0b7477eec30f9a41d420b09c2"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a85adb8f0b7477eec30f9a41d420b09c2">tinyxml2::XMLNode::InsertAfterChild</a></div><div class="ttdeci">XMLNode * InsertAfterChild(XMLNode *afterThis, XMLNode *addThis)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a8675a74aa0ada6eccab0c77ef3e5b9bd"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a8675a74aa0ada6eccab0c77ef3e5b9bd">tinyxml2::XMLNode::ToUnknown</a></div><div class="ttdeci">virtual XMLUnknown * ToUnknown()</div><div class="ttdoc">Safely cast to an Unknown, or null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:712</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a872936cae46fb473eb47fec99129fc70"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a872936cae46fb473eb47fec99129fc70">tinyxml2::XMLNode::PreviousSiblingElement</a></div><div class="ttdeci">const XMLElement * PreviousSiblingElement(const char *name=0) const</div><div class="ttdoc">Get the previous (left) sibling element of this node, with an optionally supplied name.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a8ff7dc071f3a1a6ae2ac25a37492865d"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a8ff7dc071f3a1a6ae2ac25a37492865d">tinyxml2::XMLNode::InsertFirstChild</a></div><div class="ttdeci">XMLNode * InsertFirstChild(XMLNode *addThis)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a9b5fc636646fda761d342c72e91cb286"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a9b5fc636646fda761d342c72e91cb286">tinyxml2::XMLNode::GetLineNum</a></div><div class="ttdeci">int GetLineNum() const</div><div class="ttdoc">Gets the line number the node is in, if the document was parsed from a file.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:752</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_a9b8583a277e8e26f4cbbb5492786778e"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#a9b8583a277e8e26f4cbbb5492786778e">tinyxml2::XMLNode::LastChild</a></div><div class="ttdeci">const XMLNode * LastChild() const</div><div class="ttdoc">Get the last child node, or null if none exists.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:787</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_aab516e699567f75cc9ab2ef2eee501e8"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#aab516e699567f75cc9ab2ef2eee501e8">tinyxml2::XMLNode::ToElement</a></div><div class="ttdeci">virtual XMLElement * ToElement()</div><div class="ttdoc">Safely cast to an Element, or null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:692</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_aac667c513d445f8b783e1e15ef9d3551"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#aac667c513d445f8b783e1e15ef9d3551">tinyxml2::XMLNode::PreviousSibling</a></div><div class="ttdeci">const XMLNode * PreviousSibling() const</div><div class="ttdoc">Get the previous (left) sibling node of this node.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:805</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_ac3ab489e6e202a3cd1762d3b332e89d4"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#ac3ab489e6e202a3cd1762d3b332e89d4">tinyxml2::XMLNode::NoChildren</a></div><div class="ttdeci">bool NoChildren() const</div><div class="ttdoc">Returns true if this node has no children.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:764</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_ae0f62bc186c56c2e0483ebd52dbfbe34"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#ae0f62bc186c56c2e0483ebd52dbfbe34">tinyxml2::XMLNode::Parent</a></div><div class="ttdeci">const XMLNode * Parent() const</div><div class="ttdoc">Get the parent of this node on the DOM.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:755</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_ae7dc225e1018cdd685f7563593a1fe08"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#ae7dc225e1018cdd685f7563593a1fe08">tinyxml2::XMLNode::FirstChild</a></div><div class="ttdeci">const XMLNode * FirstChild() const</div><div class="ttdoc">Get the first child node, or null if none exists.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:769</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_aeb249ed60f4e8bfad3709151c3ee4286"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#aeb249ed60f4e8bfad3709151c3ee4286">tinyxml2::XMLNode::InsertEndChild</a></div><div class="ttdeci">XMLNode * InsertEndChild(XMLNode *addThis)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_af343d1ef0b45c0020e62d784d7e67a68"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#af343d1ef0b45c0020e62d784d7e67a68">tinyxml2::XMLNode::GetDocument</a></div><div class="ttdeci">XMLDocument * GetDocument()</div><div class="ttdoc">Get the XMLDocument that owns this XMLNode.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:686</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_node_html_aff47671055aa99840a1c1ebd661e63e3"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_node.html#aff47671055aa99840a1c1ebd661e63e3">tinyxml2::XMLNode::ToComment</a></div><div class="ttdeci">virtual XMLComment * ToComment()</div><div class="ttdoc">Safely cast to a Comment, or null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:700</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2238</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a01148e2ebe6776e38c5a3e41bc5feb74"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a01148e2ebe6776e38c5a3e41bc5feb74">tinyxml2::XMLPrinter::PrintSpace</a></div><div class="ttdeci">virtual void PrintSpace(int depth)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a15fc1f2b922f540917dcf52808737b29"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a15fc1f2b922f540917dcf52808737b29">tinyxml2::XMLPrinter::VisitExit</a></div><div class="ttdeci">virtual bool VisitExit(const XMLDocument &amp;)</div><div class="ttdoc">Visit a document.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2290</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a178c608ce8476043d5d6513819cde903"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a178c608ce8476043d5d6513819cde903">tinyxml2::XMLPrinter::PushHeader</a></div><div class="ttdeci">void PushHeader(bool writeBOM, bool writeDeclaration)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a180671d73844f159f2d4aafbc11d106e"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a180671d73844f159f2d4aafbc11d106e">tinyxml2::XMLPrinter::CStr</a></div><div class="ttdeci">const char * CStr() const</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2306</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a1cc16a9362df4332012cb13cff6441b3"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a1cc16a9362df4332012cb13cff6441b3">tinyxml2::XMLPrinter::PushText</a></div><div class="ttdeci">void PushText(const char *text, bool cdata=false)</div><div class="ttdoc">Add a text node.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a1dbb1390e829d0673af66b9cd1928bd7"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a1dbb1390e829d0673af66b9cd1928bd7">tinyxml2::XMLPrinter::PushText</a></div><div class="ttdeci">void PushText(float value)</div><div class="ttdoc">Add a text node from a float.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a20fb06c83bd13e5140d7dd13af06c010"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a20fb06c83bd13e5140d7dd13af06c010">tinyxml2::XMLPrinter::OpenElement</a></div><div class="ttdeci">void OpenElement(const char *name, bool compactMode=false)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a275ae25544a12199ae40b6994ca6e4de"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a275ae25544a12199ae40b6994ca6e4de">tinyxml2::XMLPrinter::Visit</a></div><div class="ttdeci">virtual bool Visit(const XMLText &amp;text)</div><div class="ttdoc">Visit a text node.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a2ce2aa508c21ac91615093ddb9c282c5"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a2ce2aa508c21ac91615093ddb9c282c5">tinyxml2::XMLPrinter::VisitEnter</a></div><div class="ttdeci">virtual bool VisitEnter(const XMLElement &amp;element, const XMLAttribute *attribute)</div><div class="ttdoc">Visit an element.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a3256cf3523d4898b91abb18b924be04c"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a3256cf3523d4898b91abb18b924be04c">tinyxml2::XMLPrinter::CStrSize</a></div><div class="ttdeci">int CStrSize() const</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2314</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a3e0d4d78de25d4cf081009e1431cea7e"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a3e0d4d78de25d4cf081009e1431cea7e">tinyxml2::XMLPrinter::PushText</a></div><div class="ttdeci">void PushText(int value)</div><div class="ttdoc">Add a text node from an integer.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a3f16a30be1537ac141d9bd2db824ba9e"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a3f16a30be1537ac141d9bd2db824ba9e">tinyxml2::XMLPrinter::Visit</a></div><div class="ttdeci">virtual bool Visit(const XMLComment &amp;comment)</div><div class="ttdoc">Visit a comment node.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a4390e5fa1ed05189a8686647345ab29f"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a4390e5fa1ed05189a8686647345ab29f">tinyxml2::XMLPrinter::PushText</a></div><div class="ttdeci">void PushText(bool value)</div><div class="ttdoc">Add a text node from a bool.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a60b0a4cf57371ff8679c2c7556ccb708"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a60b0a4cf57371ff8679c2c7556ccb708">tinyxml2::XMLPrinter::PushText</a></div><div class="ttdeci">void PushText(uint64_t value)</div><div class="ttdoc">Add a text node from an unsigned 64bit integer.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a661fb50e7e0a4918d2d259cb0fae647e"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a661fb50e7e0a4918d2d259cb0fae647e">tinyxml2::XMLPrinter::PushText</a></div><div class="ttdeci">void PushText(unsigned value)</div><div class="ttdoc">Add a text node from an unsigned.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a690cb140ba98b7339734ff865f56b0b3"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a690cb140ba98b7339734ff865f56b0b3">tinyxml2::XMLPrinter::ClearBuffer</a></div><div class="ttdeci">void ClearBuffer(bool resetToFirstElement=true)</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:2321</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a96b0a0bfe105154a0a6c37d725258f0a"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a96b0a0bfe105154a0a6c37d725258f0a">tinyxml2::XMLPrinter::PushText</a></div><div class="ttdeci">void PushText(int64_t value)</div><div class="ttdoc">Add a text node from a signed 64bit integer.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a9a4e2c9348b42e147629d5a99f4af3f0"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a9a4e2c9348b42e147629d5a99f4af3f0">tinyxml2::XMLPrinter::PushAttribute</a></div><div class="ttdeci">void PushAttribute(const char *name, const char *value)</div><div class="ttdoc">If streaming, add an attribute to an open element.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_a9ceff5cd85e5db65838962174fcdcc46"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#a9ceff5cd85e5db65838962174fcdcc46">tinyxml2::XMLPrinter::Visit</a></div><div class="ttdeci">virtual bool Visit(const XMLDeclaration &amp;declaration)</div><div class="ttdoc">Visit a declaration.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_aa15e1da81e17dea5da6499ac5b08d9d8"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#aa15e1da81e17dea5da6499ac5b08d9d8">tinyxml2::XMLPrinter::Visit</a></div><div class="ttdeci">virtual bool Visit(const XMLUnknown &amp;unknown)</div><div class="ttdoc">Visit an unknown node.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_aa6d3841c069085f5b8a27bc7103c04f7"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#aa6d3841c069085f5b8a27bc7103c04f7">tinyxml2::XMLPrinter::XMLPrinter</a></div><div class="ttdeci">XMLPrinter(FILE *file=0, bool compact=false, int depth=0)</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_aa715302dfc09473c77c853cbd5431965"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#aa715302dfc09473c77c853cbd5431965">tinyxml2::XMLPrinter::PushText</a></div><div class="ttdeci">void PushText(double value)</div><div class="ttdoc">Add a text node from a double.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_ad04d29562b46fcdb23ab320f8b664240"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#ad04d29562b46fcdb23ab320f8b664240">tinyxml2::XMLPrinter::CloseElement</a></div><div class="ttdeci">virtual void CloseElement(bool compactMode=false)</div><div class="ttdoc">If streaming, close the Element.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_ae966b988a7a28c41e91c5ca17fb2054b"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#ae966b988a7a28c41e91c5ca17fb2054b">tinyxml2::XMLPrinter::VisitEnter</a></div><div class="ttdeci">virtual bool VisitEnter(const XMLDocument &amp;)</div><div class="ttdoc">Visit a document.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_ae99e0a7086543591edfb565f24689098"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#ae99e0a7086543591edfb565f24689098">tinyxml2::XMLPrinter::VisitExit</a></div><div class="ttdeci">virtual bool VisitExit(const XMLElement &amp;element)</div><div class="ttdoc">Visit an element.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_printer_html_afc8416814219591c2fd5656e0c233140"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_printer.html#afc8416814219591c2fd5656e0c233140">tinyxml2::XMLPrinter::PushComment</a></div><div class="ttdeci">void PushComment(const char *comment)</div><div class="ttdoc">Add a comment.</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_text_html"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_text.html">tinyxml2::XMLText</a></div><div class="ttdef"><b>Definition:</b> tinyxml2.h:992</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_text_html_a1b2c1448f1a21299d0a7913f18b55206"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_text.html#a1b2c1448f1a21299d0a7913f18b55206">tinyxml2::XMLText::Accept</a></div><div class="ttdeci">virtual bool Accept(XMLVisitor *visitor) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_text_html_ab1213b4ddebe9b17ec7e7040e9f1caf7"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_text.html#ab1213b4ddebe9b17ec7e7040e9f1caf7">tinyxml2::XMLText::ToText</a></div><div class="ttdeci">virtual XMLText * ToText()</div><div class="ttdoc">Safely cast to Text, or null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:997</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_text_html_ac1bb5ea4166c320882d9e0ad16fd385b"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_text.html#ac1bb5ea4166c320882d9e0ad16fd385b">tinyxml2::XMLText::CData</a></div><div class="ttdeci">bool CData() const</div><div class="ttdoc">Returns true if this is a CDATA text element.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1009</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_text_html_ad080357d76ab7cc59d7651249949329d"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_text.html#ad080357d76ab7cc59d7651249949329d">tinyxml2::XMLText::SetCData</a></div><div class="ttdeci">void SetCData(bool isCData)</div><div class="ttdoc">Declare whether this should be CDATA or standard text.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1005</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_text_html_ae0fff8a24e2de7eb073fd192e9db0331"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_text.html#ae0fff8a24e2de7eb073fd192e9db0331">tinyxml2::XMLText::ShallowEqual</a></div><div class="ttdeci">virtual bool ShallowEqual(const XMLNode *compare) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_text_html_af3a81ed4dd49d5151c477b3f265a3011"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_text.html#af3a81ed4dd49d5151c477b3f265a3011">tinyxml2::XMLText::ShallowClone</a></div><div class="ttdeci">virtual XMLNode * ShallowClone(XMLDocument *document) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_unknown_html"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_unknown.html">tinyxml2::XMLUnknown</a></div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1106</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_unknown_html_a0125f41c89763dea06619b5fd5246b4c"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_unknown.html#a0125f41c89763dea06619b5fd5246b4c">tinyxml2::XMLUnknown::ShallowClone</a></div><div class="ttdeci">virtual XMLNode * ShallowClone(XMLDocument *document) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_unknown_html_a0715ab2c05d7f74845c188122213b116"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_unknown.html#a0715ab2c05d7f74845c188122213b116">tinyxml2::XMLUnknown::ShallowEqual</a></div><div class="ttdeci">virtual bool ShallowEqual(const XMLNode *compare) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_unknown_html_a70983aa1b1cff3d3aa6d4d0a80e5ee48"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_unknown.html#a70983aa1b1cff3d3aa6d4d0a80e5ee48">tinyxml2::XMLUnknown::Accept</a></div><div class="ttdeci">virtual bool Accept(XMLVisitor *visitor) const</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_unknown_html_af4374856421921cad578c8affae872b6"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_unknown.html#af4374856421921cad578c8affae872b6">tinyxml2::XMLUnknown::ToUnknown</a></div><div class="ttdeci">virtual XMLUnknown * ToUnknown()</div><div class="ttdoc">Safely cast to an Unknown, or null.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:1109</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_visitor_html"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_visitor.html">tinyxml2::XMLVisitor</a></div><div class="ttdef"><b>Definition:</b> tinyxml2.h:482</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_visitor_html_a14e4748387c34bf53d24e8119bb1f292"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_visitor.html#a14e4748387c34bf53d24e8119bb1f292">tinyxml2::XMLVisitor::Visit</a></div><div class="ttdeci">virtual bool Visit(const XMLUnknown &amp;)</div><div class="ttdoc">Visit an unknown node.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:517</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_visitor_html_a170e9989cd046ba904f302d087e07086"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_visitor.html#a170e9989cd046ba904f302d087e07086">tinyxml2::XMLVisitor::VisitExit</a></div><div class="ttdeci">virtual bool VisitExit(const XMLDocument &amp;)</div><div class="ttdoc">Visit a document.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:491</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_visitor_html_a772f10ddc83f881956d32628faa16eb6"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_visitor.html#a772f10ddc83f881956d32628faa16eb6">tinyxml2::XMLVisitor::VisitExit</a></div><div class="ttdeci">virtual bool VisitExit(const XMLElement &amp;)</div><div class="ttdoc">Visit an element.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:500</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_visitor_html_acb3c22fc5f60eb9db98f533f2761f67d"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_visitor.html#acb3c22fc5f60eb9db98f533f2761f67d">tinyxml2::XMLVisitor::VisitEnter</a></div><div class="ttdeci">virtual bool VisitEnter(const XMLDocument &amp;)</div><div class="ttdoc">Visit a document.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:487</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_visitor_html_acc8147fb5a85f6c65721654e427752d7"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_visitor.html#acc8147fb5a85f6c65721654e427752d7">tinyxml2::XMLVisitor::Visit</a></div><div class="ttdeci">virtual bool Visit(const XMLComment &amp;)</div><div class="ttdoc">Visit a comment node.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:513</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_visitor_html_adc75bd459fc7ba8223b50f0616767f9a"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_visitor.html#adc75bd459fc7ba8223b50f0616767f9a">tinyxml2::XMLVisitor::Visit</a></div><div class="ttdeci">virtual bool Visit(const XMLDeclaration &amp;)</div><div class="ttdoc">Visit a declaration.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:505</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_visitor_html_af30233565856480ea48b6fa0d6dec65b"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_visitor.html#af30233565856480ea48b6fa0d6dec65b">tinyxml2::XMLVisitor::Visit</a></div><div class="ttdeci">virtual bool Visit(const XMLText &amp;)</div><div class="ttdoc">Visit a text node.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:509</div></div>
<div class="ttc" id="aclasstinyxml2_1_1_x_m_l_visitor_html_af97980a17dd4e37448b181f5ddfa92b5"><div class="ttname"><a href="classtinyxml2_1_1_x_m_l_visitor.html#af97980a17dd4e37448b181f5ddfa92b5">tinyxml2::XMLVisitor::VisitEnter</a></div><div class="ttdeci">virtual bool VisitEnter(const XMLElement &amp;, const XMLAttribute *)</div><div class="ttdoc">Visit an element.</div><div class="ttdef"><b>Definition:</b> tinyxml2.h:496</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>tinyxml2</b></li><li class="navelem"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tinyxml2::XMLDocument Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a9efa54f7ecb37c17ab1fa2b3078ccca1">Accept</a>(XMLVisitor *visitor) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a65656b0b2cbc822708eb351504178aaf">Clear</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a4085d9c52f1d93214311459d6d1fcf17">ClearError</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a62c71b6bf8734b5424063b8d9a61c266">DeepClone</a>(XMLDocument *target) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#af592ffc91514e25a39664521ac83db45">DeepCopy</a>(XMLDocument *target) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a363b6edbd6ebd55f8387d2b89f2b0921">DeleteChild</a>(XMLNode *node)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a0360085cc54df5bff85d5c5da13afdce">DeleteChildren</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#ac1d6e2c7fcc1a660624ac4f68e96380d">DeleteNode</a>(XMLNode *node)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a34e6318e182e40e3cc4f4ba5d59ed9ed">Error</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#afa3ed33b3107f920ec2b301f805ac17d">ErrorID</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a57400f816dbe7799ece33615ead9ab76">ErrorLineNum</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#ad75aa9d32c4e8b300655186808aa9abf">ErrorStr</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ae7dc225e1018cdd685f7563593a1fe08">FirstChild</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a1795a35852dc8aae877cc8ded986e59b">FirstChildElement</a>(const char *name=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a2de84cfa4ec3fe249bad745069d145f1">GetDocument</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#af343d1ef0b45c0020e62d784d7e67a68">GetDocument</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a9b5fc636646fda761d342c72e91cb286">GetLineNum</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a7f0687574afa03bc479dc44f29db0afe">GetUserData</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a33fc5d159db873a179fa26338adb05bd">HasBOM</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a85adb8f0b7477eec30f9a41d420b09c2">InsertAfterChild</a>(XMLNode *afterThis, XMLNode *addThis)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aeb249ed60f4e8bfad3709151c3ee4286">InsertEndChild</a>(XMLNode *addThis)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8ff7dc071f3a1a6ae2ac25a37492865d">InsertFirstChild</a>(XMLNode *addThis)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a9b8583a277e8e26f4cbbb5492786778e">LastChild</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a173e9d1341bc56992e2d320a35936551">LastChildElement</a>(const char *name=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a2ebd4647a8af5fc6831b294ac26a150a">LoadFile</a>(const char *filename)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a5f1d330fad44c52f3d265338dd2a6dc2">LoadFile</a>(FILE *)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#ade4874bcb439954972ef2b3723ff3259">NewComment</a>(const char *comment)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#aee2eb3435923f5494dcc70ac225b60a2">NewDeclaration</a>(const char *text=0)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a8aa7817d4a1001364b06373763ab99d6">NewElement</a>(const char *name)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#ab7e8b29ae4099092a8bb947da6361296">NewText</a>(const char *text)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a5385c937734ff6db9226ab707d2c7147">NewUnknown</a>(const char *text)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a79db9ef0fe014d27790f2218b87bcbb5">NextSibling</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a1264c86233328f0cd36297552d982f80">NextSiblingElement</a>(const char *name=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ac3ab489e6e202a3cd1762d3b332e89d4">NoChildren</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ae0f62bc186c56c2e0483ebd52dbfbe34">Parent</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#af2b616169e6517182f6725f2498e9a01">Parse</a>(const char *xml, size_t nBytes=static_cast&lt; size_t &gt;(-1))</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aac667c513d445f8b783e1e15ef9d3551">PreviousSibling</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a872936cae46fb473eb47fec99129fc70">PreviousSiblingElement</a>(const char *name=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a867cf5fa3e3ff6ae4847a8b7ee8ec083">Print</a>(XMLPrinter *streamer=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a1d033945b42e125d933d6231e4571552">PrintError</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#ad2b70320d3c2a071c2f36928edff3e1c">RootElement</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a73ac416b4a2aa0952e841220eb3da18f">SaveFile</a>(const char *filename, bool compact=false)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a8b95779479a0035acc67b3a61dfe1b74">SaveFile</a>(FILE *fp, bool compact=false)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a14419b698f7c4b140df4e80f3f0c93b0">SetBOM</a>(bool useBOM)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a002978fc889cc011d143185f2377eca2">SetUserData</a>(void *userData)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a09dd68cf9eae137579f6e50f36487513">SetValue</a>(const char *val, bool staticMem=false)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#aa37cc1709d7e1e988bc17dcfb24a69b8">ShallowClone</a>(XMLDocument *) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a6fe5ef18699091844fcf64b56ffa5bf9">ShallowEqual</a>(const XMLNode *) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aff47671055aa99840a1c1ebd661e63e3">ToComment</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a174fd4c22c010b58138c1b84a0dfbd51">ToDeclaration</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a3e185f880882bd978367bb55937735ec">ToDocument</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aab516e699567f75cc9ab2ef2eee501e8">ToElement</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a41c55dab9162d1eb62db2008430e376b">ToText</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8675a74aa0ada6eccab0c77ef3e5b9bd">ToUnknown</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a66344989a4b436155bcda72bd6b07b82">Value</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a57ddf17b6e054dda10af98991b1b8f70">XMLDocument</a>(bool processEntities=true, Whitespace whitespaceMode=PRESERVE_WHITESPACE)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">tinyxml2::XMLDocument</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

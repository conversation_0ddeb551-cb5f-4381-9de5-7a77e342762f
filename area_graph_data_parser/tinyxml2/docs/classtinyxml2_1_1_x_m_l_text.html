<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: tinyxml2::XMLText Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>tinyxml2</b></li><li class="navelem"><a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classtinyxml2_1_1_x_m_l_text-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">tinyxml2::XMLText Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for tinyxml2::XMLText:</div>
<div class="dyncontent">
 <div class="center">
  <img src="classtinyxml2_1_1_x_m_l_text.png" usemap="#tinyxml2::XMLText_map" alt=""/>
  <map id="tinyxml2::XMLText_map" name="tinyxml2::XMLText_map">
<area href="classtinyxml2_1_1_x_m_l_node.html" alt="tinyxml2::XMLNode" shape="rect" coords="0,0,120,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a1b2c1448f1a21299d0a7913f18b55206"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_text.html#a1b2c1448f1a21299d0a7913f18b55206">Accept</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a> *visitor) const</td></tr>
<tr class="separator:a1b2c1448f1a21299d0a7913f18b55206"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab1213b4ddebe9b17ec7e7040e9f1caf7"><td class="memItemLeft" align="right" valign="top"><a id="ab1213b4ddebe9b17ec7e7040e9f1caf7"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_text.html#ab1213b4ddebe9b17ec7e7040e9f1caf7">ToText</a> ()</td></tr>
<tr class="memdesc:ab1213b4ddebe9b17ec7e7040e9f1caf7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to Text, or null. <br /></td></tr>
<tr class="separator:ab1213b4ddebe9b17ec7e7040e9f1caf7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad080357d76ab7cc59d7651249949329d"><td class="memItemLeft" align="right" valign="top"><a id="ad080357d76ab7cc59d7651249949329d"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_text.html#ad080357d76ab7cc59d7651249949329d">SetCData</a> (bool isCData)</td></tr>
<tr class="memdesc:ad080357d76ab7cc59d7651249949329d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Declare whether this should be CDATA or standard text. <br /></td></tr>
<tr class="separator:ad080357d76ab7cc59d7651249949329d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac1bb5ea4166c320882d9e0ad16fd385b"><td class="memItemLeft" align="right" valign="top"><a id="ac1bb5ea4166c320882d9e0ad16fd385b"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_text.html#ac1bb5ea4166c320882d9e0ad16fd385b">CData</a> () const</td></tr>
<tr class="memdesc:ac1bb5ea4166c320882d9e0ad16fd385b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if this is a CDATA text element. <br /></td></tr>
<tr class="separator:ac1bb5ea4166c320882d9e0ad16fd385b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af3a81ed4dd49d5151c477b3f265a3011"><td class="memItemLeft" align="right" valign="top">virtual <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_text.html#af3a81ed4dd49d5151c477b3f265a3011">ShallowClone</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *document) const</td></tr>
<tr class="separator:af3a81ed4dd49d5151c477b3f265a3011"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0fff8a24e2de7eb073fd192e9db0331"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_text.html#ae0fff8a24e2de7eb073fd192e9db0331">ShallowEqual</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *compare) const</td></tr>
<tr class="separator:ae0fff8a24e2de7eb073fd192e9db0331"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classtinyxml2_1_1_x_m_l_node"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classtinyxml2_1_1_x_m_l_node')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td></tr>
<tr class="memitem:a2de84cfa4ec3fe249bad745069d145f1 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a2de84cfa4ec3fe249bad745069d145f1"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a2de84cfa4ec3fe249bad745069d145f1">GetDocument</a> () const</td></tr>
<tr class="memdesc:a2de84cfa4ec3fe249bad745069d145f1 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> that owns this <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>. <br /></td></tr>
<tr class="separator:a2de84cfa4ec3fe249bad745069d145f1 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af343d1ef0b45c0020e62d784d7e67a68 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="af343d1ef0b45c0020e62d784d7e67a68"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#af343d1ef0b45c0020e62d784d7e67a68">GetDocument</a> ()</td></tr>
<tr class="memdesc:af343d1ef0b45c0020e62d784d7e67a68 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> that owns this <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>. <br /></td></tr>
<tr class="separator:af343d1ef0b45c0020e62d784d7e67a68 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab516e699567f75cc9ab2ef2eee501e8 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="aab516e699567f75cc9ab2ef2eee501e8"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aab516e699567f75cc9ab2ef2eee501e8">ToElement</a> ()</td></tr>
<tr class="memdesc:aab516e699567f75cc9ab2ef2eee501e8 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to an Element, or null. <br /></td></tr>
<tr class="separator:aab516e699567f75cc9ab2ef2eee501e8 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff47671055aa99840a1c1ebd661e63e3 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="aff47671055aa99840a1c1ebd661e63e3"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aff47671055aa99840a1c1ebd661e63e3">ToComment</a> ()</td></tr>
<tr class="memdesc:aff47671055aa99840a1c1ebd661e63e3 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to a Comment, or null. <br /></td></tr>
<tr class="separator:aff47671055aa99840a1c1ebd661e63e3 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a836e2966ed736fc3c94f70e12a2a3357 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a836e2966ed736fc3c94f70e12a2a3357"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a836e2966ed736fc3c94f70e12a2a3357">ToDocument</a> ()</td></tr>
<tr class="memdesc:a836e2966ed736fc3c94f70e12a2a3357 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to a Document, or null. <br /></td></tr>
<tr class="separator:a836e2966ed736fc3c94f70e12a2a3357 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a174fd4c22c010b58138c1b84a0dfbd51 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a174fd4c22c010b58138c1b84a0dfbd51"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a174fd4c22c010b58138c1b84a0dfbd51">ToDeclaration</a> ()</td></tr>
<tr class="memdesc:a174fd4c22c010b58138c1b84a0dfbd51 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to a Declaration, or null. <br /></td></tr>
<tr class="separator:a174fd4c22c010b58138c1b84a0dfbd51 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8675a74aa0ada6eccab0c77ef3e5b9bd inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a8675a74aa0ada6eccab0c77ef3e5b9bd"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8675a74aa0ada6eccab0c77ef3e5b9bd">ToUnknown</a> ()</td></tr>
<tr class="memdesc:a8675a74aa0ada6eccab0c77ef3e5b9bd inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to an Unknown, or null. <br /></td></tr>
<tr class="separator:a8675a74aa0ada6eccab0c77ef3e5b9bd inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66344989a4b436155bcda72bd6b07b82 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a66344989a4b436155bcda72bd6b07b82">Value</a> () const</td></tr>
<tr class="separator:a66344989a4b436155bcda72bd6b07b82 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09dd68cf9eae137579f6e50f36487513 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a09dd68cf9eae137579f6e50f36487513">SetValue</a> (const char *val, bool staticMem=false)</td></tr>
<tr class="separator:a09dd68cf9eae137579f6e50f36487513 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b5fc636646fda761d342c72e91cb286 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a9b5fc636646fda761d342c72e91cb286"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a9b5fc636646fda761d342c72e91cb286">GetLineNum</a> () const</td></tr>
<tr class="memdesc:a9b5fc636646fda761d342c72e91cb286 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the line number the node is in, if the document was parsed from a file. <br /></td></tr>
<tr class="separator:a9b5fc636646fda761d342c72e91cb286 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0f62bc186c56c2e0483ebd52dbfbe34 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="ae0f62bc186c56c2e0483ebd52dbfbe34"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ae0f62bc186c56c2e0483ebd52dbfbe34">Parent</a> () const</td></tr>
<tr class="memdesc:ae0f62bc186c56c2e0483ebd52dbfbe34 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the parent of this node on the DOM. <br /></td></tr>
<tr class="separator:ae0f62bc186c56c2e0483ebd52dbfbe34 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3ab489e6e202a3cd1762d3b332e89d4 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="ac3ab489e6e202a3cd1762d3b332e89d4"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ac3ab489e6e202a3cd1762d3b332e89d4">NoChildren</a> () const</td></tr>
<tr class="memdesc:ac3ab489e6e202a3cd1762d3b332e89d4 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if this node has no children. <br /></td></tr>
<tr class="separator:ac3ab489e6e202a3cd1762d3b332e89d4 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae7dc225e1018cdd685f7563593a1fe08 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="ae7dc225e1018cdd685f7563593a1fe08"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ae7dc225e1018cdd685f7563593a1fe08">FirstChild</a> () const</td></tr>
<tr class="memdesc:ae7dc225e1018cdd685f7563593a1fe08 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the first child node, or null if none exists. <br /></td></tr>
<tr class="separator:ae7dc225e1018cdd685f7563593a1fe08 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1795a35852dc8aae877cc8ded986e59b inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a1795a35852dc8aae877cc8ded986e59b">FirstChildElement</a> (const char *name=0) const</td></tr>
<tr class="separator:a1795a35852dc8aae877cc8ded986e59b inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b8583a277e8e26f4cbbb5492786778e inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a9b8583a277e8e26f4cbbb5492786778e"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a9b8583a277e8e26f4cbbb5492786778e">LastChild</a> () const</td></tr>
<tr class="memdesc:a9b8583a277e8e26f4cbbb5492786778e inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the last child node, or null if none exists. <br /></td></tr>
<tr class="separator:a9b8583a277e8e26f4cbbb5492786778e inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a173e9d1341bc56992e2d320a35936551 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a173e9d1341bc56992e2d320a35936551">LastChildElement</a> (const char *name=0) const</td></tr>
<tr class="separator:a173e9d1341bc56992e2d320a35936551 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac667c513d445f8b783e1e15ef9d3551 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="aac667c513d445f8b783e1e15ef9d3551"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aac667c513d445f8b783e1e15ef9d3551">PreviousSibling</a> () const</td></tr>
<tr class="memdesc:aac667c513d445f8b783e1e15ef9d3551 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the previous (left) sibling node of this node. <br /></td></tr>
<tr class="separator:aac667c513d445f8b783e1e15ef9d3551 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a872936cae46fb473eb47fec99129fc70 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a872936cae46fb473eb47fec99129fc70"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a872936cae46fb473eb47fec99129fc70">PreviousSiblingElement</a> (const char *name=0) const</td></tr>
<tr class="memdesc:a872936cae46fb473eb47fec99129fc70 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the previous (left) sibling element of this node, with an optionally supplied name. <br /></td></tr>
<tr class="separator:a872936cae46fb473eb47fec99129fc70 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79db9ef0fe014d27790f2218b87bcbb5 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a79db9ef0fe014d27790f2218b87bcbb5"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a79db9ef0fe014d27790f2218b87bcbb5">NextSibling</a> () const</td></tr>
<tr class="memdesc:a79db9ef0fe014d27790f2218b87bcbb5 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the next (right) sibling node of this node. <br /></td></tr>
<tr class="separator:a79db9ef0fe014d27790f2218b87bcbb5 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1264c86233328f0cd36297552d982f80 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a1264c86233328f0cd36297552d982f80"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a1264c86233328f0cd36297552d982f80">NextSiblingElement</a> (const char *name=0) const</td></tr>
<tr class="memdesc:a1264c86233328f0cd36297552d982f80 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the next (right) sibling element of this node, with an optionally supplied name. <br /></td></tr>
<tr class="separator:a1264c86233328f0cd36297552d982f80 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeb249ed60f4e8bfad3709151c3ee4286 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aeb249ed60f4e8bfad3709151c3ee4286">InsertEndChild</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *addThis)</td></tr>
<tr class="separator:aeb249ed60f4e8bfad3709151c3ee4286 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8ff7dc071f3a1a6ae2ac25a37492865d inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8ff7dc071f3a1a6ae2ac25a37492865d">InsertFirstChild</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *addThis)</td></tr>
<tr class="separator:a8ff7dc071f3a1a6ae2ac25a37492865d inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a85adb8f0b7477eec30f9a41d420b09c2 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a85adb8f0b7477eec30f9a41d420b09c2">InsertAfterChild</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *afterThis, <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *addThis)</td></tr>
<tr class="separator:a85adb8f0b7477eec30f9a41d420b09c2 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0360085cc54df5bff85d5c5da13afdce inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a0360085cc54df5bff85d5c5da13afdce">DeleteChildren</a> ()</td></tr>
<tr class="separator:a0360085cc54df5bff85d5c5da13afdce inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a363b6edbd6ebd55f8387d2b89f2b0921 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a363b6edbd6ebd55f8387d2b89f2b0921">DeleteChild</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *node)</td></tr>
<tr class="separator:a363b6edbd6ebd55f8387d2b89f2b0921 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62c71b6bf8734b5424063b8d9a61c266 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a62c71b6bf8734b5424063b8d9a61c266">DeepClone</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *target) const</td></tr>
<tr class="separator:a62c71b6bf8734b5424063b8d9a61c266 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a002978fc889cc011d143185f2377eca2 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a002978fc889cc011d143185f2377eca2">SetUserData</a> (void *userData)</td></tr>
<tr class="separator:a002978fc889cc011d143185f2377eca2 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f0687574afa03bc479dc44f29db0afe inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a7f0687574afa03bc479dc44f29db0afe">GetUserData</a> () const</td></tr>
<tr class="separator:a7f0687574afa03bc479dc44f29db0afe inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>XML text.</p>
<p>Note that a text node can have child element nodes, for example: </p><pre class="fragment">&lt;root&gt;This is &lt;b&gt;bold&lt;/b&gt;&lt;/root&gt;
</pre><p>A text node can have 2 ways to output the next. "normal" output and CDATA. It will default to the mode it was parsed from the XML file and you generally want to leave it alone, but you can change the output mode with <a class="el" href="classtinyxml2_1_1_x_m_l_text.html#ad080357d76ab7cc59d7651249949329d" title="Declare whether this should be CDATA or standard text.">SetCData()</a> and query it with <a class="el" href="classtinyxml2_1_1_x_m_l_text.html#ac1bb5ea4166c320882d9e0ad16fd385b" title="Returns true if this is a CDATA text element.">CData()</a>. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a1b2c1448f1a21299d0a7913f18b55206"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1b2c1448f1a21299d0a7913f18b55206">&#9670;&nbsp;</a></span>Accept()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool tinyxml2::XMLText::Accept </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a> *&#160;</td>
          <td class="paramname"><em>visitor</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Accept a hierarchical visit of the nodes in the TinyXML-2 DOM. Every node in the XML tree will be conditionally visited and the host will be called back via the <a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a> interface.</p>
<p>This is essentially a SAX interface for TinyXML-2. (Note however it doesn't re-parse the XML for the callbacks, so the performance of TinyXML-2 is unchanged by using this interface versus any other.)</p>
<p>The interface has been based on ideas from:</p>
<ul>
<li><a href="http://www.saxproject.org/">http://www.saxproject.org/</a></li>
<li><a href="http://c2.com/cgi/wiki?HierarchicalVisitorPattern">http://c2.com/cgi/wiki?HierarchicalVisitorPattern</a></li>
</ul>
<p>Which are both good references for "visiting".</p>
<p>An example of using <a class="el" href="classtinyxml2_1_1_x_m_l_text.html#a1b2c1448f1a21299d0a7913f18b55206">Accept()</a>: </p><pre class="fragment">XMLPrinter printer;
tinyxmlDoc.Accept( &amp;printer );
const char* xmlcstr = printer.CStr();
</pre> 
<p>Implements <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a81e66df0a44c67a7af17f3b77a152785">tinyxml2::XMLNode</a>.</p>

</div>
</div>
<a id="af3a81ed4dd49d5151c477b3f265a3011"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af3a81ed4dd49d5151c477b3f265a3011">&#9670;&nbsp;</a></span>ShallowClone()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* tinyxml2::XMLText::ShallowClone </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td>
          <td class="paramname"><em>document</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Make a copy of this node, but not its children. You may pass in a Document pointer that will be the owner of the new Node. If the 'document' is null, then the node returned will be allocated from the current Document. (this-&gt;<a class="el" href="classtinyxml2_1_1_x_m_l_node.html#af343d1ef0b45c0020e62d784d7e67a68" title="Get the XMLDocument that owns this XMLNode.">GetDocument()</a>)</p>
<p>Note: if called on a <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>, this will return null. </p>

<p>Implements <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8402cbd3129d20e9e6024bbcc0531283">tinyxml2::XMLNode</a>.</p>

</div>
</div>
<a id="ae0fff8a24e2de7eb073fd192e9db0331"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae0fff8a24e2de7eb073fd192e9db0331">&#9670;&nbsp;</a></span>ShallowEqual()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool tinyxml2::XMLText::ShallowEqual </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td>
          <td class="paramname"><em>compare</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Test if 2 nodes are the same, but don't test children. The 2 nodes do not need to be in the same Document.</p>
<p>Note: if called on a <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>, this will return false. </p>

<p>Implements <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a7ce18b751c3ea09eac292dca264f9226">tinyxml2::XMLNode</a>.</p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

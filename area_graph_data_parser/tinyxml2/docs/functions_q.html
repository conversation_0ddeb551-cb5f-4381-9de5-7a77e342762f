<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_q"></a>- q -</h3><ul>
<li>QueryAttribute()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a5b7df3bed2b8954eabf227fa204522eb">tinyxml2::XMLElement</a>
</li>
<li>QueryBoolAttribute()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a14c1bb77c39689838be01838d86ca872">tinyxml2::XMLElement</a>
</li>
<li>QueryBoolText()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a3fe5417d59eb8f5c4afe924b7d332736">tinyxml2::XMLElement</a>
</li>
<li>QueryBoolValue()
: <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a5f32e038954256f61c21ff20fd13a09c">tinyxml2::XMLAttribute</a>
</li>
<li>QueryDoubleAttribute()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a5f0964e2dbd8e2ee7fce9beab689443c">tinyxml2::XMLElement</a>
</li>
<li>QueryDoubleText()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a684679c99bb036a25652744cec6c4d96">tinyxml2::XMLElement</a>
</li>
<li>QueryDoubleValue()
: <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a2aa6e55e8ea03af0609cf6690bff79b9">tinyxml2::XMLAttribute</a>
</li>
<li>QueryFloatAttribute()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#acd5eeddf6002ef90806af794b9d9a5a5">tinyxml2::XMLElement</a>
</li>
<li>QueryFloatText()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#afa332afedd93210daa6d44b88eb11e29">tinyxml2::XMLElement</a>
</li>
<li>QueryFloatValue()
: <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a049dea6449a6259b6cfed44a9427b607">tinyxml2::XMLAttribute</a>
</li>
<li>QueryInt64Attribute()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a7c0955d80b6f8d196744eacb0f6e90a8">tinyxml2::XMLElement</a>
</li>
<li>QueryInt64Text()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a120c538c8eead169e635dbc70fb226d8">tinyxml2::XMLElement</a>
</li>
<li>QueryInt64Value()
: <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a4e25344d6e4159026be34dbddf1dcac2">tinyxml2::XMLAttribute</a>
</li>
<li>QueryIntAttribute()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a8a78bc1187c1c45ad89f2690eab567b1">tinyxml2::XMLElement</a>
</li>
<li>QueryIntText()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">tinyxml2::XMLElement</a>
</li>
<li>QueryIntValue()
: <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a6d5176260db00ea301c01af8457cd993">tinyxml2::XMLAttribute</a>
</li>
<li>QueryStringAttribute()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#adb8ae765f98d0c5037faec48deea78bc">tinyxml2::XMLElement</a>
</li>
<li>QueryUnsigned64Attribute()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a13dd590b5d3958ce2ed79844aacd9405">tinyxml2::XMLElement</a>
</li>
<li>QueryUnsigned64Text()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ac2239b3bd172ad8f5b78d04d4236144b">tinyxml2::XMLElement</a>
</li>
<li>QueryUnsigned64Value()
: <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#af793c695e7ee65cf20b8010d38b1d157">tinyxml2::XMLAttribute</a>
</li>
<li>QueryUnsignedAttribute()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a26fc84cbfba6769dafcfbf256c05e22f">tinyxml2::XMLElement</a>
</li>
<li>QueryUnsignedText()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a14d38aa4b5e18a46274a27425188a6a1">tinyxml2::XMLElement</a>
</li>
<li>QueryUnsignedValue()
: <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a48a7f3496f1415832e451bd8d09c9cb9">tinyxml2::XMLAttribute</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

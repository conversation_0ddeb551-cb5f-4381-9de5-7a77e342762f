<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_t"></a>- t -</h3><ul>
<li>ToComment()
: <a class="el" href="classtinyxml2_1_1_x_m_l_comment.html#a8093e1dc8a34fa446d9dc3fde0e6c0ee">tinyxml2::XMLComment</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aff47671055aa99840a1c1ebd661e63e3">tinyxml2::XMLNode</a>
</li>
<li>ToDeclaration()
: <a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html#a159d8ac45865215e88059ea1e5b52fc5">tinyxml2::XMLDeclaration</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a108858be7ee3eb53f73b5194c1aa8ff0">tinyxml2::XMLHandle</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a174fd4c22c010b58138c1b84a0dfbd51">tinyxml2::XMLNode</a>
</li>
<li>ToDocument()
: <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a3e185f880882bd978367bb55937735ec">tinyxml2::XMLDocument</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a836e2966ed736fc3c94f70e12a2a3357">tinyxml2::XMLNode</a>
</li>
<li>ToElement()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ad9ff5c2dbc15df36cf664ce1b0ea0a5d">tinyxml2::XMLElement</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a5e73ed8f3f6f9619d5a8bb1862c47d99">tinyxml2::XMLHandle</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aab516e699567f75cc9ab2ef2eee501e8">tinyxml2::XMLNode</a>
</li>
<li>ToNode()
: <a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a03ea6ec970a021b71bf1219a0f6717df">tinyxml2::XMLHandle</a>
</li>
<li>ToText()
: <a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a6ab9e8cbfb41417246e5657e3842c62a">tinyxml2::XMLHandle</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a41c55dab9162d1eb62db2008430e376b">tinyxml2::XMLNode</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_text.html#ab1213b4ddebe9b17ec7e7040e9f1caf7">tinyxml2::XMLText</a>
</li>
<li>ToUnknown()
: <a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#aa387368a1ad8d843a9f12df863d298de">tinyxml2::XMLHandle</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8675a74aa0ada6eccab0c77ef3e5b9bd">tinyxml2::XMLNode</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html#af4374856421921cad578c8affae872b6">tinyxml2::XMLUnknown</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

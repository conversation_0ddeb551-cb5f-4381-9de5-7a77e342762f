<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: tinyxml2::XMLDocument Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>tinyxml2</b></li><li class="navelem"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classtinyxml2_1_1_x_m_l_document-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">tinyxml2::XMLDocument Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for tinyxml2::XMLDocument:</div>
<div class="dyncontent">
 <div class="center">
  <img src="classtinyxml2_1_1_x_m_l_document.png" usemap="#tinyxml2::XMLDocument_map" alt=""/>
  <map id="tinyxml2::XMLDocument_map" name="tinyxml2::XMLDocument_map">
<area href="classtinyxml2_1_1_x_m_l_node.html" alt="tinyxml2::XMLNode" shape="rect" coords="0,0,146,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a57ddf17b6e054dda10af98991b1b8f70"><td class="memItemLeft" align="right" valign="top"><a id="a57ddf17b6e054dda10af98991b1b8f70"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a57ddf17b6e054dda10af98991b1b8f70">XMLDocument</a> (bool processEntities=true, Whitespace whitespaceMode=PRESERVE_WHITESPACE)</td></tr>
<tr class="memdesc:a57ddf17b6e054dda10af98991b1b8f70"><td class="mdescLeft">&#160;</td><td class="mdescRight">constructor <br /></td></tr>
<tr class="separator:a57ddf17b6e054dda10af98991b1b8f70"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3e185f880882bd978367bb55937735ec"><td class="memItemLeft" align="right" valign="top"><a id="a3e185f880882bd978367bb55937735ec"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a3e185f880882bd978367bb55937735ec">ToDocument</a> ()</td></tr>
<tr class="memdesc:a3e185f880882bd978367bb55937735ec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to a Document, or null. <br /></td></tr>
<tr class="separator:a3e185f880882bd978367bb55937735ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af2b616169e6517182f6725f2498e9a01"><td class="memItemLeft" align="right" valign="top">XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#af2b616169e6517182f6725f2498e9a01">Parse</a> (const char *xml, size_t nBytes=static_cast&lt; size_t &gt;(-1))</td></tr>
<tr class="separator:af2b616169e6517182f6725f2498e9a01"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2ebd4647a8af5fc6831b294ac26a150a"><td class="memItemLeft" align="right" valign="top">XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a2ebd4647a8af5fc6831b294ac26a150a">LoadFile</a> (const char *filename)</td></tr>
<tr class="separator:a2ebd4647a8af5fc6831b294ac26a150a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f1d330fad44c52f3d265338dd2a6dc2"><td class="memItemLeft" align="right" valign="top">XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a5f1d330fad44c52f3d265338dd2a6dc2">LoadFile</a> (FILE *)</td></tr>
<tr class="separator:a5f1d330fad44c52f3d265338dd2a6dc2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a73ac416b4a2aa0952e841220eb3da18f"><td class="memItemLeft" align="right" valign="top">XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a73ac416b4a2aa0952e841220eb3da18f">SaveFile</a> (const char *filename, bool compact=false)</td></tr>
<tr class="separator:a73ac416b4a2aa0952e841220eb3da18f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b95779479a0035acc67b3a61dfe1b74"><td class="memItemLeft" align="right" valign="top">XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a8b95779479a0035acc67b3a61dfe1b74">SaveFile</a> (FILE *fp, bool compact=false)</td></tr>
<tr class="separator:a8b95779479a0035acc67b3a61dfe1b74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a33fc5d159db873a179fa26338adb05bd"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a33fc5d159db873a179fa26338adb05bd">HasBOM</a> () const</td></tr>
<tr class="separator:a33fc5d159db873a179fa26338adb05bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a14419b698f7c4b140df4e80f3f0c93b0"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a14419b698f7c4b140df4e80f3f0c93b0">SetBOM</a> (bool useBOM)</td></tr>
<tr class="separator:a14419b698f7c4b140df4e80f3f0c93b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad2b70320d3c2a071c2f36928edff3e1c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#ad2b70320d3c2a071c2f36928edff3e1c">RootElement</a> ()</td></tr>
<tr class="separator:ad2b70320d3c2a071c2f36928edff3e1c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a867cf5fa3e3ff6ae4847a8b7ee8ec083"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a867cf5fa3e3ff6ae4847a8b7ee8ec083">Print</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">XMLPrinter</a> *streamer=0) const</td></tr>
<tr class="separator:a867cf5fa3e3ff6ae4847a8b7ee8ec083"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9efa54f7ecb37c17ab1fa2b3078ccca1"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a9efa54f7ecb37c17ab1fa2b3078ccca1">Accept</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a> *visitor) const</td></tr>
<tr class="separator:a9efa54f7ecb37c17ab1fa2b3078ccca1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8aa7817d4a1001364b06373763ab99d6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a8aa7817d4a1001364b06373763ab99d6">NewElement</a> (const char *name)</td></tr>
<tr class="separator:a8aa7817d4a1001364b06373763ab99d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ade4874bcb439954972ef2b3723ff3259"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#ade4874bcb439954972ef2b3723ff3259">NewComment</a> (const char *comment)</td></tr>
<tr class="separator:ade4874bcb439954972ef2b3723ff3259"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7e8b29ae4099092a8bb947da6361296"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#ab7e8b29ae4099092a8bb947da6361296">NewText</a> (const char *text)</td></tr>
<tr class="separator:ab7e8b29ae4099092a8bb947da6361296"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aee2eb3435923f5494dcc70ac225b60a2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#aee2eb3435923f5494dcc70ac225b60a2">NewDeclaration</a> (const char *text=0)</td></tr>
<tr class="separator:aee2eb3435923f5494dcc70ac225b60a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5385c937734ff6db9226ab707d2c7147"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a5385c937734ff6db9226ab707d2c7147">NewUnknown</a> (const char *text)</td></tr>
<tr class="separator:a5385c937734ff6db9226ab707d2c7147"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac1d6e2c7fcc1a660624ac4f68e96380d"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#ac1d6e2c7fcc1a660624ac4f68e96380d">DeleteNode</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *node)</td></tr>
<tr class="separator:ac1d6e2c7fcc1a660624ac4f68e96380d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4085d9c52f1d93214311459d6d1fcf17"><td class="memItemLeft" align="right" valign="top"><a id="a4085d9c52f1d93214311459d6d1fcf17"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a4085d9c52f1d93214311459d6d1fcf17">ClearError</a> ()</td></tr>
<tr class="memdesc:a4085d9c52f1d93214311459d6d1fcf17"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clears the error flags. <br /></td></tr>
<tr class="separator:a4085d9c52f1d93214311459d6d1fcf17"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a34e6318e182e40e3cc4f4ba5d59ed9ed"><td class="memItemLeft" align="right" valign="top"><a id="a34e6318e182e40e3cc4f4ba5d59ed9ed"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a34e6318e182e40e3cc4f4ba5d59ed9ed">Error</a> () const</td></tr>
<tr class="memdesc:a34e6318e182e40e3cc4f4ba5d59ed9ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return true if there was an error parsing the document. <br /></td></tr>
<tr class="separator:a34e6318e182e40e3cc4f4ba5d59ed9ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa3ed33b3107f920ec2b301f805ac17d"><td class="memItemLeft" align="right" valign="top"><a id="afa3ed33b3107f920ec2b301f805ac17d"></a>
XMLError&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#afa3ed33b3107f920ec2b301f805ac17d">ErrorID</a> () const</td></tr>
<tr class="memdesc:afa3ed33b3107f920ec2b301f805ac17d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the errorID. <br /></td></tr>
<tr class="separator:afa3ed33b3107f920ec2b301f805ac17d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad75aa9d32c4e8b300655186808aa9abf"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#ad75aa9d32c4e8b300655186808aa9abf">ErrorStr</a> () const</td></tr>
<tr class="separator:ad75aa9d32c4e8b300655186808aa9abf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d033945b42e125d933d6231e4571552"><td class="memItemLeft" align="right" valign="top"><a id="a1d033945b42e125d933d6231e4571552"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a1d033945b42e125d933d6231e4571552">PrintError</a> () const</td></tr>
<tr class="memdesc:a1d033945b42e125d933d6231e4571552"><td class="mdescLeft">&#160;</td><td class="mdescRight">A (trivial) utility function that prints the <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#ad75aa9d32c4e8b300655186808aa9abf">ErrorStr()</a> to stdout. <br /></td></tr>
<tr class="separator:a1d033945b42e125d933d6231e4571552"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a57400f816dbe7799ece33615ead9ab76"><td class="memItemLeft" align="right" valign="top"><a id="a57400f816dbe7799ece33615ead9ab76"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a57400f816dbe7799ece33615ead9ab76">ErrorLineNum</a> () const</td></tr>
<tr class="memdesc:a57400f816dbe7799ece33615ead9ab76"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the line where the error occurred, or zero if unknown. <br /></td></tr>
<tr class="separator:a57400f816dbe7799ece33615ead9ab76"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a65656b0b2cbc822708eb351504178aaf"><td class="memItemLeft" align="right" valign="top"><a id="a65656b0b2cbc822708eb351504178aaf"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a65656b0b2cbc822708eb351504178aaf">Clear</a> ()</td></tr>
<tr class="memdesc:a65656b0b2cbc822708eb351504178aaf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clear the document, resetting it to the initial state. <br /></td></tr>
<tr class="separator:a65656b0b2cbc822708eb351504178aaf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af592ffc91514e25a39664521ac83db45"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#af592ffc91514e25a39664521ac83db45">DeepCopy</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *target) const</td></tr>
<tr class="separator:af592ffc91514e25a39664521ac83db45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa37cc1709d7e1e988bc17dcfb24a69b8"><td class="memItemLeft" align="right" valign="top">virtual <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#aa37cc1709d7e1e988bc17dcfb24a69b8">ShallowClone</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *) const</td></tr>
<tr class="separator:aa37cc1709d7e1e988bc17dcfb24a69b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6fe5ef18699091844fcf64b56ffa5bf9"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a6fe5ef18699091844fcf64b56ffa5bf9">ShallowEqual</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *) const</td></tr>
<tr class="separator:a6fe5ef18699091844fcf64b56ffa5bf9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_classtinyxml2_1_1_x_m_l_node"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_classtinyxml2_1_1_x_m_l_node')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td></tr>
<tr class="memitem:a2de84cfa4ec3fe249bad745069d145f1 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a2de84cfa4ec3fe249bad745069d145f1"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a2de84cfa4ec3fe249bad745069d145f1">GetDocument</a> () const</td></tr>
<tr class="memdesc:a2de84cfa4ec3fe249bad745069d145f1 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> that owns this <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>. <br /></td></tr>
<tr class="separator:a2de84cfa4ec3fe249bad745069d145f1 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af343d1ef0b45c0020e62d784d7e67a68 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="af343d1ef0b45c0020e62d784d7e67a68"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#af343d1ef0b45c0020e62d784d7e67a68">GetDocument</a> ()</td></tr>
<tr class="memdesc:af343d1ef0b45c0020e62d784d7e67a68 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> that owns this <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>. <br /></td></tr>
<tr class="separator:af343d1ef0b45c0020e62d784d7e67a68 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab516e699567f75cc9ab2ef2eee501e8 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="aab516e699567f75cc9ab2ef2eee501e8"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aab516e699567f75cc9ab2ef2eee501e8">ToElement</a> ()</td></tr>
<tr class="memdesc:aab516e699567f75cc9ab2ef2eee501e8 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to an Element, or null. <br /></td></tr>
<tr class="separator:aab516e699567f75cc9ab2ef2eee501e8 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41c55dab9162d1eb62db2008430e376b inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a41c55dab9162d1eb62db2008430e376b"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a41c55dab9162d1eb62db2008430e376b">ToText</a> ()</td></tr>
<tr class="memdesc:a41c55dab9162d1eb62db2008430e376b inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to Text, or null. <br /></td></tr>
<tr class="separator:a41c55dab9162d1eb62db2008430e376b inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff47671055aa99840a1c1ebd661e63e3 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="aff47671055aa99840a1c1ebd661e63e3"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aff47671055aa99840a1c1ebd661e63e3">ToComment</a> ()</td></tr>
<tr class="memdesc:aff47671055aa99840a1c1ebd661e63e3 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to a Comment, or null. <br /></td></tr>
<tr class="separator:aff47671055aa99840a1c1ebd661e63e3 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a174fd4c22c010b58138c1b84a0dfbd51 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a174fd4c22c010b58138c1b84a0dfbd51"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a174fd4c22c010b58138c1b84a0dfbd51">ToDeclaration</a> ()</td></tr>
<tr class="memdesc:a174fd4c22c010b58138c1b84a0dfbd51 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to a Declaration, or null. <br /></td></tr>
<tr class="separator:a174fd4c22c010b58138c1b84a0dfbd51 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8675a74aa0ada6eccab0c77ef3e5b9bd inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a8675a74aa0ada6eccab0c77ef3e5b9bd"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8675a74aa0ada6eccab0c77ef3e5b9bd">ToUnknown</a> ()</td></tr>
<tr class="memdesc:a8675a74aa0ada6eccab0c77ef3e5b9bd inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to an Unknown, or null. <br /></td></tr>
<tr class="separator:a8675a74aa0ada6eccab0c77ef3e5b9bd inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66344989a4b436155bcda72bd6b07b82 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a66344989a4b436155bcda72bd6b07b82">Value</a> () const</td></tr>
<tr class="separator:a66344989a4b436155bcda72bd6b07b82 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09dd68cf9eae137579f6e50f36487513 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a09dd68cf9eae137579f6e50f36487513">SetValue</a> (const char *val, bool staticMem=false)</td></tr>
<tr class="separator:a09dd68cf9eae137579f6e50f36487513 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b5fc636646fda761d342c72e91cb286 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a9b5fc636646fda761d342c72e91cb286"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a9b5fc636646fda761d342c72e91cb286">GetLineNum</a> () const</td></tr>
<tr class="memdesc:a9b5fc636646fda761d342c72e91cb286 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the line number the node is in, if the document was parsed from a file. <br /></td></tr>
<tr class="separator:a9b5fc636646fda761d342c72e91cb286 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0f62bc186c56c2e0483ebd52dbfbe34 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="ae0f62bc186c56c2e0483ebd52dbfbe34"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ae0f62bc186c56c2e0483ebd52dbfbe34">Parent</a> () const</td></tr>
<tr class="memdesc:ae0f62bc186c56c2e0483ebd52dbfbe34 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the parent of this node on the DOM. <br /></td></tr>
<tr class="separator:ae0f62bc186c56c2e0483ebd52dbfbe34 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3ab489e6e202a3cd1762d3b332e89d4 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="ac3ab489e6e202a3cd1762d3b332e89d4"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ac3ab489e6e202a3cd1762d3b332e89d4">NoChildren</a> () const</td></tr>
<tr class="memdesc:ac3ab489e6e202a3cd1762d3b332e89d4 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if this node has no children. <br /></td></tr>
<tr class="separator:ac3ab489e6e202a3cd1762d3b332e89d4 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae7dc225e1018cdd685f7563593a1fe08 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="ae7dc225e1018cdd685f7563593a1fe08"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ae7dc225e1018cdd685f7563593a1fe08">FirstChild</a> () const</td></tr>
<tr class="memdesc:ae7dc225e1018cdd685f7563593a1fe08 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the first child node, or null if none exists. <br /></td></tr>
<tr class="separator:ae7dc225e1018cdd685f7563593a1fe08 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1795a35852dc8aae877cc8ded986e59b inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a1795a35852dc8aae877cc8ded986e59b">FirstChildElement</a> (const char *name=0) const</td></tr>
<tr class="separator:a1795a35852dc8aae877cc8ded986e59b inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b8583a277e8e26f4cbbb5492786778e inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a9b8583a277e8e26f4cbbb5492786778e"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a9b8583a277e8e26f4cbbb5492786778e">LastChild</a> () const</td></tr>
<tr class="memdesc:a9b8583a277e8e26f4cbbb5492786778e inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the last child node, or null if none exists. <br /></td></tr>
<tr class="separator:a9b8583a277e8e26f4cbbb5492786778e inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a173e9d1341bc56992e2d320a35936551 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a173e9d1341bc56992e2d320a35936551">LastChildElement</a> (const char *name=0) const</td></tr>
<tr class="separator:a173e9d1341bc56992e2d320a35936551 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac667c513d445f8b783e1e15ef9d3551 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="aac667c513d445f8b783e1e15ef9d3551"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aac667c513d445f8b783e1e15ef9d3551">PreviousSibling</a> () const</td></tr>
<tr class="memdesc:aac667c513d445f8b783e1e15ef9d3551 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the previous (left) sibling node of this node. <br /></td></tr>
<tr class="separator:aac667c513d445f8b783e1e15ef9d3551 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a872936cae46fb473eb47fec99129fc70 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a872936cae46fb473eb47fec99129fc70"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a872936cae46fb473eb47fec99129fc70">PreviousSiblingElement</a> (const char *name=0) const</td></tr>
<tr class="memdesc:a872936cae46fb473eb47fec99129fc70 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the previous (left) sibling element of this node, with an optionally supplied name. <br /></td></tr>
<tr class="separator:a872936cae46fb473eb47fec99129fc70 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79db9ef0fe014d27790f2218b87bcbb5 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a79db9ef0fe014d27790f2218b87bcbb5"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a79db9ef0fe014d27790f2218b87bcbb5">NextSibling</a> () const</td></tr>
<tr class="memdesc:a79db9ef0fe014d27790f2218b87bcbb5 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the next (right) sibling node of this node. <br /></td></tr>
<tr class="separator:a79db9ef0fe014d27790f2218b87bcbb5 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1264c86233328f0cd36297552d982f80 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a id="a1264c86233328f0cd36297552d982f80"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a1264c86233328f0cd36297552d982f80">NextSiblingElement</a> (const char *name=0) const</td></tr>
<tr class="memdesc:a1264c86233328f0cd36297552d982f80 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the next (right) sibling element of this node, with an optionally supplied name. <br /></td></tr>
<tr class="separator:a1264c86233328f0cd36297552d982f80 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeb249ed60f4e8bfad3709151c3ee4286 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aeb249ed60f4e8bfad3709151c3ee4286">InsertEndChild</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *addThis)</td></tr>
<tr class="separator:aeb249ed60f4e8bfad3709151c3ee4286 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8ff7dc071f3a1a6ae2ac25a37492865d inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8ff7dc071f3a1a6ae2ac25a37492865d">InsertFirstChild</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *addThis)</td></tr>
<tr class="separator:a8ff7dc071f3a1a6ae2ac25a37492865d inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a85adb8f0b7477eec30f9a41d420b09c2 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a85adb8f0b7477eec30f9a41d420b09c2">InsertAfterChild</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *afterThis, <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *addThis)</td></tr>
<tr class="separator:a85adb8f0b7477eec30f9a41d420b09c2 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0360085cc54df5bff85d5c5da13afdce inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a0360085cc54df5bff85d5c5da13afdce">DeleteChildren</a> ()</td></tr>
<tr class="separator:a0360085cc54df5bff85d5c5da13afdce inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a363b6edbd6ebd55f8387d2b89f2b0921 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a363b6edbd6ebd55f8387d2b89f2b0921">DeleteChild</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *node)</td></tr>
<tr class="separator:a363b6edbd6ebd55f8387d2b89f2b0921 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62c71b6bf8734b5424063b8d9a61c266 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a62c71b6bf8734b5424063b8d9a61c266">DeepClone</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *target) const</td></tr>
<tr class="separator:a62c71b6bf8734b5424063b8d9a61c266 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a002978fc889cc011d143185f2377eca2 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a002978fc889cc011d143185f2377eca2">SetUserData</a> (void *userData)</td></tr>
<tr class="separator:a002978fc889cc011d143185f2377eca2 inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f0687574afa03bc479dc44f29db0afe inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a7f0687574afa03bc479dc44f29db0afe">GetUserData</a> () const</td></tr>
<tr class="separator:a7f0687574afa03bc479dc44f29db0afe inherit pub_methods_classtinyxml2_1_1_x_m_l_node"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>A Document binds together all the functionality. It can be saved, loaded, and printed to the screen. All Nodes are connected and allocated to a Document. If the Document is deleted, all its Nodes are also deleted. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a9efa54f7ecb37c17ab1fa2b3078ccca1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9efa54f7ecb37c17ab1fa2b3078ccca1">&#9670;&nbsp;</a></span>Accept()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool tinyxml2::XMLDocument::Accept </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a> *&#160;</td>
          <td class="paramname"><em>visitor</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Accept a hierarchical visit of the nodes in the TinyXML-2 DOM. Every node in the XML tree will be conditionally visited and the host will be called back via the <a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a> interface.</p>
<p>This is essentially a SAX interface for TinyXML-2. (Note however it doesn't re-parse the XML for the callbacks, so the performance of TinyXML-2 is unchanged by using this interface versus any other.)</p>
<p>The interface has been based on ideas from:</p>
<ul>
<li><a href="http://www.saxproject.org/">http://www.saxproject.org/</a></li>
<li><a href="http://c2.com/cgi/wiki?HierarchicalVisitorPattern">http://c2.com/cgi/wiki?HierarchicalVisitorPattern</a></li>
</ul>
<p>Which are both good references for "visiting".</p>
<p>An example of using <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a9efa54f7ecb37c17ab1fa2b3078ccca1">Accept()</a>: </p><pre class="fragment">XMLPrinter printer;
tinyxmlDoc.Accept( &amp;printer );
const char* xmlcstr = printer.CStr();
</pre> 
<p>Implements <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a81e66df0a44c67a7af17f3b77a152785">tinyxml2::XMLNode</a>.</p>

</div>
</div>
<a id="af592ffc91514e25a39664521ac83db45"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af592ffc91514e25a39664521ac83db45">&#9670;&nbsp;</a></span>DeepCopy()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void tinyxml2::XMLDocument::DeepCopy </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td>
          <td class="paramname"><em>target</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Copies this document to a target document. The target will be completely cleared before the copy. If you want to copy a sub-tree, see <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a62c71b6bf8734b5424063b8d9a61c266">XMLNode::DeepClone()</a>.</p>
<p>NOTE: that the 'target' must be non-null. </p>

</div>
</div>
<a id="ac1d6e2c7fcc1a660624ac4f68e96380d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac1d6e2c7fcc1a660624ac4f68e96380d">&#9670;&nbsp;</a></span>DeleteNode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void tinyxml2::XMLDocument::DeleteNode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td>
          <td class="paramname"><em>node</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Delete a node associated with this document. It will be unlinked from the DOM. </p>

</div>
</div>
<a id="ad75aa9d32c4e8b300655186808aa9abf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad75aa9d32c4e8b300655186808aa9abf">&#9670;&nbsp;</a></span>ErrorStr()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* tinyxml2::XMLDocument::ErrorStr </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns a "long form" error description. A hopefully helpful diagnostic with location, line number, and/or additional info. </p>

</div>
</div>
<a id="a33fc5d159db873a179fa26338adb05bd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a33fc5d159db873a179fa26338adb05bd">&#9670;&nbsp;</a></span>HasBOM()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool tinyxml2::XMLDocument::HasBOM </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns true if this document has a leading Byte Order Mark of UTF8. </p>

</div>
</div>
<a id="a2ebd4647a8af5fc6831b294ac26a150a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2ebd4647a8af5fc6831b294ac26a150a">&#9670;&nbsp;</a></span>LoadFile() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">XMLError tinyxml2::XMLDocument::LoadFile </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>filename</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Load an XML file from disk. Returns XML_SUCCESS (0) on success, or an errorID. </p>

</div>
</div>
<a id="a5f1d330fad44c52f3d265338dd2a6dc2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5f1d330fad44c52f3d265338dd2a6dc2">&#9670;&nbsp;</a></span>LoadFile() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">XMLError tinyxml2::XMLDocument::LoadFile </td>
          <td>(</td>
          <td class="paramtype">FILE *&#160;</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Load an XML file from disk. You are responsible for providing and closing the FILE*.</p>
<p>NOTE: The file should be opened as binary ("rb") not text in order for TinyXML-2 to correctly do newline normalization.</p>
<p>Returns XML_SUCCESS (0) on success, or an errorID. </p>

</div>
</div>
<a id="ade4874bcb439954972ef2b3723ff3259"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ade4874bcb439954972ef2b3723ff3259">&#9670;&nbsp;</a></span>NewComment()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a>* tinyxml2::XMLDocument::NewComment </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>comment</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Create a new Comment associated with this Document. The memory for the Comment is managed by the Document. </p>

</div>
</div>
<a id="aee2eb3435923f5494dcc70ac225b60a2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aee2eb3435923f5494dcc70ac225b60a2">&#9670;&nbsp;</a></span>NewDeclaration()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>* tinyxml2::XMLDocument::NewDeclaration </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>text</em> = <code>0</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Create a new Declaration associated with this Document. The memory for the object is managed by the Document.</p>
<p>If the 'text' param is null, the standard declaration is used.: </p><pre class="fragment">    &lt;?xml version="1.0" encoding="UTF-8"?&gt;
</pre> 
</div>
</div>
<a id="a8aa7817d4a1001364b06373763ab99d6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8aa7817d4a1001364b06373763ab99d6">&#9670;&nbsp;</a></span>NewElement()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* tinyxml2::XMLDocument::NewElement </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>name</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Create a new Element associated with this Document. The memory for the Element is managed by the Document. </p>

</div>
</div>
<a id="ab7e8b29ae4099092a8bb947da6361296"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab7e8b29ae4099092a8bb947da6361296">&#9670;&nbsp;</a></span>NewText()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>* tinyxml2::XMLDocument::NewText </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>text</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Create a new Text associated with this Document. The memory for the Text is managed by the Document. </p>

</div>
</div>
<a id="a5385c937734ff6db9226ab707d2c7147"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5385c937734ff6db9226ab707d2c7147">&#9670;&nbsp;</a></span>NewUnknown()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>* tinyxml2::XMLDocument::NewUnknown </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>text</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Create a new Unknown associated with this Document. The memory for the object is managed by the Document. </p>

</div>
</div>
<a id="af2b616169e6517182f6725f2498e9a01"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af2b616169e6517182f6725f2498e9a01">&#9670;&nbsp;</a></span>Parse()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">XMLError tinyxml2::XMLDocument::Parse </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>xml</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&#160;</td>
          <td class="paramname"><em>nBytes</em> = <code>static_cast&lt;&#160;size_t&#160;&gt;(-1)</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Parse an XML file from a character string. Returns XML_SUCCESS (0) on success, or an errorID.</p>
<p>You may optionally pass in the 'nBytes', which is the number of bytes which will be parsed. If not specified, TinyXML-2 will assume 'xml' points to a null terminated string. </p>

</div>
</div>
<a id="a867cf5fa3e3ff6ae4847a8b7ee8ec083"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a867cf5fa3e3ff6ae4847a8b7ee8ec083">&#9670;&nbsp;</a></span>Print()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void tinyxml2::XMLDocument::Print </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">XMLPrinter</a> *&#160;</td>
          <td class="paramname"><em>streamer</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Print the Document. If the Printer is not provided, it will print to stdout. If you provide Printer, this can print to a file: </p><pre class="fragment">XMLPrinter printer( fp );
doc.Print( &amp;printer );
</pre><p>Or you can use a printer to print to memory: </p><pre class="fragment">XMLPrinter printer;
doc.Print( &amp;printer );
// printer.CStr() has a const char* to the XML
</pre> 
</div>
</div>
<a id="ad2b70320d3c2a071c2f36928edff3e1c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad2b70320d3c2a071c2f36928edff3e1c">&#9670;&nbsp;</a></span>RootElement()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* tinyxml2::XMLDocument::RootElement </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Return the root element of DOM. Equivalent to <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a1795a35852dc8aae877cc8ded986e59b">FirstChildElement()</a>. To get the first node, use FirstChild(). </p>

</div>
</div>
<a id="a73ac416b4a2aa0952e841220eb3da18f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a73ac416b4a2aa0952e841220eb3da18f">&#9670;&nbsp;</a></span>SaveFile() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">XMLError tinyxml2::XMLDocument::SaveFile </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>filename</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>compact</em> = <code>false</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Save the XML file to disk. Returns XML_SUCCESS (0) on success, or an errorID. </p>

</div>
</div>
<a id="a8b95779479a0035acc67b3a61dfe1b74"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8b95779479a0035acc67b3a61dfe1b74">&#9670;&nbsp;</a></span>SaveFile() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">XMLError tinyxml2::XMLDocument::SaveFile </td>
          <td>(</td>
          <td class="paramtype">FILE *&#160;</td>
          <td class="paramname"><em>fp</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>compact</em> = <code>false</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Save the XML file to disk. You are responsible for providing and closing the FILE*.</p>
<p>Returns XML_SUCCESS (0) on success, or an errorID. </p>

</div>
</div>
<a id="a14419b698f7c4b140df4e80f3f0c93b0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a14419b698f7c4b140df4e80f3f0c93b0">&#9670;&nbsp;</a></span>SetBOM()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void tinyxml2::XMLDocument::SetBOM </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>useBOM</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sets whether to write the BOM when writing the file. </p>

</div>
</div>
<a id="aa37cc1709d7e1e988bc17dcfb24a69b8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa37cc1709d7e1e988bc17dcfb24a69b8">&#9670;&nbsp;</a></span>ShallowClone()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* tinyxml2::XMLDocument::ShallowClone </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td>
          <td class="paramname"><em>document</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Make a copy of this node, but not its children. You may pass in a Document pointer that will be the owner of the new Node. If the 'document' is null, then the node returned will be allocated from the current Document. (this-&gt;<a class="el" href="classtinyxml2_1_1_x_m_l_node.html#af343d1ef0b45c0020e62d784d7e67a68" title="Get the XMLDocument that owns this XMLNode.">GetDocument()</a>)</p>
<p>Note: if called on a <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>, this will return null. </p>

<p>Implements <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8402cbd3129d20e9e6024bbcc0531283">tinyxml2::XMLNode</a>.</p>

</div>
</div>
<a id="a6fe5ef18699091844fcf64b56ffa5bf9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6fe5ef18699091844fcf64b56ffa5bf9">&#9670;&nbsp;</a></span>ShallowEqual()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool tinyxml2::XMLDocument::ShallowEqual </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td>
          <td class="paramname"><em>compare</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Test if 2 nodes are the same, but don't test children. The 2 nodes do not need to be in the same Document.</p>
<p>Note: if called on a <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>, this will return false. </p>

<p>Implements <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a7ce18b751c3ea09eac292dca264f9226">tinyxml2::XMLNode</a>.</p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

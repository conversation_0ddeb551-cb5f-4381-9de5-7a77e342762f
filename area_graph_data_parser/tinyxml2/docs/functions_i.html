<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_i"></a>- i -</h3><ul>
<li>InsertAfterChild()
: <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a85adb8f0b7477eec30f9a41d420b09c2">tinyxml2::XMLNode</a>
</li>
<li>InsertEndChild()
: <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aeb249ed60f4e8bfad3709151c3ee4286">tinyxml2::XMLNode</a>
</li>
<li>InsertFirstChild()
: <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8ff7dc071f3a1a6ae2ac25a37492865d">tinyxml2::XMLNode</a>
</li>
<li>InsertNewChildElement()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#abc9506eff9780f666f49dc3d5e5cae13">tinyxml2::XMLElement</a>
</li>
<li>InsertNewComment()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ae4f2c2e781b8dc030411d84cd20fa46d">tinyxml2::XMLElement</a>
</li>
<li>InsertNewDeclaration()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#adec237e788b50c4ed73c918a166adde6">tinyxml2::XMLElement</a>
</li>
<li>InsertNewText()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a189e155810fc9fdd4da1409cbadee187">tinyxml2::XMLElement</a>
</li>
<li>InsertNewUnknown()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#acaa5fe3957760e68185006965e2c11c2">tinyxml2::XMLElement</a>
</li>
<li>Int64Attribute()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a66d96972adecd816194191f13cc4a0a0">tinyxml2::XMLElement</a>
</li>
<li>Int64Text()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#aab6151f7e3b4c2c0a8234e262d7b6b8a">tinyxml2::XMLElement</a>
</li>
<li>IntAttribute()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a95a89b13bb14a2d4655e2b5b406c00d4">tinyxml2::XMLElement</a>
</li>
<li>IntValue()
: <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#adfa2433f0fdafd5c3880936de9affa80">tinyxml2::XMLAttribute</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

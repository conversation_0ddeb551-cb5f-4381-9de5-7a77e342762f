<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: Read attributes and text information.</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="PageDoc"><div class="header">
  <div class="headertitle">
<div class="title">Read attributes and text information. </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p></p>
<p>There are fundamentally 2 ways of writing a key-value pair into an XML file. (Something that's always annoyed me about XML.) Either by using attributes, or by writing the key name into an element and the value into the text node wrapped by the element. Both approaches are illustrated in this example, which shows two ways to encode the value "2" into the key "v":</p>
<div class="fragment"><div class="line"><span class="keywordtype">bool</span> example_4()</div>
<div class="line">{</div>
<div class="line">    <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span>* xml =</div>
<div class="line">        <span class="stringliteral">&quot;&lt;information&gt;&quot;</span></div>
<div class="line">        <span class="stringliteral">&quot;   &lt;attributeApproach v=&#39;2&#39; /&gt;&quot;</span></div>
<div class="line">        <span class="stringliteral">&quot;   &lt;textApproach&gt;&quot;</span></div>
<div class="line">        <span class="stringliteral">&quot;       &lt;v&gt;2&lt;/v&gt;&quot;</span></div>
<div class="line">        <span class="stringliteral">&quot;   &lt;/textApproach&gt;&quot;</span></div>
<div class="line">        <span class="stringliteral">&quot;&lt;/information&gt;&quot;</span>;</div>
</div><!-- fragment --><p> TinyXML-2 has accessors for both approaches.</p>
<p>When using an attribute, you navigate to the XMLElement with that attribute and use the QueryIntAttribute() group of methods. (Also QueryFloatAttribute(), etc.)</p>
<div class="fragment"><div class="line">    XMLElement* attributeApproachElement = doc.FirstChildElement()-&gt;FirstChildElement( <span class="stringliteral">&quot;attributeApproach&quot;</span> );</div>
<div class="line">    attributeApproachElement-&gt;QueryIntAttribute( <span class="stringliteral">&quot;v&quot;</span>, &amp;v0 );</div>
</div><!-- fragment --><p> When using the text approach, you need to navigate down one more step to the XMLElement that contains the text. Note the extra FirstChildElement( "v" ) in the code below. The value of the text can then be safely queried with the QueryIntText() group of methods. (Also QueryFloatText(), etc.)</p>
<div class="fragment"><div class="line">    XMLElement* textApproachElement = doc.FirstChildElement()-&gt;FirstChildElement( <span class="stringliteral">&quot;textApproach&quot;</span> );</div>
<div class="line">    textApproachElement-&gt;FirstChildElement( <span class="stringliteral">&quot;v&quot;</span> )-&gt;QueryIntText( &amp;v1 );</div>
</div><!-- fragment --></div></div><!-- contents -->
</div><!-- PageDoc -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

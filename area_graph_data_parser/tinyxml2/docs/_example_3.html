<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: Get information out of XML</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="PageDoc"><div class="header">
  <div class="headertitle">
<div class="title">Get information out of XML </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p> In this example, we navigate a simple XML file, and read some interesting text. Note that this example doesn't use error checking; working code should check for null pointers when walking an XML tree, or use XMLHandle.</p>
<p>(The XML is an excerpt from "dream.xml").</p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> example_3()</div>
<div class="line">{</div>
<div class="line">    <span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span>* xml =</div>
<div class="line">        <span class="stringliteral">&quot;&lt;?xml version=\&quot;1.0\&quot;?&gt;&quot;</span></div>
<div class="line">        <span class="stringliteral">&quot;&lt;!DOCTYPE PLAY SYSTEM \&quot;play.dtd\&quot;&gt;&quot;</span></div>
<div class="line">        <span class="stringliteral">&quot;&lt;PLAY&gt;&quot;</span></div>
<div class="line">        <span class="stringliteral">&quot;&lt;TITLE&gt;A Midsummer Night&#39;s Dream&lt;/TITLE&gt;&quot;</span></div>
<div class="line">        <span class="stringliteral">&quot;&lt;/PLAY&gt;&quot;</span>;</div>
</div><!-- fragment --><p> The structure of the XML file is:</p>
<ul>
<li>
(declaration) </li>
<li>
(dtd stuff) </li>
<li>
Element "PLAY" <ul>
<li>
Element "TITLE" <ul>
<li>
Text "A Midsummer Night's Dream" </li>
</ul>
</li>
</ul>
</li>
</ul>
<p>For this example, we want to print out the title of the play. The text of the title (what we want) is child of the "TITLE" element which is a child of the "PLAY" element.</p>
<p>We want to skip the declaration and dtd, so the method FirstChildElement() is a good choice. The FirstChildElement() of the Document is the "PLAY" Element, the FirstChildElement() of the "PLAY" Element is the "TITLE" Element.</p>
<div class="fragment"><div class="line"> </div>
<div class="line">    XMLDocument doc;</div>
<div class="line">    doc.Parse( xml );</div>
<div class="line"> </div>
<div class="line">    XMLElement* titleElement = doc.FirstChildElement( <span class="stringliteral">&quot;PLAY&quot;</span> )-&gt;FirstChildElement( <span class="stringliteral">&quot;TITLE&quot;</span> );</div>
</div><!-- fragment --><p> We can then use the convenience function GetText() to get the title of the play.</p>
<div class="fragment"><div class="line">    <span class="keyword">const</span> <span class="keywordtype">char</span>* title = titleElement-&gt;GetText();</div>
<div class="line">    printf( <span class="stringliteral">&quot;Name of play (1): %s\n&quot;</span>, title );</div>
</div><!-- fragment --><p> Text is just another Node in the XML DOM. And in fact you should be a little cautious with it, as text nodes can contain elements.</p>
<pre class="fragment">Consider: A Midsummer Night's &lt;b&gt;Dream&lt;/b&gt;
</pre><p>It is more correct to actually query the Text Node if in doubt:</p>
<div class="fragment"><div class="line"> </div>
<div class="line">    XMLText* textNode = titleElement-&gt;FirstChild()-&gt;ToText();</div>
<div class="line">    title = textNode-&gt;Value();</div>
<div class="line">    printf( <span class="stringliteral">&quot;Name of play (2): %s\n&quot;</span>, title );</div>
</div><!-- fragment --><p> Noting that here we use FirstChild() since we are looking for XMLText, not an element, and ToText() is a cast from a Node to a XMLText. </p>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

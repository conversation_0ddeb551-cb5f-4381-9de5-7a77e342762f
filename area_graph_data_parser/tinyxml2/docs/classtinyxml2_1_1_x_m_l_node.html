<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: tinyxml2::XMLNode Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>tinyxml2</b></li><li class="navelem"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classtinyxml2_1_1_x_m_l_node-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">tinyxml2::XMLNode Class Reference<span class="mlabels"><span class="mlabel">abstract</span></span></div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for tinyxml2::XMLNode:</div>
<div class="dyncontent">
 <div class="center">
  <img src="classtinyxml2_1_1_x_m_l_node.png" usemap="#tinyxml2::XMLNode_map" alt=""/>
  <map id="tinyxml2::XMLNode_map" name="tinyxml2::XMLNode_map">
<area href="classtinyxml2_1_1_x_m_l_comment.html" alt="tinyxml2::XMLComment" shape="rect" coords="0,56,155,80"/>
<area href="classtinyxml2_1_1_x_m_l_declaration.html" alt="tinyxml2::XMLDeclaration" shape="rect" coords="165,56,320,80"/>
<area href="classtinyxml2_1_1_x_m_l_document.html" alt="tinyxml2::XMLDocument" shape="rect" coords="330,56,485,80"/>
<area href="classtinyxml2_1_1_x_m_l_element.html" alt="tinyxml2::XMLElement" shape="rect" coords="495,56,650,80"/>
<area href="classtinyxml2_1_1_x_m_l_text.html" alt="tinyxml2::XMLText" shape="rect" coords="660,56,815,80"/>
<area href="classtinyxml2_1_1_x_m_l_unknown.html" alt="tinyxml2::XMLUnknown" shape="rect" coords="825,56,980,80"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a2de84cfa4ec3fe249bad745069d145f1"><td class="memItemLeft" align="right" valign="top"><a id="a2de84cfa4ec3fe249bad745069d145f1"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a2de84cfa4ec3fe249bad745069d145f1">GetDocument</a> () const</td></tr>
<tr class="memdesc:a2de84cfa4ec3fe249bad745069d145f1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> that owns this <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>. <br /></td></tr>
<tr class="separator:a2de84cfa4ec3fe249bad745069d145f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af343d1ef0b45c0020e62d784d7e67a68"><td class="memItemLeft" align="right" valign="top"><a id="af343d1ef0b45c0020e62d784d7e67a68"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#af343d1ef0b45c0020e62d784d7e67a68">GetDocument</a> ()</td></tr>
<tr class="memdesc:af343d1ef0b45c0020e62d784d7e67a68"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> that owns this <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>. <br /></td></tr>
<tr class="separator:af343d1ef0b45c0020e62d784d7e67a68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab516e699567f75cc9ab2ef2eee501e8"><td class="memItemLeft" align="right" valign="top"><a id="aab516e699567f75cc9ab2ef2eee501e8"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aab516e699567f75cc9ab2ef2eee501e8">ToElement</a> ()</td></tr>
<tr class="memdesc:aab516e699567f75cc9ab2ef2eee501e8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to an Element, or null. <br /></td></tr>
<tr class="separator:aab516e699567f75cc9ab2ef2eee501e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41c55dab9162d1eb62db2008430e376b"><td class="memItemLeft" align="right" valign="top"><a id="a41c55dab9162d1eb62db2008430e376b"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a41c55dab9162d1eb62db2008430e376b">ToText</a> ()</td></tr>
<tr class="memdesc:a41c55dab9162d1eb62db2008430e376b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to Text, or null. <br /></td></tr>
<tr class="separator:a41c55dab9162d1eb62db2008430e376b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff47671055aa99840a1c1ebd661e63e3"><td class="memItemLeft" align="right" valign="top"><a id="aff47671055aa99840a1c1ebd661e63e3"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_comment.html">XMLComment</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aff47671055aa99840a1c1ebd661e63e3">ToComment</a> ()</td></tr>
<tr class="memdesc:aff47671055aa99840a1c1ebd661e63e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to a Comment, or null. <br /></td></tr>
<tr class="separator:aff47671055aa99840a1c1ebd661e63e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a836e2966ed736fc3c94f70e12a2a3357"><td class="memItemLeft" align="right" valign="top"><a id="a836e2966ed736fc3c94f70e12a2a3357"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a836e2966ed736fc3c94f70e12a2a3357">ToDocument</a> ()</td></tr>
<tr class="memdesc:a836e2966ed736fc3c94f70e12a2a3357"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to a Document, or null. <br /></td></tr>
<tr class="separator:a836e2966ed736fc3c94f70e12a2a3357"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a174fd4c22c010b58138c1b84a0dfbd51"><td class="memItemLeft" align="right" valign="top"><a id="a174fd4c22c010b58138c1b84a0dfbd51"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a174fd4c22c010b58138c1b84a0dfbd51">ToDeclaration</a> ()</td></tr>
<tr class="memdesc:a174fd4c22c010b58138c1b84a0dfbd51"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to a Declaration, or null. <br /></td></tr>
<tr class="separator:a174fd4c22c010b58138c1b84a0dfbd51"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8675a74aa0ada6eccab0c77ef3e5b9bd"><td class="memItemLeft" align="right" valign="top"><a id="a8675a74aa0ada6eccab0c77ef3e5b9bd"></a>
virtual <a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8675a74aa0ada6eccab0c77ef3e5b9bd">ToUnknown</a> ()</td></tr>
<tr class="memdesc:a8675a74aa0ada6eccab0c77ef3e5b9bd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely cast to an Unknown, or null. <br /></td></tr>
<tr class="separator:a8675a74aa0ada6eccab0c77ef3e5b9bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66344989a4b436155bcda72bd6b07b82"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a66344989a4b436155bcda72bd6b07b82">Value</a> () const</td></tr>
<tr class="separator:a66344989a4b436155bcda72bd6b07b82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09dd68cf9eae137579f6e50f36487513"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a09dd68cf9eae137579f6e50f36487513">SetValue</a> (const char *val, bool staticMem=false)</td></tr>
<tr class="separator:a09dd68cf9eae137579f6e50f36487513"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b5fc636646fda761d342c72e91cb286"><td class="memItemLeft" align="right" valign="top"><a id="a9b5fc636646fda761d342c72e91cb286"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a9b5fc636646fda761d342c72e91cb286">GetLineNum</a> () const</td></tr>
<tr class="memdesc:a9b5fc636646fda761d342c72e91cb286"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the line number the node is in, if the document was parsed from a file. <br /></td></tr>
<tr class="separator:a9b5fc636646fda761d342c72e91cb286"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0f62bc186c56c2e0483ebd52dbfbe34"><td class="memItemLeft" align="right" valign="top"><a id="ae0f62bc186c56c2e0483ebd52dbfbe34"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ae0f62bc186c56c2e0483ebd52dbfbe34">Parent</a> () const</td></tr>
<tr class="memdesc:ae0f62bc186c56c2e0483ebd52dbfbe34"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the parent of this node on the DOM. <br /></td></tr>
<tr class="separator:ae0f62bc186c56c2e0483ebd52dbfbe34"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3ab489e6e202a3cd1762d3b332e89d4"><td class="memItemLeft" align="right" valign="top"><a id="ac3ab489e6e202a3cd1762d3b332e89d4"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ac3ab489e6e202a3cd1762d3b332e89d4">NoChildren</a> () const</td></tr>
<tr class="memdesc:ac3ab489e6e202a3cd1762d3b332e89d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if this node has no children. <br /></td></tr>
<tr class="separator:ac3ab489e6e202a3cd1762d3b332e89d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae7dc225e1018cdd685f7563593a1fe08"><td class="memItemLeft" align="right" valign="top"><a id="ae7dc225e1018cdd685f7563593a1fe08"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ae7dc225e1018cdd685f7563593a1fe08">FirstChild</a> () const</td></tr>
<tr class="memdesc:ae7dc225e1018cdd685f7563593a1fe08"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the first child node, or null if none exists. <br /></td></tr>
<tr class="separator:ae7dc225e1018cdd685f7563593a1fe08"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1795a35852dc8aae877cc8ded986e59b"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a1795a35852dc8aae877cc8ded986e59b">FirstChildElement</a> (const char *name=0) const</td></tr>
<tr class="separator:a1795a35852dc8aae877cc8ded986e59b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b8583a277e8e26f4cbbb5492786778e"><td class="memItemLeft" align="right" valign="top"><a id="a9b8583a277e8e26f4cbbb5492786778e"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a9b8583a277e8e26f4cbbb5492786778e">LastChild</a> () const</td></tr>
<tr class="memdesc:a9b8583a277e8e26f4cbbb5492786778e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the last child node, or null if none exists. <br /></td></tr>
<tr class="separator:a9b8583a277e8e26f4cbbb5492786778e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a173e9d1341bc56992e2d320a35936551"><td class="memItemLeft" align="right" valign="top">const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a173e9d1341bc56992e2d320a35936551">LastChildElement</a> (const char *name=0) const</td></tr>
<tr class="separator:a173e9d1341bc56992e2d320a35936551"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac667c513d445f8b783e1e15ef9d3551"><td class="memItemLeft" align="right" valign="top"><a id="aac667c513d445f8b783e1e15ef9d3551"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aac667c513d445f8b783e1e15ef9d3551">PreviousSibling</a> () const</td></tr>
<tr class="memdesc:aac667c513d445f8b783e1e15ef9d3551"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the previous (left) sibling node of this node. <br /></td></tr>
<tr class="separator:aac667c513d445f8b783e1e15ef9d3551"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a872936cae46fb473eb47fec99129fc70"><td class="memItemLeft" align="right" valign="top"><a id="a872936cae46fb473eb47fec99129fc70"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a872936cae46fb473eb47fec99129fc70">PreviousSiblingElement</a> (const char *name=0) const</td></tr>
<tr class="memdesc:a872936cae46fb473eb47fec99129fc70"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the previous (left) sibling element of this node, with an optionally supplied name. <br /></td></tr>
<tr class="separator:a872936cae46fb473eb47fec99129fc70"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79db9ef0fe014d27790f2218b87bcbb5"><td class="memItemLeft" align="right" valign="top"><a id="a79db9ef0fe014d27790f2218b87bcbb5"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a79db9ef0fe014d27790f2218b87bcbb5">NextSibling</a> () const</td></tr>
<tr class="memdesc:a79db9ef0fe014d27790f2218b87bcbb5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the next (right) sibling node of this node. <br /></td></tr>
<tr class="separator:a79db9ef0fe014d27790f2218b87bcbb5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1264c86233328f0cd36297552d982f80"><td class="memItemLeft" align="right" valign="top"><a id="a1264c86233328f0cd36297552d982f80"></a>
const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a1264c86233328f0cd36297552d982f80">NextSiblingElement</a> (const char *name=0) const</td></tr>
<tr class="memdesc:a1264c86233328f0cd36297552d982f80"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the next (right) sibling element of this node, with an optionally supplied name. <br /></td></tr>
<tr class="separator:a1264c86233328f0cd36297552d982f80"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeb249ed60f4e8bfad3709151c3ee4286"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aeb249ed60f4e8bfad3709151c3ee4286">InsertEndChild</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *addThis)</td></tr>
<tr class="separator:aeb249ed60f4e8bfad3709151c3ee4286"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8ff7dc071f3a1a6ae2ac25a37492865d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8ff7dc071f3a1a6ae2ac25a37492865d">InsertFirstChild</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *addThis)</td></tr>
<tr class="separator:a8ff7dc071f3a1a6ae2ac25a37492865d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a85adb8f0b7477eec30f9a41d420b09c2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a85adb8f0b7477eec30f9a41d420b09c2">InsertAfterChild</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *afterThis, <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *addThis)</td></tr>
<tr class="separator:a85adb8f0b7477eec30f9a41d420b09c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0360085cc54df5bff85d5c5da13afdce"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a0360085cc54df5bff85d5c5da13afdce">DeleteChildren</a> ()</td></tr>
<tr class="separator:a0360085cc54df5bff85d5c5da13afdce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a363b6edbd6ebd55f8387d2b89f2b0921"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a363b6edbd6ebd55f8387d2b89f2b0921">DeleteChild</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *node)</td></tr>
<tr class="separator:a363b6edbd6ebd55f8387d2b89f2b0921"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8402cbd3129d20e9e6024bbcc0531283"><td class="memItemLeft" align="right" valign="top">virtual <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8402cbd3129d20e9e6024bbcc0531283">ShallowClone</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *document) const =0</td></tr>
<tr class="separator:a8402cbd3129d20e9e6024bbcc0531283"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62c71b6bf8734b5424063b8d9a61c266"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a62c71b6bf8734b5424063b8d9a61c266">DeepClone</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *target) const</td></tr>
<tr class="separator:a62c71b6bf8734b5424063b8d9a61c266"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ce18b751c3ea09eac292dca264f9226"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a7ce18b751c3ea09eac292dca264f9226">ShallowEqual</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *compare) const =0</td></tr>
<tr class="separator:a7ce18b751c3ea09eac292dca264f9226"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a81e66df0a44c67a7af17f3b77a152785"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a81e66df0a44c67a7af17f3b77a152785">Accept</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a> *visitor) const =0</td></tr>
<tr class="separator:a81e66df0a44c67a7af17f3b77a152785"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a002978fc889cc011d143185f2377eca2"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a002978fc889cc011d143185f2377eca2">SetUserData</a> (void *userData)</td></tr>
<tr class="separator:a002978fc889cc011d143185f2377eca2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f0687574afa03bc479dc44f29db0afe"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a7f0687574afa03bc479dc44f29db0afe">GetUserData</a> () const</td></tr>
<tr class="separator:a7f0687574afa03bc479dc44f29db0afe"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> is a base class for every object that is in the XML Document Object Model (DOM), except XMLAttributes. Nodes have siblings, a parent, and children which can be navigated. A node is always in a <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>. The type of a <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> can be queried, and it can be cast to its more defined type.</p>
<p>A <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> allocates memory for all its Nodes. When the <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> gets deleted, all its Nodes will also be deleted.</p>
<pre class="fragment">A Document can contain: Element (container or leaf)
                        Comment (leaf)
                        Unknown (leaf)
                        Declaration( leaf )

An Element can contain: Element (container or leaf)
                        Text    (leaf)
                        Attributes (not on tree)
                        Comment (leaf)
                        Unknown (leaf)</pre> </div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a81e66df0a44c67a7af17f3b77a152785"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a81e66df0a44c67a7af17f3b77a152785">&#9670;&nbsp;</a></span>Accept()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool tinyxml2::XMLNode::Accept </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a> *&#160;</td>
          <td class="paramname"><em>visitor</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">pure virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Accept a hierarchical visit of the nodes in the TinyXML-2 DOM. Every node in the XML tree will be conditionally visited and the host will be called back via the <a class="el" href="classtinyxml2_1_1_x_m_l_visitor.html">XMLVisitor</a> interface.</p>
<p>This is essentially a SAX interface for TinyXML-2. (Note however it doesn't re-parse the XML for the callbacks, so the performance of TinyXML-2 is unchanged by using this interface versus any other.)</p>
<p>The interface has been based on ideas from:</p>
<ul>
<li><a href="http://www.saxproject.org/">http://www.saxproject.org/</a></li>
<li><a href="http://c2.com/cgi/wiki?HierarchicalVisitorPattern">http://c2.com/cgi/wiki?HierarchicalVisitorPattern</a></li>
</ul>
<p>Which are both good references for "visiting".</p>
<p>An example of using <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a81e66df0a44c67a7af17f3b77a152785">Accept()</a>: </p><pre class="fragment">XMLPrinter printer;
tinyxmlDoc.Accept( &amp;printer );
const char* xmlcstr = printer.CStr();
</pre> 
<p>Implemented in <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a9efa54f7ecb37c17ab1fa2b3078ccca1">tinyxml2::XMLDocument</a>, <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a3ea8a40e788fb9ad876c28a32932c6d5">tinyxml2::XMLElement</a>, <a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html#a70983aa1b1cff3d3aa6d4d0a80e5ee48">tinyxml2::XMLUnknown</a>, <a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html#a5f376019fb34752eb248548f42f32045">tinyxml2::XMLDeclaration</a>, <a class="el" href="classtinyxml2_1_1_x_m_l_comment.html#a4a33dc32fae0285b03f9cfcb3e43e122">tinyxml2::XMLComment</a>, and <a class="el" href="classtinyxml2_1_1_x_m_l_text.html#a1b2c1448f1a21299d0a7913f18b55206">tinyxml2::XMLText</a>.</p>

</div>
</div>
<a id="a62c71b6bf8734b5424063b8d9a61c266"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a62c71b6bf8734b5424063b8d9a61c266">&#9670;&nbsp;</a></span>DeepClone()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* tinyxml2::XMLNode::DeepClone </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td>
          <td class="paramname"><em>target</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Make a copy of this node and all its children.</p>
<p>If the 'target' is null, then the nodes will be allocated in the current document. If 'target' is specified, the memory will be allocated is the specified <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>.</p>
<p>NOTE: This is probably not the correct tool to copy a document, since XMLDocuments can have multiple top level XMLNodes. You probably want to use <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#af592ffc91514e25a39664521ac83db45">XMLDocument::DeepCopy()</a> </p>

</div>
</div>
<a id="a363b6edbd6ebd55f8387d2b89f2b0921"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a363b6edbd6ebd55f8387d2b89f2b0921">&#9670;&nbsp;</a></span>DeleteChild()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void tinyxml2::XMLNode::DeleteChild </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td>
          <td class="paramname"><em>node</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Delete a child of this node. </p>

</div>
</div>
<a id="a0360085cc54df5bff85d5c5da13afdce"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0360085cc54df5bff85d5c5da13afdce">&#9670;&nbsp;</a></span>DeleteChildren()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void tinyxml2::XMLNode::DeleteChildren </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Delete all the children of this node. </p>

</div>
</div>
<a id="a1795a35852dc8aae877cc8ded986e59b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1795a35852dc8aae877cc8ded986e59b">&#9670;&nbsp;</a></span>FirstChildElement()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* tinyxml2::XMLNode::FirstChildElement </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>name</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get the first child element, or optionally the first child element with the specified name. </p>

</div>
</div>
<a id="a7f0687574afa03bc479dc44f29db0afe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7f0687574afa03bc479dc44f29db0afe">&#9670;&nbsp;</a></span>GetUserData()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void* tinyxml2::XMLNode::GetUserData </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get user data set into the <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>. TinyXML-2 in no way processes or interprets user data. It is initially 0. </p>

</div>
</div>
<a id="a85adb8f0b7477eec30f9a41d420b09c2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a85adb8f0b7477eec30f9a41d420b09c2">&#9670;&nbsp;</a></span>InsertAfterChild()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* tinyxml2::XMLNode::InsertAfterChild </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td>
          <td class="paramname"><em>afterThis</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td>
          <td class="paramname"><em>addThis</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Add a node after the specified child node. If the child node is already part of the document, it is moved from its old location to the new location. Returns the addThis argument or 0 if the afterThis node is not a child of this node, or if the node does not belong to the same document. </p>

</div>
</div>
<a id="aeb249ed60f4e8bfad3709151c3ee4286"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aeb249ed60f4e8bfad3709151c3ee4286">&#9670;&nbsp;</a></span>InsertEndChild()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* tinyxml2::XMLNode::InsertEndChild </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td>
          <td class="paramname"><em>addThis</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Add a child node as the last (right) child. If the child node is already part of the document, it is moved from its old location to the new location. Returns the addThis argument or 0 if the node does not belong to the same document. </p>

</div>
</div>
<a id="a8ff7dc071f3a1a6ae2ac25a37492865d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8ff7dc071f3a1a6ae2ac25a37492865d">&#9670;&nbsp;</a></span>InsertFirstChild()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* tinyxml2::XMLNode::InsertFirstChild </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td>
          <td class="paramname"><em>addThis</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Add a child node as the first (left) child. If the child node is already part of the document, it is moved from its old location to the new location. Returns the addThis argument or 0 if the node does not belong to the same document. </p>

</div>
</div>
<a id="a173e9d1341bc56992e2d320a35936551"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a173e9d1341bc56992e2d320a35936551">&#9670;&nbsp;</a></span>LastChildElement()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>* tinyxml2::XMLNode::LastChildElement </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>name</em> = <code>0</code></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Get the last child element or optionally the last child element with the specified name. </p>

</div>
</div>
<a id="a002978fc889cc011d143185f2377eca2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a002978fc889cc011d143185f2377eca2">&#9670;&nbsp;</a></span>SetUserData()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void tinyxml2::XMLNode::SetUserData </td>
          <td>(</td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>userData</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Set user data into the <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>. TinyXML-2 in no way processes or interprets user data. It is initially 0. </p>

</div>
</div>
<a id="a09dd68cf9eae137579f6e50f36487513"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a09dd68cf9eae137579f6e50f36487513">&#9670;&nbsp;</a></span>SetValue()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void tinyxml2::XMLNode::SetValue </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>val</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>staticMem</em> = <code>false</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Set the Value of an XML node. </p><dl class="section see"><dt>See also</dt><dd><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a66344989a4b436155bcda72bd6b07b82">Value()</a> </dd></dl>

</div>
</div>
<a id="a8402cbd3129d20e9e6024bbcc0531283"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8402cbd3129d20e9e6024bbcc0531283">&#9670;&nbsp;</a></span>ShallowClone()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>* tinyxml2::XMLNode::ShallowClone </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a> *&#160;</td>
          <td class="paramname"><em>document</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">pure virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Make a copy of this node, but not its children. You may pass in a Document pointer that will be the owner of the new Node. If the 'document' is null, then the node returned will be allocated from the current Document. (this-&gt;<a class="el" href="classtinyxml2_1_1_x_m_l_node.html#af343d1ef0b45c0020e62d784d7e67a68" title="Get the XMLDocument that owns this XMLNode.">GetDocument()</a>)</p>
<p>Note: if called on a <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>, this will return null. </p>

<p>Implemented in <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ac035742d68b0c50c3f676374e59fe750">tinyxml2::XMLElement</a>, <a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html#a0125f41c89763dea06619b5fd5246b4c">tinyxml2::XMLUnknown</a>, <a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html#a118d47518dd9e522644e42efa259aed7">tinyxml2::XMLDeclaration</a>, <a class="el" href="classtinyxml2_1_1_x_m_l_comment.html#a08991cc63fadf7e95078ac4f9ea1b073">tinyxml2::XMLComment</a>, <a class="el" href="classtinyxml2_1_1_x_m_l_text.html#af3a81ed4dd49d5151c477b3f265a3011">tinyxml2::XMLText</a>, and <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#aa37cc1709d7e1e988bc17dcfb24a69b8">tinyxml2::XMLDocument</a>.</p>

</div>
</div>
<a id="a7ce18b751c3ea09eac292dca264f9226"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7ce18b751c3ea09eac292dca264f9226">&#9670;&nbsp;</a></span>ShallowEqual()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool tinyxml2::XMLNode::ShallowEqual </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td>
          <td class="paramname"><em>compare</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">pure virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Test if 2 nodes are the same, but don't test children. The 2 nodes do not need to be in the same Document.</p>
<p>Note: if called on a <a class="el" href="classtinyxml2_1_1_x_m_l_document.html">XMLDocument</a>, this will return false. </p>

<p>Implemented in <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ad9ea913a460b48979bd83cf9871c99f6">tinyxml2::XMLElement</a>, <a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html#a0715ab2c05d7f74845c188122213b116">tinyxml2::XMLUnknown</a>, <a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html#aa26b70011694e9b9e9480b929e9b78d6">tinyxml2::XMLDeclaration</a>, <a class="el" href="classtinyxml2_1_1_x_m_l_comment.html#a6f7d227b25afa8cc3c763b7cc8833739">tinyxml2::XMLComment</a>, <a class="el" href="classtinyxml2_1_1_x_m_l_text.html#ae0fff8a24e2de7eb073fd192e9db0331">tinyxml2::XMLText</a>, and <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a6fe5ef18699091844fcf64b56ffa5bf9">tinyxml2::XMLDocument</a>.</p>

</div>
</div>
<a id="a66344989a4b436155bcda72bd6b07b82"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a66344989a4b436155bcda72bd6b07b82">&#9670;&nbsp;</a></span>Value()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* tinyxml2::XMLNode::Value </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The meaning of 'value' changes for the specific type. </p><pre class="fragment">Document:   empty (NULL is returned, not an empty string)
Element:    name of the element
Comment:    the comment text
Unknown:    the tag contents
Text:       the text string
</pre> 
</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

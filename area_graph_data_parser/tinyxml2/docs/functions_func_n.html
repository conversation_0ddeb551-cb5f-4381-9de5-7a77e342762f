<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index_n"></a>- n -</h3><ul>
<li>Name()
: <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#ab886c486ec19f02ed826f8dc129e5ad8">tinyxml2::XMLAttribute</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a63e057fb5baee1dd29f323cb85907b35">tinyxml2::XMLElement</a>
</li>
<li>NewComment()
: <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#ade4874bcb439954972ef2b3723ff3259">tinyxml2::XMLDocument</a>
</li>
<li>NewDeclaration()
: <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#aee2eb3435923f5494dcc70ac225b60a2">tinyxml2::XMLDocument</a>
</li>
<li>NewElement()
: <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a8aa7817d4a1001364b06373763ab99d6">tinyxml2::XMLDocument</a>
</li>
<li>NewText()
: <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#ab7e8b29ae4099092a8bb947da6361296">tinyxml2::XMLDocument</a>
</li>
<li>NewUnknown()
: <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a5385c937734ff6db9226ab707d2c7147">tinyxml2::XMLDocument</a>
</li>
<li>Next()
: <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#aee53571b21e7ce5421eb929523a8bbe6">tinyxml2::XMLAttribute</a>
</li>
<li>NextSibling()
: <a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#aad2eccc7c7c7b18145877c978c3850b5">tinyxml2::XMLHandle</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a79db9ef0fe014d27790f2218b87bcbb5">tinyxml2::XMLNode</a>
</li>
<li>NextSiblingElement()
: <a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#ae41d88ee061f3c49a081630ff753b2c5">tinyxml2::XMLHandle</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a1264c86233328f0cd36297552d982f80">tinyxml2::XMLNode</a>
</li>
<li>NoChildren()
: <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ac3ab489e6e202a3cd1762d3b332e89d4">tinyxml2::XMLNode</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

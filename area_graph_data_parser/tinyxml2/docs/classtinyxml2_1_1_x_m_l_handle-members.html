<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>tinyxml2</b></li><li class="navelem"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tinyxml2::XMLHandle Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a536447dc7f54c0cd11e031dad94795ae">FirstChild</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a74b04dd0f15e0bf01860e282b840b6a3">FirstChildElement</a>(const char *name=0)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a9d09f04435f0f2f7d0816b0198d0517b">LastChild</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a42cccd0ce8b1ce704f431025e9f19e0c">LastChildElement</a>(const char *name=0)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#aad2eccc7c7c7b18145877c978c3850b5">NextSibling</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#ae41d88ee061f3c49a081630ff753b2c5">NextSiblingElement</a>(const char *name=0)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a75b908322bb4b83be3281b6845252b20">operator=</a>(const XMLHandle &amp;ref)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a428374e756f4db4cbc287fec64eae02c">PreviousSibling</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a786957e498039554ed334cdc36612a7e">PreviousSiblingElement</a>(const char *name=0)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a108858be7ee3eb53f73b5194c1aa8ff0">ToDeclaration</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a5e73ed8f3f6f9619d5a8bb1862c47d99">ToElement</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a03ea6ec970a021b71bf1219a0f6717df">ToNode</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a6ab9e8cbfb41417246e5657e3842c62a">ToText</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#aa387368a1ad8d843a9f12df863d298de">ToUnknown</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a9c240a35c18f053509b4b97ddccd9793">XMLHandle</a>(XMLNode *node)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">explicit</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#aa2edbc1c0d3e3e8259bd98de7f1cf500">XMLHandle</a>(XMLNode &amp;node)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">explicit</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#afd8e01e6018c07347b8e6d80272466aa">XMLHandle</a>(const XMLHandle &amp;ref)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">tinyxml2::XMLHandle</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

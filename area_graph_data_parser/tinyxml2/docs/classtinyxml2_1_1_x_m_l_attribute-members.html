<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>tinyxml2</b></li><li class="navelem"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">XMLAttribute</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tinyxml2::XMLAttribute Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a98ce5207344ad33a265b0422addae1ff">BoolValue</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a4aa73513f54ff0087d3e804f0f54e30f">DoubleValue</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a27797b45d21c981257720db94f5f8801">FloatValue</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a02d5ea924586e35f9c13857d1671b765">GetLineNum</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#adfa2433f0fdafd5c3880936de9affa80">IntValue</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#ab886c486ec19f02ed826f8dc129e5ad8">Name</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#aee53571b21e7ce5421eb929523a8bbe6">Next</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a5f32e038954256f61c21ff20fd13a09c">QueryBoolValue</a>(bool *value) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a2aa6e55e8ea03af0609cf6690bff79b9">QueryDoubleValue</a>(double *value) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a049dea6449a6259b6cfed44a9427b607">QueryFloatValue</a>(float *value) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a4e25344d6e4159026be34dbddf1dcac2">QueryInt64Value</a>(int64_t *value) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a6d5176260db00ea301c01af8457cd993">QueryIntValue</a>(int *value) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#af793c695e7ee65cf20b8010d38b1d157">QueryUnsigned64Value</a>(uint64_t *value) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a48a7f3496f1415832e451bd8d09c9cb9">QueryUnsignedValue</a>(unsigned int *value) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a406d2c4a13c7af99a65edb59dd9f7581">SetAttribute</a>(const char *value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#ad86d7d7058d76761c3a80662566a57e5">SetAttribute</a>(int value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#ae70468c0f6df2748ba3529c716999fae">SetAttribute</a>(unsigned value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a7c1240f479722b9aa29b6c030aa116c2">SetAttribute</a>(int64_t value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a10964060a5c0d92486ecf8705bdf37da">SetAttribute</a>(uint64_t value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#ab3516def4fe058fe328f2b89fc2d77da">SetAttribute</a>(bool value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a9a65ab3147abe8ccbbd373ce8791e818">SetAttribute</a>(double value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#ae95e843313aaf5d56c32530b6456df02">SetAttribute</a>(float value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a0be5343b08a957c42c02c5d32c35d338">UnsignedValue</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a1aab1dd0e43ecbcfa306adbcf3a3d853">Value</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html">tinyxml2::XMLAttribute</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

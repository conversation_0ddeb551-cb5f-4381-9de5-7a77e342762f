<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: tinyxml2::XMLHandle Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>tinyxml2</b></li><li class="navelem"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="classtinyxml2_1_1_x_m_l_handle-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">tinyxml2::XMLHandle Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a9c240a35c18f053509b4b97ddccd9793"><td class="memItemLeft" align="right" valign="top"><a id="a9c240a35c18f053509b4b97ddccd9793"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a9c240a35c18f053509b4b97ddccd9793">XMLHandle</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *node)</td></tr>
<tr class="memdesc:a9c240a35c18f053509b4b97ddccd9793"><td class="mdescLeft">&#160;</td><td class="mdescRight">Create a handle from any node (at any depth of the tree.) This can be a null pointer. <br /></td></tr>
<tr class="separator:a9c240a35c18f053509b4b97ddccd9793"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa2edbc1c0d3e3e8259bd98de7f1cf500"><td class="memItemLeft" align="right" valign="top"><a id="aa2edbc1c0d3e3e8259bd98de7f1cf500"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#aa2edbc1c0d3e3e8259bd98de7f1cf500">XMLHandle</a> (<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> &amp;node)</td></tr>
<tr class="memdesc:aa2edbc1c0d3e3e8259bd98de7f1cf500"><td class="mdescLeft">&#160;</td><td class="mdescRight">Create a handle from a node. <br /></td></tr>
<tr class="separator:aa2edbc1c0d3e3e8259bd98de7f1cf500"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd8e01e6018c07347b8e6d80272466aa"><td class="memItemLeft" align="right" valign="top"><a id="afd8e01e6018c07347b8e6d80272466aa"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#afd8e01e6018c07347b8e6d80272466aa">XMLHandle</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a> &amp;ref)</td></tr>
<tr class="memdesc:afd8e01e6018c07347b8e6d80272466aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copy constructor. <br /></td></tr>
<tr class="separator:afd8e01e6018c07347b8e6d80272466aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a75b908322bb4b83be3281b6845252b20"><td class="memItemLeft" align="right" valign="top"><a id="a75b908322bb4b83be3281b6845252b20"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a75b908322bb4b83be3281b6845252b20">operator=</a> (const <a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a> &amp;ref)</td></tr>
<tr class="memdesc:a75b908322bb4b83be3281b6845252b20"><td class="mdescLeft">&#160;</td><td class="mdescRight">Assignment. <br /></td></tr>
<tr class="separator:a75b908322bb4b83be3281b6845252b20"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a536447dc7f54c0cd11e031dad94795ae"><td class="memItemLeft" align="right" valign="top"><a id="a536447dc7f54c0cd11e031dad94795ae"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a536447dc7f54c0cd11e031dad94795ae">FirstChild</a> ()</td></tr>
<tr class="memdesc:a536447dc7f54c0cd11e031dad94795ae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the first child of this handle. <br /></td></tr>
<tr class="separator:a536447dc7f54c0cd11e031dad94795ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a74b04dd0f15e0bf01860e282b840b6a3"><td class="memItemLeft" align="right" valign="top"><a id="a74b04dd0f15e0bf01860e282b840b6a3"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a74b04dd0f15e0bf01860e282b840b6a3">FirstChildElement</a> (const char *name=0)</td></tr>
<tr class="memdesc:a74b04dd0f15e0bf01860e282b840b6a3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the first child element of this handle. <br /></td></tr>
<tr class="separator:a74b04dd0f15e0bf01860e282b840b6a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9d09f04435f0f2f7d0816b0198d0517b"><td class="memItemLeft" align="right" valign="top"><a id="a9d09f04435f0f2f7d0816b0198d0517b"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a9d09f04435f0f2f7d0816b0198d0517b">LastChild</a> ()</td></tr>
<tr class="memdesc:a9d09f04435f0f2f7d0816b0198d0517b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the last child of this handle. <br /></td></tr>
<tr class="separator:a9d09f04435f0f2f7d0816b0198d0517b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42cccd0ce8b1ce704f431025e9f19e0c"><td class="memItemLeft" align="right" valign="top"><a id="a42cccd0ce8b1ce704f431025e9f19e0c"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a42cccd0ce8b1ce704f431025e9f19e0c">LastChildElement</a> (const char *name=0)</td></tr>
<tr class="memdesc:a42cccd0ce8b1ce704f431025e9f19e0c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the last child element of this handle. <br /></td></tr>
<tr class="separator:a42cccd0ce8b1ce704f431025e9f19e0c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a428374e756f4db4cbc287fec64eae02c"><td class="memItemLeft" align="right" valign="top"><a id="a428374e756f4db4cbc287fec64eae02c"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a428374e756f4db4cbc287fec64eae02c">PreviousSibling</a> ()</td></tr>
<tr class="memdesc:a428374e756f4db4cbc287fec64eae02c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the previous sibling of this handle. <br /></td></tr>
<tr class="separator:a428374e756f4db4cbc287fec64eae02c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a786957e498039554ed334cdc36612a7e"><td class="memItemLeft" align="right" valign="top"><a id="a786957e498039554ed334cdc36612a7e"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a786957e498039554ed334cdc36612a7e">PreviousSiblingElement</a> (const char *name=0)</td></tr>
<tr class="memdesc:a786957e498039554ed334cdc36612a7e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the previous sibling element of this handle. <br /></td></tr>
<tr class="separator:a786957e498039554ed334cdc36612a7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad2eccc7c7c7b18145877c978c3850b5"><td class="memItemLeft" align="right" valign="top"><a id="aad2eccc7c7c7b18145877c978c3850b5"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#aad2eccc7c7c7b18145877c978c3850b5">NextSibling</a> ()</td></tr>
<tr class="memdesc:aad2eccc7c7c7b18145877c978c3850b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the next sibling of this handle. <br /></td></tr>
<tr class="separator:aad2eccc7c7c7b18145877c978c3850b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae41d88ee061f3c49a081630ff753b2c5"><td class="memItemLeft" align="right" valign="top"><a id="ae41d88ee061f3c49a081630ff753b2c5"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#ae41d88ee061f3c49a081630ff753b2c5">NextSiblingElement</a> (const char *name=0)</td></tr>
<tr class="memdesc:ae41d88ee061f3c49a081630ff753b2c5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the next sibling element of this handle. <br /></td></tr>
<tr class="separator:ae41d88ee061f3c49a081630ff753b2c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a03ea6ec970a021b71bf1219a0f6717df"><td class="memItemLeft" align="right" valign="top"><a id="a03ea6ec970a021b71bf1219a0f6717df"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a03ea6ec970a021b71bf1219a0f6717df">ToNode</a> ()</td></tr>
<tr class="memdesc:a03ea6ec970a021b71bf1219a0f6717df"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safe cast to <a class="el" href="classtinyxml2_1_1_x_m_l_node.html">XMLNode</a>. This can return null. <br /></td></tr>
<tr class="separator:a03ea6ec970a021b71bf1219a0f6717df"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5e73ed8f3f6f9619d5a8bb1862c47d99"><td class="memItemLeft" align="right" valign="top"><a id="a5e73ed8f3f6f9619d5a8bb1862c47d99"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a5e73ed8f3f6f9619d5a8bb1862c47d99">ToElement</a> ()</td></tr>
<tr class="memdesc:a5e73ed8f3f6f9619d5a8bb1862c47d99"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safe cast to <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a>. This can return null. <br /></td></tr>
<tr class="separator:a5e73ed8f3f6f9619d5a8bb1862c47d99"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ab9e8cbfb41417246e5657e3842c62a"><td class="memItemLeft" align="right" valign="top"><a id="a6ab9e8cbfb41417246e5657e3842c62a"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a6ab9e8cbfb41417246e5657e3842c62a">ToText</a> ()</td></tr>
<tr class="memdesc:a6ab9e8cbfb41417246e5657e3842c62a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safe cast to <a class="el" href="classtinyxml2_1_1_x_m_l_text.html">XMLText</a>. This can return null. <br /></td></tr>
<tr class="separator:a6ab9e8cbfb41417246e5657e3842c62a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa387368a1ad8d843a9f12df863d298de"><td class="memItemLeft" align="right" valign="top"><a id="aa387368a1ad8d843a9f12df863d298de"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#aa387368a1ad8d843a9f12df863d298de">ToUnknown</a> ()</td></tr>
<tr class="memdesc:aa387368a1ad8d843a9f12df863d298de"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safe cast to <a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html">XMLUnknown</a>. This can return null. <br /></td></tr>
<tr class="separator:aa387368a1ad8d843a9f12df863d298de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a108858be7ee3eb53f73b5194c1aa8ff0"><td class="memItemLeft" align="right" valign="top"><a id="a108858be7ee3eb53f73b5194c1aa8ff0"></a>
<a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="classtinyxml2_1_1_x_m_l_handle.html#a108858be7ee3eb53f73b5194c1aa8ff0">ToDeclaration</a> ()</td></tr>
<tr class="memdesc:a108858be7ee3eb53f73b5194c1aa8ff0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safe cast to <a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html">XMLDeclaration</a>. This can return null. <br /></td></tr>
<tr class="separator:a108858be7ee3eb53f73b5194c1aa8ff0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>A <a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a> is a class that wraps a node pointer with null checks; this is an incredibly useful thing. Note that <a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a> is not part of the TinyXML-2 DOM structure. It is a separate utility class.</p>
<p>Take an example: </p><pre class="fragment">&lt;Document&gt;
    &lt;Element attributeA = "valueA"&gt;
        &lt;Child attributeB = "value1" /&gt;
        &lt;Child attributeB = "value2" /&gt;
    &lt;/Element&gt;
&lt;/Document&gt;
</pre><p>Assuming you want the value of "attributeB" in the 2nd "Child" element, it's very easy to write a <em>lot</em> of code that looks like:</p>
<pre class="fragment">XMLElement* root = document.FirstChildElement( "Document" );
if ( root )
{
    XMLElement* element = root-&gt;FirstChildElement( "Element" );
    if ( element )
    {
        XMLElement* child = element-&gt;FirstChildElement( "Child" );
        if ( child )
        {
            XMLElement* child2 = child-&gt;NextSiblingElement( "Child" );
            if ( child2 )
            {
                // Finally do something useful.
</pre><p>And that doesn't even cover "else" cases. <a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a> addresses the verbosity of such code. A <a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a> checks for null pointers so it is perfectly safe and correct to use:</p>
<pre class="fragment">XMLHandle docHandle( &amp;document );
XMLElement* child2 = docHandle.FirstChildElement( "Document" ).FirstChildElement( "Element" ).FirstChildElement().NextSiblingElement();
if ( child2 )
{
    // do something useful
</pre><p>Which is MUCH more concise and useful.</p>
<p>It is also safe to copy handles - internally they are nothing more than node pointers. </p><pre class="fragment">XMLHandle handleCopy = handle;
</pre><p>See also <a class="el" href="classtinyxml2_1_1_x_m_l_const_handle.html">XMLConstHandle</a>, which is the same as <a class="el" href="classtinyxml2_1_1_x_m_l_handle.html">XMLHandle</a>, but operates on const objects. </p>
</div><hr/>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="tinyxml2_8h_source.html">tinyxml2.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

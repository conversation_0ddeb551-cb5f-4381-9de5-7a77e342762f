<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index_s"></a>- s -</h3><ul>
<li>SaveFile()
: <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a73ac416b4a2aa0952e841220eb3da18f">tinyxml2::XMLDocument</a>
</li>
<li>SetAttribute()
: <a class="el" href="classtinyxml2_1_1_x_m_l_attribute.html#a406d2c4a13c7af99a65edb59dd9f7581">tinyxml2::XMLAttribute</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a11943abf2d0831548c3790dd5d9f119c">tinyxml2::XMLElement</a>
</li>
<li>SetBOM()
: <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a14419b698f7c4b140df4e80f3f0c93b0">tinyxml2::XMLDocument</a>
</li>
<li>SetCData()
: <a class="el" href="classtinyxml2_1_1_x_m_l_text.html#ad080357d76ab7cc59d7651249949329d">tinyxml2::XMLText</a>
</li>
<li>SetName()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a97712009a530d8cb8a63bf705f02b4f1">tinyxml2::XMLElement</a>
</li>
<li>SetText()
: <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a1f9c2cd61b72af5ae708d37b7ad283ce">tinyxml2::XMLElement</a>
</li>
<li>SetUserData()
: <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a002978fc889cc011d143185f2377eca2">tinyxml2::XMLNode</a>
</li>
<li>SetValue()
: <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a09dd68cf9eae137579f6e50f36487513">tinyxml2::XMLNode</a>
</li>
<li>ShallowClone()
: <a class="el" href="classtinyxml2_1_1_x_m_l_comment.html#a08991cc63fadf7e95078ac4f9ea1b073">tinyxml2::XMLComment</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html#a118d47518dd9e522644e42efa259aed7">tinyxml2::XMLDeclaration</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#aa37cc1709d7e1e988bc17dcfb24a69b8">tinyxml2::XMLDocument</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ac035742d68b0c50c3f676374e59fe750">tinyxml2::XMLElement</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8402cbd3129d20e9e6024bbcc0531283">tinyxml2::XMLNode</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_text.html#af3a81ed4dd49d5151c477b3f265a3011">tinyxml2::XMLText</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html#a0125f41c89763dea06619b5fd5246b4c">tinyxml2::XMLUnknown</a>
</li>
<li>ShallowEqual()
: <a class="el" href="classtinyxml2_1_1_x_m_l_comment.html#a6f7d227b25afa8cc3c763b7cc8833739">tinyxml2::XMLComment</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_declaration.html#aa26b70011694e9b9e9480b929e9b78d6">tinyxml2::XMLDeclaration</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_document.html#a6fe5ef18699091844fcf64b56ffa5bf9">tinyxml2::XMLDocument</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ad9ea913a460b48979bd83cf9871c99f6">tinyxml2::XMLElement</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a7ce18b751c3ea09eac292dca264f9226">tinyxml2::XMLNode</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_text.html#ae0fff8a24e2de7eb073fd192e9db0331">tinyxml2::XMLText</a>
, <a class="el" href="classtinyxml2_1_1_x_m_l_unknown.html#a0715ab2c05d7f74845c188122213b116">tinyxml2::XMLUnknown</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>tinyxml2</b></li><li class="navelem"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">XMLElement</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tinyxml2::XMLElement Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a3ea8a40e788fb9ad876c28a32932c6d5">Accept</a>(XMLVisitor *visitor) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a70e49ed60b11212ae35f7e354cfe1de9">Attribute</a>(const char *name, const char *value=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a53eda26131e1ad1031ef8ec8adb51bd8">BoolAttribute</a>(const char *name, bool defaultValue=false) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a68569f59f6382bcea7f5013ec59736d2">BoolText</a>(bool defaultValue=false) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a62c71b6bf8734b5424063b8d9a61c266">DeepClone</a>(XMLDocument *target) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#aebd45aa7118964c30b32fe12e944628a">DeleteAttribute</a>(const char *name)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a363b6edbd6ebd55f8387d2b89f2b0921">DeleteChild</a>(XMLNode *node)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a0360085cc54df5bff85d5c5da13afdce">DeleteChildren</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a10a90c505aea716bf073eea1c97f33b5">DoubleAttribute</a>(const char *name, double defaultValue=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a81b1ff0cf2f2cd09be8badc08b39a2b7">DoubleText</a>(double defaultValue=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a2dcd4d5d6fb63396cd2f257c318b42c4">FindAttribute</a>(const char *name) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a3e191704c8d499906ec11fe2f60c6686">FirstAttribute</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ae7dc225e1018cdd685f7563593a1fe08">FirstChild</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a1795a35852dc8aae877cc8ded986e59b">FirstChildElement</a>(const char *name=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ab1f4be2332e27dc640e9b6abd01d64dd">FloatAttribute</a>(const char *name, float defaultValue=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a45444eb21f99ca46101545992dc2e927">FloatText</a>(float defaultValue=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a2de84cfa4ec3fe249bad745069d145f1">GetDocument</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#af343d1ef0b45c0020e62d784d7e67a68">GetDocument</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a9b5fc636646fda761d342c72e91cb286">GetLineNum</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a6d5c8d115561ade4e4456b71d91b6f51">GetText</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a7f0687574afa03bc479dc44f29db0afe">GetUserData</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a85adb8f0b7477eec30f9a41d420b09c2">InsertAfterChild</a>(XMLNode *afterThis, XMLNode *addThis)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aeb249ed60f4e8bfad3709151c3ee4286">InsertEndChild</a>(XMLNode *addThis)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8ff7dc071f3a1a6ae2ac25a37492865d">InsertFirstChild</a>(XMLNode *addThis)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#abc9506eff9780f666f49dc3d5e5cae13">InsertNewChildElement</a>(const char *name)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ae4f2c2e781b8dc030411d84cd20fa46d">InsertNewComment</a>(const char *comment)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#adec237e788b50c4ed73c918a166adde6">InsertNewDeclaration</a>(const char *text)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a189e155810fc9fdd4da1409cbadee187">InsertNewText</a>(const char *text)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#acaa5fe3957760e68185006965e2c11c2">InsertNewUnknown</a>(const char *text)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a66d96972adecd816194191f13cc4a0a0">Int64Attribute</a>(const char *name, int64_t defaultValue=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#aab6151f7e3b4c2c0a8234e262d7b6b8a">Int64Text</a>(int64_t defaultValue=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a95a89b13bb14a2d4655e2b5b406c00d4">IntAttribute</a>(const char *name, int defaultValue=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a9b8583a277e8e26f4cbbb5492786778e">LastChild</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a173e9d1341bc56992e2d320a35936551">LastChildElement</a>(const char *name=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a63e057fb5baee1dd29f323cb85907b35">Name</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a79db9ef0fe014d27790f2218b87bcbb5">NextSibling</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a1264c86233328f0cd36297552d982f80">NextSiblingElement</a>(const char *name=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ac3ab489e6e202a3cd1762d3b332e89d4">NoChildren</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#ae0f62bc186c56c2e0483ebd52dbfbe34">Parent</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aac667c513d445f8b783e1e15ef9d3551">PreviousSibling</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a872936cae46fb473eb47fec99129fc70">PreviousSiblingElement</a>(const char *name=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a5b7df3bed2b8954eabf227fa204522eb">QueryAttribute</a>(const char *name, int *value) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a14c1bb77c39689838be01838d86ca872">QueryBoolAttribute</a>(const char *name, bool *value) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a3fe5417d59eb8f5c4afe924b7d332736">QueryBoolText</a>(bool *bval) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a5f0964e2dbd8e2ee7fce9beab689443c">QueryDoubleAttribute</a>(const char *name, double *value) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a684679c99bb036a25652744cec6c4d96">QueryDoubleText</a>(double *dval) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#acd5eeddf6002ef90806af794b9d9a5a5">QueryFloatAttribute</a>(const char *name, float *value) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#afa332afedd93210daa6d44b88eb11e29">QueryFloatText</a>(float *fval) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a7c0955d80b6f8d196744eacb0f6e90a8">QueryInt64Attribute</a>(const char *name, int64_t *value) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a120c538c8eead169e635dbc70fb226d8">QueryInt64Text</a>(int64_t *uval) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a8a78bc1187c1c45ad89f2690eab567b1">QueryIntAttribute</a>(const char *name, int *value) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a926357996bef633cb736e1a558419632">QueryIntText</a>(int *ival) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#adb8ae765f98d0c5037faec48deea78bc">QueryStringAttribute</a>(const char *name, const char **value) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a13dd590b5d3958ce2ed79844aacd9405">QueryUnsigned64Attribute</a>(const char *name, uint64_t *value) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ac2239b3bd172ad8f5b78d04d4236144b">QueryUnsigned64Text</a>(uint64_t *uval) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a26fc84cbfba6769dafcfbf256c05e22f">QueryUnsignedAttribute</a>(const char *name, unsigned int *value) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a14d38aa4b5e18a46274a27425188a6a1">QueryUnsignedText</a>(unsigned *uval) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a11943abf2d0831548c3790dd5d9f119c">SetAttribute</a>(const char *name, const char *value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#aae6568c64c7f1cc88be8461ba41a79cf">SetAttribute</a>(const char *name, int value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ae143997e90064ba82326b29a9930ea8f">SetAttribute</a>(const char *name, unsigned value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#aaeefdf9171fec91b13a776b42299b0dd">SetAttribute</a>(const char *name, int64_t value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ad598868c0599ddc4695dab18552c308d">SetAttribute</a>(const char *name, uint64_t value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#aa848b696e6a75e4e545c6da9893b11e1">SetAttribute</a>(const char *name, bool value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a233397ee81e70eb5d4b814c5f8698533">SetAttribute</a>(const char *name, double value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a554b70d882e65b28fc084b23df9b9759">SetAttribute</a>(const char *name, float value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a97712009a530d8cb8a63bf705f02b4f1">SetName</a>(const char *str, bool staticMem=false)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a1f9c2cd61b72af5ae708d37b7ad283ce">SetText</a>(const char *inText)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#aeae8917b5ea6060b3c08d4e3d8d632d7">SetText</a>(int value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a7bbfcc11d516598bc924a8fba4d08597">SetText</a>(unsigned value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a7b62cd33acdfeff7ea2b1b330d4368e4">SetText</a>(int64_t value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a6e615bc745afd1ca8ded56d7aac02657">SetText</a>(uint64_t value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ae4b543d6770de76fb6ab68e541c192a4">SetText</a>(bool value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a67bd77ac9aaeff58ff20b4275a65ba4e">SetText</a>(double value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a51d560da5ae3ad6b75e0ab9ffb2ae42a">SetText</a>(float value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a002978fc889cc011d143185f2377eca2">SetUserData</a>(void *userData)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a09dd68cf9eae137579f6e50f36487513">SetValue</a>(const char *val, bool staticMem=false)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ac035742d68b0c50c3f676374e59fe750">ShallowClone</a>(XMLDocument *document) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ad9ea913a460b48979bd83cf9871c99f6">ShallowEqual</a>(const XMLNode *compare) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#aff47671055aa99840a1c1ebd661e63e3">ToComment</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a174fd4c22c010b58138c1b84a0dfbd51">ToDeclaration</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a836e2966ed736fc3c94f70e12a2a3357">ToDocument</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#ad9ff5c2dbc15df36cf664ce1b0ea0a5d">ToElement</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a41c55dab9162d1eb62db2008430e376b">ToText</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a8675a74aa0ada6eccab0c77ef3e5b9bd">ToUnknown</a>()</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a226502bab8f1be7ede1fdd255398eb85">Unsigned64Attribute</a>(const char *name, uint64_t defaultValue=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#af48c1023abbac1acdf4927c51c3a5f0c">Unsigned64Text</a>(uint64_t defaultValue=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#afea43a1d4aa33e3703ddee5fc9adc26c">UnsignedAttribute</a>(const char *name, unsigned defaultValue=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html#a49bad014ffcc17b0b6119d5b2c97dfb5">UnsignedText</a>(unsigned defaultValue=0) const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_element.html">tinyxml2::XMLElement</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html#a66344989a4b436155bcda72bd6b07b82">Value</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_node.html">tinyxml2::XMLNode</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

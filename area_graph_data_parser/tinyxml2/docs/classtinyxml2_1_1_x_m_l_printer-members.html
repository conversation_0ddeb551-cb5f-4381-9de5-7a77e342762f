<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>TinyXML-2: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">TinyXML-2
   &#160;<span id="projectnumber">9.0.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><b>tinyxml2</b></li><li class="navelem"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">XMLPrinter</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tinyxml2::XMLPrinter Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a690cb140ba98b7339734ff865f56b0b3">ClearBuffer</a>(bool resetToFirstElement=true)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#ad04d29562b46fcdb23ab320f8b664240">CloseElement</a>(bool compactMode=false)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a180671d73844f159f2d4aafbc11d106e">CStr</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a3256cf3523d4898b91abb18b924be04c">CStrSize</a>() const</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a20fb06c83bd13e5140d7dd13af06c010">OpenElement</a>(const char *name, bool compactMode=false)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a01148e2ebe6776e38c5a3e41bc5feb74">PrintSpace</a>(int depth)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a9a4e2c9348b42e147629d5a99f4af3f0">PushAttribute</a>(const char *name, const char *value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#afc8416814219591c2fd5656e0c233140">PushComment</a>(const char *comment)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a178c608ce8476043d5d6513819cde903">PushHeader</a>(bool writeBOM, bool writeDeclaration)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a1cc16a9362df4332012cb13cff6441b3">PushText</a>(const char *text, bool cdata=false)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a3e0d4d78de25d4cf081009e1431cea7e">PushText</a>(int value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a661fb50e7e0a4918d2d259cb0fae647e">PushText</a>(unsigned value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a96b0a0bfe105154a0a6c37d725258f0a">PushText</a>(int64_t value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a60b0a4cf57371ff8679c2c7556ccb708">PushText</a>(uint64_t value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a4390e5fa1ed05189a8686647345ab29f">PushText</a>(bool value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a1dbb1390e829d0673af66b9cd1928bd7">PushText</a>(float value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#aa715302dfc09473c77c853cbd5431965">PushText</a>(double value)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a275ae25544a12199ae40b6994ca6e4de">Visit</a>(const XMLText &amp;text)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a3f16a30be1537ac141d9bd2db824ba9e">Visit</a>(const XMLComment &amp;comment)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a9ceff5cd85e5db65838962174fcdcc46">Visit</a>(const XMLDeclaration &amp;declaration)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#aa15e1da81e17dea5da6499ac5b08d9d8">Visit</a>(const XMLUnknown &amp;unknown)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#ae966b988a7a28c41e91c5ca17fb2054b">VisitEnter</a>(const XMLDocument &amp;)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a2ce2aa508c21ac91615093ddb9c282c5">VisitEnter</a>(const XMLElement &amp;element, const XMLAttribute *attribute)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#a15fc1f2b922f540917dcf52808737b29">VisitExit</a>(const XMLDocument &amp;)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#ae99e0a7086543591edfb565f24689098">VisitExit</a>(const XMLElement &amp;element)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html#aa6d3841c069085f5b8a27bc7103c04f7">XMLPrinter</a>(FILE *file=0, bool compact=false, int depth=0)</td><td class="entry"><a class="el" href="classtinyxml2_1_1_x_m_l_printer.html">tinyxml2::XMLPrinter</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Sat Apr 2 2022 13:12:57 for TinyXML-2 by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>

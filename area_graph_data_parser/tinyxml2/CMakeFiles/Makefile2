# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.24

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/project/OsmAG/tinyxml2

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/project/OsmAG/tinyxml2

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/tinyxml2.dir/all
all: CMakeFiles/xmltest.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/Experimental.dir/clean
clean: CMakeFiles/Nightly.dir/clean
clean: CMakeFiles/Continuous.dir/clean
clean: CMakeFiles/NightlyMemoryCheck.dir/clean
clean: CMakeFiles/NightlyStart.dir/clean
clean: CMakeFiles/NightlyUpdate.dir/clean
clean: CMakeFiles/NightlyConfigure.dir/clean
clean: CMakeFiles/NightlyBuild.dir/clean
clean: CMakeFiles/NightlyTest.dir/clean
clean: CMakeFiles/NightlyCoverage.dir/clean
clean: CMakeFiles/NightlyMemCheck.dir/clean
clean: CMakeFiles/NightlySubmit.dir/clean
clean: CMakeFiles/ExperimentalStart.dir/clean
clean: CMakeFiles/ExperimentalUpdate.dir/clean
clean: CMakeFiles/ExperimentalConfigure.dir/clean
clean: CMakeFiles/ExperimentalBuild.dir/clean
clean: CMakeFiles/ExperimentalTest.dir/clean
clean: CMakeFiles/ExperimentalCoverage.dir/clean
clean: CMakeFiles/ExperimentalMemCheck.dir/clean
clean: CMakeFiles/ExperimentalSubmit.dir/clean
clean: CMakeFiles/ContinuousStart.dir/clean
clean: CMakeFiles/ContinuousUpdate.dir/clean
clean: CMakeFiles/ContinuousConfigure.dir/clean
clean: CMakeFiles/ContinuousBuild.dir/clean
clean: CMakeFiles/ContinuousTest.dir/clean
clean: CMakeFiles/ContinuousCoverage.dir/clean
clean: CMakeFiles/ContinuousMemCheck.dir/clean
clean: CMakeFiles/ContinuousSubmit.dir/clean
clean: CMakeFiles/tinyxml2.dir/clean
clean: CMakeFiles/xmltest.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/Experimental.dir

# All Build rule for target.
CMakeFiles/Experimental.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Experimental.dir/build.make CMakeFiles/Experimental.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Experimental.dir/build.make CMakeFiles/Experimental.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target Experimental"
.PHONY : CMakeFiles/Experimental.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/Experimental.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/Experimental.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/Experimental.dir/rule

# Convenience name for target.
Experimental: CMakeFiles/Experimental.dir/rule
.PHONY : Experimental

# clean rule for target.
CMakeFiles/Experimental.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Experimental.dir/build.make CMakeFiles/Experimental.dir/clean
.PHONY : CMakeFiles/Experimental.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/Nightly.dir

# All Build rule for target.
CMakeFiles/Nightly.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Nightly.dir/build.make CMakeFiles/Nightly.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Nightly.dir/build.make CMakeFiles/Nightly.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target Nightly"
.PHONY : CMakeFiles/Nightly.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/Nightly.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/Nightly.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/Nightly.dir/rule

# Convenience name for target.
Nightly: CMakeFiles/Nightly.dir/rule
.PHONY : Nightly

# clean rule for target.
CMakeFiles/Nightly.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Nightly.dir/build.make CMakeFiles/Nightly.dir/clean
.PHONY : CMakeFiles/Nightly.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/Continuous.dir

# All Build rule for target.
CMakeFiles/Continuous.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Continuous.dir/build.make CMakeFiles/Continuous.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Continuous.dir/build.make CMakeFiles/Continuous.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target Continuous"
.PHONY : CMakeFiles/Continuous.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/Continuous.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/Continuous.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/Continuous.dir/rule

# Convenience name for target.
Continuous: CMakeFiles/Continuous.dir/rule
.PHONY : Continuous

# clean rule for target.
CMakeFiles/Continuous.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Continuous.dir/build.make CMakeFiles/Continuous.dir/clean
.PHONY : CMakeFiles/Continuous.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/NightlyMemoryCheck.dir

# All Build rule for target.
CMakeFiles/NightlyMemoryCheck.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyMemoryCheck.dir/build.make CMakeFiles/NightlyMemoryCheck.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyMemoryCheck.dir/build.make CMakeFiles/NightlyMemoryCheck.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target NightlyMemoryCheck"
.PHONY : CMakeFiles/NightlyMemoryCheck.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/NightlyMemoryCheck.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/NightlyMemoryCheck.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/NightlyMemoryCheck.dir/rule

# Convenience name for target.
NightlyMemoryCheck: CMakeFiles/NightlyMemoryCheck.dir/rule
.PHONY : NightlyMemoryCheck

# clean rule for target.
CMakeFiles/NightlyMemoryCheck.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyMemoryCheck.dir/build.make CMakeFiles/NightlyMemoryCheck.dir/clean
.PHONY : CMakeFiles/NightlyMemoryCheck.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/NightlyStart.dir

# All Build rule for target.
CMakeFiles/NightlyStart.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyStart.dir/build.make CMakeFiles/NightlyStart.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyStart.dir/build.make CMakeFiles/NightlyStart.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target NightlyStart"
.PHONY : CMakeFiles/NightlyStart.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/NightlyStart.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/NightlyStart.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/NightlyStart.dir/rule

# Convenience name for target.
NightlyStart: CMakeFiles/NightlyStart.dir/rule
.PHONY : NightlyStart

# clean rule for target.
CMakeFiles/NightlyStart.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyStart.dir/build.make CMakeFiles/NightlyStart.dir/clean
.PHONY : CMakeFiles/NightlyStart.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/NightlyUpdate.dir

# All Build rule for target.
CMakeFiles/NightlyUpdate.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyUpdate.dir/build.make CMakeFiles/NightlyUpdate.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyUpdate.dir/build.make CMakeFiles/NightlyUpdate.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target NightlyUpdate"
.PHONY : CMakeFiles/NightlyUpdate.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/NightlyUpdate.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/NightlyUpdate.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/NightlyUpdate.dir/rule

# Convenience name for target.
NightlyUpdate: CMakeFiles/NightlyUpdate.dir/rule
.PHONY : NightlyUpdate

# clean rule for target.
CMakeFiles/NightlyUpdate.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyUpdate.dir/build.make CMakeFiles/NightlyUpdate.dir/clean
.PHONY : CMakeFiles/NightlyUpdate.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/NightlyConfigure.dir

# All Build rule for target.
CMakeFiles/NightlyConfigure.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyConfigure.dir/build.make CMakeFiles/NightlyConfigure.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyConfigure.dir/build.make CMakeFiles/NightlyConfigure.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target NightlyConfigure"
.PHONY : CMakeFiles/NightlyConfigure.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/NightlyConfigure.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/NightlyConfigure.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/NightlyConfigure.dir/rule

# Convenience name for target.
NightlyConfigure: CMakeFiles/NightlyConfigure.dir/rule
.PHONY : NightlyConfigure

# clean rule for target.
CMakeFiles/NightlyConfigure.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyConfigure.dir/build.make CMakeFiles/NightlyConfigure.dir/clean
.PHONY : CMakeFiles/NightlyConfigure.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/NightlyBuild.dir

# All Build rule for target.
CMakeFiles/NightlyBuild.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyBuild.dir/build.make CMakeFiles/NightlyBuild.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyBuild.dir/build.make CMakeFiles/NightlyBuild.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target NightlyBuild"
.PHONY : CMakeFiles/NightlyBuild.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/NightlyBuild.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/NightlyBuild.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/NightlyBuild.dir/rule

# Convenience name for target.
NightlyBuild: CMakeFiles/NightlyBuild.dir/rule
.PHONY : NightlyBuild

# clean rule for target.
CMakeFiles/NightlyBuild.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyBuild.dir/build.make CMakeFiles/NightlyBuild.dir/clean
.PHONY : CMakeFiles/NightlyBuild.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/NightlyTest.dir

# All Build rule for target.
CMakeFiles/NightlyTest.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyTest.dir/build.make CMakeFiles/NightlyTest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyTest.dir/build.make CMakeFiles/NightlyTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target NightlyTest"
.PHONY : CMakeFiles/NightlyTest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/NightlyTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/NightlyTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/NightlyTest.dir/rule

# Convenience name for target.
NightlyTest: CMakeFiles/NightlyTest.dir/rule
.PHONY : NightlyTest

# clean rule for target.
CMakeFiles/NightlyTest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyTest.dir/build.make CMakeFiles/NightlyTest.dir/clean
.PHONY : CMakeFiles/NightlyTest.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/NightlyCoverage.dir

# All Build rule for target.
CMakeFiles/NightlyCoverage.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyCoverage.dir/build.make CMakeFiles/NightlyCoverage.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyCoverage.dir/build.make CMakeFiles/NightlyCoverage.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target NightlyCoverage"
.PHONY : CMakeFiles/NightlyCoverage.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/NightlyCoverage.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/NightlyCoverage.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/NightlyCoverage.dir/rule

# Convenience name for target.
NightlyCoverage: CMakeFiles/NightlyCoverage.dir/rule
.PHONY : NightlyCoverage

# clean rule for target.
CMakeFiles/NightlyCoverage.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyCoverage.dir/build.make CMakeFiles/NightlyCoverage.dir/clean
.PHONY : CMakeFiles/NightlyCoverage.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/NightlyMemCheck.dir

# All Build rule for target.
CMakeFiles/NightlyMemCheck.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyMemCheck.dir/build.make CMakeFiles/NightlyMemCheck.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyMemCheck.dir/build.make CMakeFiles/NightlyMemCheck.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target NightlyMemCheck"
.PHONY : CMakeFiles/NightlyMemCheck.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/NightlyMemCheck.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/NightlyMemCheck.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/NightlyMemCheck.dir/rule

# Convenience name for target.
NightlyMemCheck: CMakeFiles/NightlyMemCheck.dir/rule
.PHONY : NightlyMemCheck

# clean rule for target.
CMakeFiles/NightlyMemCheck.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlyMemCheck.dir/build.make CMakeFiles/NightlyMemCheck.dir/clean
.PHONY : CMakeFiles/NightlyMemCheck.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/NightlySubmit.dir

# All Build rule for target.
CMakeFiles/NightlySubmit.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlySubmit.dir/build.make CMakeFiles/NightlySubmit.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlySubmit.dir/build.make CMakeFiles/NightlySubmit.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target NightlySubmit"
.PHONY : CMakeFiles/NightlySubmit.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/NightlySubmit.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/NightlySubmit.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/NightlySubmit.dir/rule

# Convenience name for target.
NightlySubmit: CMakeFiles/NightlySubmit.dir/rule
.PHONY : NightlySubmit

# clean rule for target.
CMakeFiles/NightlySubmit.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/NightlySubmit.dir/build.make CMakeFiles/NightlySubmit.dir/clean
.PHONY : CMakeFiles/NightlySubmit.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ExperimentalStart.dir

# All Build rule for target.
CMakeFiles/ExperimentalStart.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalStart.dir/build.make CMakeFiles/ExperimentalStart.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalStart.dir/build.make CMakeFiles/ExperimentalStart.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target ExperimentalStart"
.PHONY : CMakeFiles/ExperimentalStart.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ExperimentalStart.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ExperimentalStart.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/ExperimentalStart.dir/rule

# Convenience name for target.
ExperimentalStart: CMakeFiles/ExperimentalStart.dir/rule
.PHONY : ExperimentalStart

# clean rule for target.
CMakeFiles/ExperimentalStart.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalStart.dir/build.make CMakeFiles/ExperimentalStart.dir/clean
.PHONY : CMakeFiles/ExperimentalStart.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ExperimentalUpdate.dir

# All Build rule for target.
CMakeFiles/ExperimentalUpdate.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalUpdate.dir/build.make CMakeFiles/ExperimentalUpdate.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalUpdate.dir/build.make CMakeFiles/ExperimentalUpdate.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target ExperimentalUpdate"
.PHONY : CMakeFiles/ExperimentalUpdate.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ExperimentalUpdate.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ExperimentalUpdate.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/ExperimentalUpdate.dir/rule

# Convenience name for target.
ExperimentalUpdate: CMakeFiles/ExperimentalUpdate.dir/rule
.PHONY : ExperimentalUpdate

# clean rule for target.
CMakeFiles/ExperimentalUpdate.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalUpdate.dir/build.make CMakeFiles/ExperimentalUpdate.dir/clean
.PHONY : CMakeFiles/ExperimentalUpdate.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ExperimentalConfigure.dir

# All Build rule for target.
CMakeFiles/ExperimentalConfigure.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalConfigure.dir/build.make CMakeFiles/ExperimentalConfigure.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalConfigure.dir/build.make CMakeFiles/ExperimentalConfigure.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target ExperimentalConfigure"
.PHONY : CMakeFiles/ExperimentalConfigure.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ExperimentalConfigure.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ExperimentalConfigure.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/ExperimentalConfigure.dir/rule

# Convenience name for target.
ExperimentalConfigure: CMakeFiles/ExperimentalConfigure.dir/rule
.PHONY : ExperimentalConfigure

# clean rule for target.
CMakeFiles/ExperimentalConfigure.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalConfigure.dir/build.make CMakeFiles/ExperimentalConfigure.dir/clean
.PHONY : CMakeFiles/ExperimentalConfigure.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ExperimentalBuild.dir

# All Build rule for target.
CMakeFiles/ExperimentalBuild.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalBuild.dir/build.make CMakeFiles/ExperimentalBuild.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalBuild.dir/build.make CMakeFiles/ExperimentalBuild.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target ExperimentalBuild"
.PHONY : CMakeFiles/ExperimentalBuild.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ExperimentalBuild.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ExperimentalBuild.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/ExperimentalBuild.dir/rule

# Convenience name for target.
ExperimentalBuild: CMakeFiles/ExperimentalBuild.dir/rule
.PHONY : ExperimentalBuild

# clean rule for target.
CMakeFiles/ExperimentalBuild.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalBuild.dir/build.make CMakeFiles/ExperimentalBuild.dir/clean
.PHONY : CMakeFiles/ExperimentalBuild.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ExperimentalTest.dir

# All Build rule for target.
CMakeFiles/ExperimentalTest.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalTest.dir/build.make CMakeFiles/ExperimentalTest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalTest.dir/build.make CMakeFiles/ExperimentalTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target ExperimentalTest"
.PHONY : CMakeFiles/ExperimentalTest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ExperimentalTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ExperimentalTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/ExperimentalTest.dir/rule

# Convenience name for target.
ExperimentalTest: CMakeFiles/ExperimentalTest.dir/rule
.PHONY : ExperimentalTest

# clean rule for target.
CMakeFiles/ExperimentalTest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalTest.dir/build.make CMakeFiles/ExperimentalTest.dir/clean
.PHONY : CMakeFiles/ExperimentalTest.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ExperimentalCoverage.dir

# All Build rule for target.
CMakeFiles/ExperimentalCoverage.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalCoverage.dir/build.make CMakeFiles/ExperimentalCoverage.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalCoverage.dir/build.make CMakeFiles/ExperimentalCoverage.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target ExperimentalCoverage"
.PHONY : CMakeFiles/ExperimentalCoverage.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ExperimentalCoverage.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ExperimentalCoverage.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/ExperimentalCoverage.dir/rule

# Convenience name for target.
ExperimentalCoverage: CMakeFiles/ExperimentalCoverage.dir/rule
.PHONY : ExperimentalCoverage

# clean rule for target.
CMakeFiles/ExperimentalCoverage.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalCoverage.dir/build.make CMakeFiles/ExperimentalCoverage.dir/clean
.PHONY : CMakeFiles/ExperimentalCoverage.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ExperimentalMemCheck.dir

# All Build rule for target.
CMakeFiles/ExperimentalMemCheck.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalMemCheck.dir/build.make CMakeFiles/ExperimentalMemCheck.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalMemCheck.dir/build.make CMakeFiles/ExperimentalMemCheck.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target ExperimentalMemCheck"
.PHONY : CMakeFiles/ExperimentalMemCheck.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ExperimentalMemCheck.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ExperimentalMemCheck.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/ExperimentalMemCheck.dir/rule

# Convenience name for target.
ExperimentalMemCheck: CMakeFiles/ExperimentalMemCheck.dir/rule
.PHONY : ExperimentalMemCheck

# clean rule for target.
CMakeFiles/ExperimentalMemCheck.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalMemCheck.dir/build.make CMakeFiles/ExperimentalMemCheck.dir/clean
.PHONY : CMakeFiles/ExperimentalMemCheck.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ExperimentalSubmit.dir

# All Build rule for target.
CMakeFiles/ExperimentalSubmit.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalSubmit.dir/build.make CMakeFiles/ExperimentalSubmit.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalSubmit.dir/build.make CMakeFiles/ExperimentalSubmit.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target ExperimentalSubmit"
.PHONY : CMakeFiles/ExperimentalSubmit.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ExperimentalSubmit.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ExperimentalSubmit.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/ExperimentalSubmit.dir/rule

# Convenience name for target.
ExperimentalSubmit: CMakeFiles/ExperimentalSubmit.dir/rule
.PHONY : ExperimentalSubmit

# clean rule for target.
CMakeFiles/ExperimentalSubmit.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ExperimentalSubmit.dir/build.make CMakeFiles/ExperimentalSubmit.dir/clean
.PHONY : CMakeFiles/ExperimentalSubmit.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ContinuousStart.dir

# All Build rule for target.
CMakeFiles/ContinuousStart.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousStart.dir/build.make CMakeFiles/ContinuousStart.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousStart.dir/build.make CMakeFiles/ContinuousStart.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target ContinuousStart"
.PHONY : CMakeFiles/ContinuousStart.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ContinuousStart.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ContinuousStart.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/ContinuousStart.dir/rule

# Convenience name for target.
ContinuousStart: CMakeFiles/ContinuousStart.dir/rule
.PHONY : ContinuousStart

# clean rule for target.
CMakeFiles/ContinuousStart.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousStart.dir/build.make CMakeFiles/ContinuousStart.dir/clean
.PHONY : CMakeFiles/ContinuousStart.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ContinuousUpdate.dir

# All Build rule for target.
CMakeFiles/ContinuousUpdate.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousUpdate.dir/build.make CMakeFiles/ContinuousUpdate.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousUpdate.dir/build.make CMakeFiles/ContinuousUpdate.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target ContinuousUpdate"
.PHONY : CMakeFiles/ContinuousUpdate.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ContinuousUpdate.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ContinuousUpdate.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/ContinuousUpdate.dir/rule

# Convenience name for target.
ContinuousUpdate: CMakeFiles/ContinuousUpdate.dir/rule
.PHONY : ContinuousUpdate

# clean rule for target.
CMakeFiles/ContinuousUpdate.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousUpdate.dir/build.make CMakeFiles/ContinuousUpdate.dir/clean
.PHONY : CMakeFiles/ContinuousUpdate.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ContinuousConfigure.dir

# All Build rule for target.
CMakeFiles/ContinuousConfigure.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousConfigure.dir/build.make CMakeFiles/ContinuousConfigure.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousConfigure.dir/build.make CMakeFiles/ContinuousConfigure.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target ContinuousConfigure"
.PHONY : CMakeFiles/ContinuousConfigure.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ContinuousConfigure.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ContinuousConfigure.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/ContinuousConfigure.dir/rule

# Convenience name for target.
ContinuousConfigure: CMakeFiles/ContinuousConfigure.dir/rule
.PHONY : ContinuousConfigure

# clean rule for target.
CMakeFiles/ContinuousConfigure.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousConfigure.dir/build.make CMakeFiles/ContinuousConfigure.dir/clean
.PHONY : CMakeFiles/ContinuousConfigure.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ContinuousBuild.dir

# All Build rule for target.
CMakeFiles/ContinuousBuild.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousBuild.dir/build.make CMakeFiles/ContinuousBuild.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousBuild.dir/build.make CMakeFiles/ContinuousBuild.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target ContinuousBuild"
.PHONY : CMakeFiles/ContinuousBuild.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ContinuousBuild.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ContinuousBuild.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/ContinuousBuild.dir/rule

# Convenience name for target.
ContinuousBuild: CMakeFiles/ContinuousBuild.dir/rule
.PHONY : ContinuousBuild

# clean rule for target.
CMakeFiles/ContinuousBuild.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousBuild.dir/build.make CMakeFiles/ContinuousBuild.dir/clean
.PHONY : CMakeFiles/ContinuousBuild.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ContinuousTest.dir

# All Build rule for target.
CMakeFiles/ContinuousTest.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousTest.dir/build.make CMakeFiles/ContinuousTest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousTest.dir/build.make CMakeFiles/ContinuousTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target ContinuousTest"
.PHONY : CMakeFiles/ContinuousTest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ContinuousTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ContinuousTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/ContinuousTest.dir/rule

# Convenience name for target.
ContinuousTest: CMakeFiles/ContinuousTest.dir/rule
.PHONY : ContinuousTest

# clean rule for target.
CMakeFiles/ContinuousTest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousTest.dir/build.make CMakeFiles/ContinuousTest.dir/clean
.PHONY : CMakeFiles/ContinuousTest.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ContinuousCoverage.dir

# All Build rule for target.
CMakeFiles/ContinuousCoverage.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousCoverage.dir/build.make CMakeFiles/ContinuousCoverage.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousCoverage.dir/build.make CMakeFiles/ContinuousCoverage.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target ContinuousCoverage"
.PHONY : CMakeFiles/ContinuousCoverage.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ContinuousCoverage.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ContinuousCoverage.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/ContinuousCoverage.dir/rule

# Convenience name for target.
ContinuousCoverage: CMakeFiles/ContinuousCoverage.dir/rule
.PHONY : ContinuousCoverage

# clean rule for target.
CMakeFiles/ContinuousCoverage.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousCoverage.dir/build.make CMakeFiles/ContinuousCoverage.dir/clean
.PHONY : CMakeFiles/ContinuousCoverage.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ContinuousMemCheck.dir

# All Build rule for target.
CMakeFiles/ContinuousMemCheck.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousMemCheck.dir/build.make CMakeFiles/ContinuousMemCheck.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousMemCheck.dir/build.make CMakeFiles/ContinuousMemCheck.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target ContinuousMemCheck"
.PHONY : CMakeFiles/ContinuousMemCheck.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ContinuousMemCheck.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ContinuousMemCheck.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/ContinuousMemCheck.dir/rule

# Convenience name for target.
ContinuousMemCheck: CMakeFiles/ContinuousMemCheck.dir/rule
.PHONY : ContinuousMemCheck

# clean rule for target.
CMakeFiles/ContinuousMemCheck.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousMemCheck.dir/build.make CMakeFiles/ContinuousMemCheck.dir/clean
.PHONY : CMakeFiles/ContinuousMemCheck.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ContinuousSubmit.dir

# All Build rule for target.
CMakeFiles/ContinuousSubmit.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousSubmit.dir/build.make CMakeFiles/ContinuousSubmit.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousSubmit.dir/build.make CMakeFiles/ContinuousSubmit.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num= "Built target ContinuousSubmit"
.PHONY : CMakeFiles/ContinuousSubmit.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ContinuousSubmit.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ContinuousSubmit.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/ContinuousSubmit.dir/rule

# Convenience name for target.
ContinuousSubmit: CMakeFiles/ContinuousSubmit.dir/rule
.PHONY : ContinuousSubmit

# clean rule for target.
CMakeFiles/ContinuousSubmit.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ContinuousSubmit.dir/build.make CMakeFiles/ContinuousSubmit.dir/clean
.PHONY : CMakeFiles/ContinuousSubmit.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/tinyxml2.dir

# All Build rule for target.
CMakeFiles/tinyxml2.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tinyxml2.dir/build.make CMakeFiles/tinyxml2.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tinyxml2.dir/build.make CMakeFiles/tinyxml2.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num=1,2 "Built target tinyxml2"
.PHONY : CMakeFiles/tinyxml2.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/tinyxml2.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/tinyxml2.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/tinyxml2.dir/rule

# Convenience name for target.
tinyxml2: CMakeFiles/tinyxml2.dir/rule
.PHONY : tinyxml2

# clean rule for target.
CMakeFiles/tinyxml2.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tinyxml2.dir/build.make CMakeFiles/tinyxml2.dir/clean
.PHONY : CMakeFiles/tinyxml2.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/xmltest.dir

# All Build rule for target.
CMakeFiles/xmltest.dir/all: CMakeFiles/tinyxml2.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/xmltest.dir/build.make CMakeFiles/xmltest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/xmltest.dir/build.make CMakeFiles/xmltest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/project/OsmAG/tinyxml2/CMakeFiles --progress-num=3,4 "Built target xmltest"
.PHONY : CMakeFiles/xmltest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/xmltest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/xmltest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/OsmAG/tinyxml2/CMakeFiles 0
.PHONY : CMakeFiles/xmltest.dir/rule

# Convenience name for target.
xmltest: CMakeFiles/xmltest.dir/rule
.PHONY : xmltest

# clean rule for target.
CMakeFiles/xmltest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/xmltest.dir/build.make CMakeFiles/xmltest.dir/clean
.PHONY : CMakeFiles/xmltest.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system


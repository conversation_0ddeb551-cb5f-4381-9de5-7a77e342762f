# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.24

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "CMakeFiles/3.24.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.24.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.24.3/CMakeSystem.cmake"
  "CMakeLists.txt"
  "cmake/tinyxml2.pc.in"
  "tinyxml2.pc.gen"
  "/usr/local/share/cmake-3.24/Modules/BasicConfigVersion-SameMajorVersion.cmake.in"
  "/usr/local/share/cmake-3.24/Modules/CMakeCCompiler.cmake.in"
  "/usr/local/share/cmake-3.24/Modules/CMakeCCompilerABI.c"
  "/usr/local/share/cmake-3.24/Modules/CMakeCInformation.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeCXXCompiler.cmake.in"
  "/usr/local/share/cmake-3.24/Modules/CMakeCXXCompilerABI.cpp"
  "/usr/local/share/cmake-3.24/Modules/CMakeCXXInformation.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeCompilerIdDetection.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeDetermineCCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeDetermineCXXCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeDetermineCompileFeatures.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeDetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeDetermineCompilerABI.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeDetermineCompilerId.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeDetermineSystem.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeFindBinUtils.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeGenericSystem.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeInitializeConfigs.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeLanguageInformation.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakePackageConfigHelpers.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeParseLibraryArchitecture.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeSystem.cmake.in"
  "/usr/local/share/cmake-3.24/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeTestCCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeTestCXXCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeTestCompilerCommon.cmake"
  "/usr/local/share/cmake-3.24/Modules/CMakeUnixFindMake.cmake"
  "/usr/local/share/cmake-3.24/Modules/CTest.cmake"
  "/usr/local/share/cmake-3.24/Modules/CTestTargets.cmake"
  "/usr/local/share/cmake-3.24/Modules/CTestUseLaunchers.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/GNU-C.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/GNU-CXX.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/GNU-FindBinUtils.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/GNU.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/LCC-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.24/Modules/DartConfiguration.tcl.in"
  "/usr/local/share/cmake-3.24/Modules/GNUInstallDirs.cmake"
  "/usr/local/share/cmake-3.24/Modules/Internal/FeatureTesting.cmake"
  "/usr/local/share/cmake-3.24/Modules/Platform/Linux-Determine-CXX.cmake"
  "/usr/local/share/cmake-3.24/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/local/share/cmake-3.24/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/local/share/cmake-3.24/Modules/Platform/Linux-GNU.cmake"
  "/usr/local/share/cmake-3.24/Modules/Platform/Linux.cmake"
  "/usr/local/share/cmake-3.24/Modules/Platform/UnixPaths.cmake"
  "/usr/local/share/cmake-3.24/Modules/WriteBasicConfigVersionFile.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.24.3/CMakeSystem.cmake"
  "CMakeFiles/3.24.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.24.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.24.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.24.3/CMakeCXXCompiler.cmake"
  "DartConfiguration.tcl"
  "tinyxml2-config-version.cmake"
  "tinyxml2.pc.gen"
  "tinyxml2.pc"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/Experimental.dir/DependInfo.cmake"
  "CMakeFiles/Nightly.dir/DependInfo.cmake"
  "CMakeFiles/Continuous.dir/DependInfo.cmake"
  "CMakeFiles/NightlyMemoryCheck.dir/DependInfo.cmake"
  "CMakeFiles/NightlyStart.dir/DependInfo.cmake"
  "CMakeFiles/NightlyUpdate.dir/DependInfo.cmake"
  "CMakeFiles/NightlyConfigure.dir/DependInfo.cmake"
  "CMakeFiles/NightlyBuild.dir/DependInfo.cmake"
  "CMakeFiles/NightlyTest.dir/DependInfo.cmake"
  "CMakeFiles/NightlyCoverage.dir/DependInfo.cmake"
  "CMakeFiles/NightlyMemCheck.dir/DependInfo.cmake"
  "CMakeFiles/NightlySubmit.dir/DependInfo.cmake"
  "CMakeFiles/ExperimentalStart.dir/DependInfo.cmake"
  "CMakeFiles/ExperimentalUpdate.dir/DependInfo.cmake"
  "CMakeFiles/ExperimentalConfigure.dir/DependInfo.cmake"
  "CMakeFiles/ExperimentalBuild.dir/DependInfo.cmake"
  "CMakeFiles/ExperimentalTest.dir/DependInfo.cmake"
  "CMakeFiles/ExperimentalCoverage.dir/DependInfo.cmake"
  "CMakeFiles/ExperimentalMemCheck.dir/DependInfo.cmake"
  "CMakeFiles/ExperimentalSubmit.dir/DependInfo.cmake"
  "CMakeFiles/ContinuousStart.dir/DependInfo.cmake"
  "CMakeFiles/ContinuousUpdate.dir/DependInfo.cmake"
  "CMakeFiles/ContinuousConfigure.dir/DependInfo.cmake"
  "CMakeFiles/ContinuousBuild.dir/DependInfo.cmake"
  "CMakeFiles/ContinuousTest.dir/DependInfo.cmake"
  "CMakeFiles/ContinuousCoverage.dir/DependInfo.cmake"
  "CMakeFiles/ContinuousMemCheck.dir/DependInfo.cmake"
  "CMakeFiles/ContinuousSubmit.dir/DependInfo.cmake"
  "CMakeFiles/tinyxml2.dir/DependInfo.cmake"
  "CMakeFiles/xmltest.dir/DependInfo.cmake"
  )

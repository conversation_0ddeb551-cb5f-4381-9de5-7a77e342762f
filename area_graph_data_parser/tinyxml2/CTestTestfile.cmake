# CMake generated Testfile for 
# Source directory: /home/<USER>/project/OsmAG/tinyxml2
# Build directory: /home/<USER>/project/OsmAG/tinyxml2
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(xmltest "/home/<USER>/project/OsmAG/tinyxml2/xmltest")
set_tests_properties(xmltest PROPERTIES  PASS_REGULAR_EXPRESSION ", Fail 0" WORKING_DIRECTORY "/home/<USER>/project/OsmAG/tinyxml2" _BACKTRACE_TRIPLES "/home/<USER>/project/OsmAG/tinyxml2/CMakeLists.txt;48;add_test;/home/<USER>/project/OsmAG/tinyxml2/CMakeLists.txt;0;")
